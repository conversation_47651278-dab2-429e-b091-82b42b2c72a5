{"logs": [{"outputFile": "com.libretv.android.app-mergeReleaseResources-2:/values-mn/values-mn.xml", "map": [{"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\0b493fc5385f598ec7f47c0a10bc88bb\\transformed\\material3-release\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,290,402,519,618,715,829,971,1089,1228,1313,1415,1507,1605,1723,1845,1952,2094,2238,2370,2546,2672,2793,2913,3032,3125,3225,3348,3486,3585,3691,3797,3941,4086,4193,4292,4375,4470,4564,4675,4760,4844,4945,5025,5108,5207,5307,5402,5504,5591,5695,5794,5899,6030,6110,6214", "endColumns": "118,115,111,116,98,96,113,141,117,138,84,101,91,97,117,121,106,141,143,131,175,125,120,119,118,92,99,122,137,98,105,105,143,144,106,98,82,94,93,110,84,83,100,79,82,98,99,94,101,86,103,98,104,130,79,103,94", "endOffsets": "169,285,397,514,613,710,824,966,1084,1223,1308,1410,1502,1600,1718,1840,1947,2089,2233,2365,2541,2667,2788,2908,3027,3120,3220,3343,3481,3580,3686,3792,3936,4081,4188,4287,4370,4465,4559,4670,4755,4839,4940,5020,5103,5202,5302,5397,5499,5586,5690,5789,5894,6025,6105,6209,6304"}, "to": {"startLines": "102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7662,7781,7897,8009,8126,8225,8322,8436,8578,8696,8835,8920,9022,9114,9212,9330,9452,9559,9701,9845,9977,10153,10279,10400,10520,10639,10732,10832,10955,11093,11192,11298,11404,11548,11693,11800,11899,11982,12077,12171,12282,12367,12451,12552,12632,12715,12814,12914,13009,13111,13198,13302,13401,13506,13637,13717,13821", "endColumns": "118,115,111,116,98,96,113,141,117,138,84,101,91,97,117,121,106,141,143,131,175,125,120,119,118,92,99,122,137,98,105,105,143,144,106,98,82,94,93,110,84,83,100,79,82,98,99,94,101,86,103,98,104,130,79,103,94", "endOffsets": "7776,7892,8004,8121,8220,8317,8431,8573,8691,8830,8915,9017,9109,9207,9325,9447,9554,9696,9840,9972,10148,10274,10395,10515,10634,10727,10827,10950,11088,11187,11293,11399,11543,11688,11795,11894,11977,12072,12166,12277,12362,12446,12547,12627,12710,12809,12909,13004,13106,13193,13297,13396,13501,13632,13712,13816,13911"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\6b27351f1a96970e2ba142187de324d4\\transformed\\media3-ui-1.7.1\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,496,672,758,847,931,1023,1114,1190,1255,1344,1437,1508,1576,1637,1705,1860,2018,2172,2239,2321,2392,2472,2563,2657,2723,2788,2841,2899,2947,3008,3070,3146,3208,3272,3333,3394,3458,3510,3574,3652,3730,3788", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,85,88,83,91,90,75,64,88,92,70,67,60,67,154,157,153,66,81,70,79,90,93,65,64,52,57,47,60,61,75,61,63,60,60,63,51,63,77,77,57,69", "endOffsets": "280,491,667,753,842,926,1018,1109,1185,1250,1339,1432,1503,1571,1632,1700,1855,2013,2167,2234,2316,2387,2467,2558,2652,2718,2783,2836,2894,2942,3003,3065,3141,3203,3267,3328,3389,3453,3505,3569,3647,3725,3783,3853"}, "to": {"startLines": "2,11,15,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,546,3635,3721,3810,3894,3986,4077,4153,4218,4307,4400,4471,4539,4600,4668,4823,4981,5135,5202,5284,5355,5435,5526,5620,5686,6412,6465,6523,6571,6632,6694,6770,6832,6896,6957,7018,7082,7134,7198,7276,7354,7412", "endLines": "10,14,18,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99", "endColumns": "17,12,12,85,88,83,91,90,75,64,88,92,70,67,60,67,154,157,153,66,81,70,79,90,93,65,64,52,57,47,60,61,75,61,63,60,60,63,51,63,77,77,57,69", "endOffsets": "330,541,717,3716,3805,3889,3981,4072,4148,4213,4302,4395,4466,4534,4595,4663,4818,4976,5130,5197,5279,5350,5430,5521,5615,5681,5746,6460,6518,6566,6627,6689,6765,6827,6891,6952,7013,7077,7129,7193,7271,7349,7407,7477"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\4c56c0fb8a9fdb1c153785f7a46f8077\\transformed\\media3-session-1.7.1\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,137,239,319,422,528,603,685,778,873,974,1040,1139,1240,1340,1423,1519,1600,1700,1774,1845,1926,2007,2103", "endColumns": "81,101,79,102,105,74,81,92,94,100,65,98,100,99,82,95,80,99,73,70,80,80,95,95", "endOffsets": "132,234,314,417,523,598,680,773,868,969,1035,1134,1235,1335,1418,1514,1595,1695,1769,1840,1921,2002,2098,2194"}, "to": {"startLines": "30,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,159,160,161,162,163,164", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1811,2072,2174,2254,2357,2463,2538,2620,2713,2808,2909,2975,3074,3175,3275,3358,3454,3535,13916,13990,14061,14142,14223,14319", "endColumns": "81,101,79,102,105,74,81,92,94,100,65,98,100,99,82,95,80,99,73,70,80,80,95,95", "endOffsets": "1888,2169,2249,2352,2458,2533,2615,2708,2803,2904,2970,3069,3170,3270,3353,3449,3530,3630,13985,14056,14137,14218,14314,14410"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\955c05c442e591726e4833ad1e0fc303\\transformed\\core-1.16.0\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,454,559,671,790", "endColumns": "97,101,100,97,104,111,118,100", "endOffsets": "148,250,351,449,554,666,785,886"}, "to": {"startLines": "20,21,22,23,24,25,26,174", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "806,904,1006,1107,1205,1310,1422,15147", "endColumns": "97,101,100,97,104,111,118,100", "endOffsets": "899,1001,1102,1200,1305,1417,1536,15243"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\2425a4573e4fb65053b44e46c3806d87\\transformed\\foundation-release\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,139,225", "endColumns": "83,85,88", "endOffsets": "134,220,309"}, "to": {"startLines": "19,178,179", "startColumns": "4,4,4", "startOffsets": "722,15508,15594", "endColumns": "83,85,88", "endOffsets": "801,15589,15678"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\26497276d619c831fa389bb2f1588ec8\\transformed\\media3-exoplayer-1.7.1\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,179,244,308,382,456,547,635", "endColumns": "64,58,64,63,73,73,90,87,80", "endOffsets": "115,174,239,303,377,451,542,630,711"}, "to": {"startLines": "74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "5751,5816,5875,5940,6004,6078,6152,6243,6331", "endColumns": "64,58,64,63,73,73,90,87,80", "endOffsets": "5811,5870,5935,5999,6073,6147,6238,6326,6407"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\406d1819cde1682167f3af9259cf68c0\\transformed\\ui-release\\res\\values-mn\\values-mn.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "190,282,368,460,556,639,725,819,906,987,1070,1153,1226,1317,1394,1474,1551,1628,1694", "endColumns": "91,85,91,95,82,85,93,86,80,82,82,72,90,76,79,76,76,65,116", "endOffsets": "277,363,455,551,634,720,814,901,982,1065,1148,1221,1312,1389,1469,1546,1623,1689,1806"}, "to": {"startLines": "27,28,29,31,32,100,101,165,166,167,168,169,170,171,172,173,175,176,177", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1541,1633,1719,1893,1989,7482,7568,14415,14502,14583,14666,14749,14822,14913,14990,15070,15248,15325,15391", "endColumns": "91,85,91,95,82,85,93,86,80,82,82,72,90,76,79,76,76,65,116", "endOffsets": "1628,1714,1806,1984,2067,7563,7657,14497,14578,14661,14744,14817,14908,14985,15065,15142,15320,15386,15503"}}]}]}