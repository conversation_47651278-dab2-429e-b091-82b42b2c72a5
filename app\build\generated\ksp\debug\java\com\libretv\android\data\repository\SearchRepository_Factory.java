package com.libretv.android.data.repository;

import com.libretv.android.data.database.SearchHistoryDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class SearchRepository_Factory implements Factory<SearchRepository> {
  private final Provider<SearchHistoryDao> searchHistoryDaoProvider;

  public SearchRepository_Factory(Provider<SearchHistoryDao> searchHistoryDaoProvider) {
    this.searchHistoryDaoProvider = searchHistoryDaoProvider;
  }

  @Override
  public SearchRepository get() {
    return newInstance(searchHistoryDaoProvider.get());
  }

  public static SearchRepository_Factory create(
      Provider<SearchHistoryDao> searchHistoryDaoProvider) {
    return new SearchRepository_Factory(searchHistoryDaoProvider);
  }

  public static SearchRepository newInstance(SearchHistoryDao searchHistoryDao) {
    return new SearchRepository(searchHistoryDao);
  }
}
