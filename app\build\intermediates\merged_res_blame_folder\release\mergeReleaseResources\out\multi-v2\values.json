{"logs": [{"outputFile": "com.libretv.android.app-mergeReleaseResources-2:/values/values.xml", "map": [{"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\2425a4573e4fb65053b44e46c3806d87\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,101,157", "endColumns": "45,55,54", "endOffsets": "96,152,207"}, "to": {"startLines": "382,547,548", "startColumns": "4,4,4", "startOffsets": "22354,34557,34613", "endColumns": "45,55,54", "endOffsets": "22395,34608,34663"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\ecf6ab9b3b3e13c2912b30b1e461ce38\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "360", "startColumns": "4", "startOffsets": "20909", "endColumns": "42", "endOffsets": "20947"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\e8e3d02eb8e4113eee298889264c6369\\transformed\\activity-1.10.1\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "339,361", "startColumns": "4,4", "startOffsets": "19786,20952", "endColumns": "41,59", "endOffsets": "19823,21007"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\09378dcba741b68faa4989a5c91d6bd5\\transformed\\recyclerview-1.3.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,111,170,218,274,349,425,497,563", "endLines": "2,3,4,5,6,7,8,9,30", "endColumns": "55,58,47,55,74,75,71,65,24", "endOffsets": "106,165,213,269,344,420,492,558,1398"}, "to": {"startLines": "48,171,172,173,174,175,176,333,1136", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "2078,8798,8857,8905,8961,9036,9112,19468,60984", "endLines": "48,171,172,173,174,175,176,333,1156", "endColumns": "55,58,47,55,74,75,71,65,24", "endOffsets": "2129,8852,8900,8956,9031,9107,9179,19529,61819"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\abbd8e9524afba48716f5430cec85e10\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}, "to": {"startLines": "329", "startColumns": "4", "startOffsets": "19242", "endColumns": "65", "endOffsets": "19303"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\406d1819cde1682167f3af9259cf68c0\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "34,35,36,37,38,39,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,63,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2060,2134,2192,2247,2298,2353,2452,2517,2571,2637,2738,2796,2848,2908,2970,3024,3074,3128,3174,3228,3274,3316,3356,3403,3439,3529,3641,3752", "endLines": "34,35,36,37,38,39,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,62,65,70", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,53,45,41,39,46,35,89,12,12,12", "endOffsets": "2129,2187,2242,2293,2348,2401,2512,2566,2632,2733,2791,2843,2903,2965,3019,3069,3123,3169,3223,3269,3311,3351,3398,3434,3524,3636,3747,4004"}, "to": {"startLines": "281,283,284,330,331,365,390,391,392,394,395,465,466,534,535,536,537,538,539,540,541,542,544,545,546,556,738,741", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "17030,17154,17212,19308,19359,21180,22911,22976,23030,23170,23271,28838,28890,33865,33927,33981,34031,34085,34131,34185,34231,34273,34384,34431,34467,35164,45556,45667", "endLines": "281,283,284,330,331,365,390,391,392,394,395,465,466,534,535,536,537,538,539,540,541,542,544,545,546,558,740,745", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,53,45,41,39,46,35,89,12,12,12", "endOffsets": "17099,17207,17262,19354,19409,21228,22971,23025,23091,23266,23324,28885,28945,33922,33976,34026,34080,34126,34180,34226,34268,34308,34426,34462,34552,35271,45662,45919"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\955c05c442e591726e4833ad1e0fc303\\transformed\\core-1.16.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,100,101,105,106,107,108,114,124,159,180,213", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4484,4542,4603,4666,4723,4774,4832,4882,4943,5000,5066,5100,5135,5170,5240,5307,5379,5448,5517,5591,5663,5751,5822,5939,6140,6250,6451,6580,6652,6719,6922,7223,9029,9710,10392", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,99,100,104,105,106,107,113,123,158,179,212,218", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4479,4537,4598,4661,4718,4769,4827,4877,4938,4995,5061,5095,5130,5165,5235,5302,5374,5443,5512,5586,5658,5746,5817,5934,6135,6245,6446,6575,6647,6714,6917,7218,9024,9705,10387,10554"}, "to": {"startLines": "39,106,107,109,110,118,119,129,130,131,132,133,134,135,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,334,335,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,371,383,384,385,386,387,388,389,543,746,747,752,755,760,789,790,833,839,865,900,930,963", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1602,4844,4916,5046,5111,5593,5662,6231,6301,6369,6441,6511,6572,6646,9184,9245,9306,9368,9432,9494,9555,9623,9723,9783,9849,9922,9991,10048,10100,14907,14979,15055,15120,15179,15238,15298,15358,15418,15478,15538,15598,15658,15718,15778,15838,15897,15957,16017,16077,16137,16197,16257,16317,16377,16437,16497,16556,16616,16676,16735,16794,16853,16912,16971,19534,19569,19893,19948,20011,20066,20124,20180,20238,20299,20362,20419,20470,20528,20578,20639,20696,20762,20796,20831,21590,22400,22467,22539,22608,22677,22751,22823,34313,45924,46041,46308,46601,46868,48655,48727,50093,50296,51173,52979,53979,54661", "endLines": "39,106,107,109,110,118,119,129,130,131,132,133,134,135,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,334,335,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,371,383,384,385,386,387,388,389,543,746,750,752,758,760,789,790,838,848,899,920,962,968", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "1657,4911,4999,5106,5172,5657,5720,6296,6364,6436,6506,6567,6641,6714,9240,9301,9363,9427,9489,9550,9618,9718,9778,9844,9917,9986,10043,10095,10157,14974,15050,15115,15174,15233,15293,15353,15413,15473,15533,15593,15653,15713,15773,15833,15892,15952,16012,16072,16132,16192,16252,16312,16372,16432,16492,16551,16611,16671,16730,16789,16848,16907,16966,17025,19564,19599,19943,20006,20061,20119,20175,20233,20294,20357,20414,20465,20523,20573,20634,20691,20757,20791,20826,20861,21655,22462,22534,22603,22672,22746,22818,22906,34379,46036,46237,46413,46797,46992,48722,48789,50291,50592,52974,53655,54656,54823"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\44c4165ba6d998610d7f987a40811eff\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "363", "startColumns": "4", "startOffsets": "21066", "endColumns": "49", "endOffsets": "21111"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\b019d1ca88646cc0c0d931e66d340927\\transformed\\appcompat-resources-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "798,814,820,1157,1173", "startColumns": "4,4,4,4,4", "startOffsets": "49098,49523,49701,61824,62235", "endLines": "813,819,829,1172,1176", "endColumns": "24,24,24,24,24", "endOffsets": "49518,49696,49980,62230,62357"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\0b493fc5385f598ec7f47c0a10bc88bb\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,15,16,17,18,19,20,21,22,23,24,25,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,173,261,347,428,512,581,646,729,835,921,1041,1095,1164,1225,1294,1383,1478,1552,1649,1742,1840,1989,2080,2168,2264,2362,2426,2494,2581,2675,2742,2814,2886,2987,3096,3172,3241,3289,3355,3419,3493,3550,3607,3679,3729,3783,3854,3925,3995,4064,4122,4198,4269,4343,4429,4479,4549,4614,5329", "endLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,73,76", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "168,256,342,423,507,576,641,724,830,916,1036,1090,1159,1220,1289,1378,1473,1547,1644,1737,1835,1984,2075,2163,2259,2357,2421,2489,2576,2670,2737,2809,2881,2982,3091,3167,3236,3284,3350,3414,3488,3545,3602,3674,3724,3778,3849,3920,3990,4059,4117,4193,4264,4338,4424,4474,4544,4609,5324,5477"}, "to": {"startLines": "370,467,468,469,470,471,472,473,474,475,476,479,480,481,482,483,484,485,486,487,488,489,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,559,569", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "21517,28950,29038,29124,29205,29289,29358,29423,29506,29612,29698,29818,29872,29941,30002,30071,30160,30255,30329,30426,30519,30617,30766,30857,30945,31041,31139,31203,31271,31358,31452,31519,31591,31663,31764,31873,31949,32018,32066,32132,32196,32270,32327,32384,32456,32506,32560,32631,32702,32772,32841,32899,32975,33046,33120,33206,33256,33326,35276,35991", "endLines": "370,467,468,469,470,471,472,473,474,475,478,479,480,481,482,483,484,485,486,487,488,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,568,571", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "21585,29033,29119,29200,29284,29353,29418,29501,29607,29693,29813,29867,29936,29997,30066,30155,30250,30324,30421,30514,30612,30761,30852,30940,31036,31134,31198,31266,31353,31447,31514,31586,31658,31759,31868,31944,32013,32061,32127,32191,32265,32322,32379,32451,32501,32555,32626,32697,32767,32836,32894,32970,33041,33115,33201,33251,33321,33386,35986,36139"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\dab4f5d0853d53eccf431f943f79567f\\transformed\\navigation-common-release\\res\\values\\values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "995,1008,1014,1020,1029", "startColumns": "4,4,4,4,4", "startOffsets": "55773,56412,56656,56903,57266", "endLines": "1007,1013,1019,1022,1033", "endColumns": "24,24,24,24,24", "endOffsets": "56407,56651,56898,57031,57443"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\6b27351f1a96970e2ba142187de324d4\\transformed\\media3-ui-1.7.1\\res\\values\\values.xml", "from": {"startLines": "2,11,12,13,14,19,20,21,25,26,27,28,29,30,31,32,33,34,38,39,40,41,42,43,44,45,46,47,48,49,54,61,62,63,64,65,66,67,72,73,74,75,76,77,78,79,80,81,82,83,84,85,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,235,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,287,291,295,299,303,307,311,315,316,322,333,337,341,345,349,353,357,361,365,369,373,377,390,395,400,405,418,426,436,440,444,448,451,467,493,538", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,385,439,493,656,702,751,877,926,975,1034,1088,1143,1203,1262,1314,1364,1492,1557,1605,1654,1702,1759,1806,1861,1913,1967,2021,2075,2223,2461,2511,2560,2621,2681,2737,2797,2967,3027,3080,3137,3192,3248,3305,3354,3405,3460,3514,3573,3629,3684,3971,4036,4094,4143,4191,4242,4288,4345,4402,4464,4531,4603,4647,4704,4760,4823,4896,4966,5025,5082,5129,5184,5229,5278,5333,5387,5437,5488,5542,5601,5651,5709,5765,5818,5881,5946,6009,6061,6121,6185,6251,6309,6381,6442,6512,6582,6647,6712,6783,6878,6983,7086,7167,7250,7331,7420,7513,7606,7699,7784,7879,7972,8049,8141,8219,8299,8377,8463,8545,8638,8716,8807,8888,8977,9080,9181,9265,9361,9458,9553,9646,9738,9831,9924,10017,10100,10187,10282,10375,10477,10569,10650,10745,10838,10915,10959,11000,11045,11093,11137,11180,11229,11276,11320,11376,11429,11471,11518,11566,11626,11664,11714,11758,11797,11847,11899,11937,11984,12031,12072,12111,12149,12193,12241,12283,12321,12363,12417,12464,12501,12550,12592,12633,12674,12716,12759,12797,12833,12911,12989,13286,13556,13638,13720,13862,13940,14027,14112,14179,14242,14334,14426,14491,14554,14616,14687,14797,14908,15018,15085,15165,15236,15303,15388,15473,15536,15624,15688,15830,15930,15978,16121,16184,16246,16311,16382,16440,16498,16564,16616,16678,16754,16830,16884,16997,17276,17507,17717,17930,18140,18362,18578,18782,18820,19174,19961,20202,20442,20699,20952,21205,21440,21687,21926,22170,22391,22586,23258,23549,23845,24148,24814,25348,25822,26033,26233,26409,26517,27093,28038,29633", "endLines": "10,11,12,13,18,19,20,24,25,26,27,28,29,30,31,32,33,37,38,39,40,41,42,43,44,45,46,47,48,53,60,61,62,63,64,65,66,71,72,73,74,75,76,77,78,79,80,81,82,83,84,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,234,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,286,290,294,298,302,306,310,314,315,321,332,336,340,344,348,352,356,360,364,368,372,376,389,394,399,404,417,425,435,439,443,447,450,466,492,537,594", "endColumns": "17,49,53,53,9,45,48,9,48,48,58,53,54,59,58,51,49,9,64,47,48,47,56,46,54,51,53,53,53,9,9,49,48,60,59,55,59,9,59,52,56,54,55,56,48,50,54,53,58,55,54,9,64,57,48,47,50,45,56,56,61,66,71,43,56,55,62,72,69,58,56,46,54,44,48,54,53,49,50,53,58,49,57,55,52,62,64,62,51,59,63,65,57,71,60,69,69,64,64,70,94,104,102,80,82,80,88,92,92,92,84,94,92,76,91,77,79,77,85,81,92,77,90,80,88,102,100,83,95,96,94,92,91,92,92,92,82,86,94,92,101,91,80,94,92,76,43,40,44,47,43,42,48,46,43,55,52,41,46,47,59,37,49,43,38,49,51,37,46,46,40,38,37,43,47,41,37,41,53,46,36,48,41,40,40,41,42,37,35,77,77,12,12,81,81,141,77,86,84,66,62,91,91,64,62,61,70,109,110,109,66,79,70,66,84,84,62,87,63,141,99,47,142,62,61,64,70,57,57,65,51,61,75,75,53,112,10,10,10,10,10,10,10,10,37,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,22,22,22,22,22", "endOffsets": "330,380,434,488,651,697,746,872,921,970,1029,1083,1138,1198,1257,1309,1359,1487,1552,1600,1649,1697,1754,1801,1856,1908,1962,2016,2070,2218,2456,2506,2555,2616,2676,2732,2792,2962,3022,3075,3132,3187,3243,3300,3349,3400,3455,3509,3568,3624,3679,3966,4031,4089,4138,4186,4237,4283,4340,4397,4459,4526,4598,4642,4699,4755,4818,4891,4961,5020,5077,5124,5179,5224,5273,5328,5382,5432,5483,5537,5596,5646,5704,5760,5813,5876,5941,6004,6056,6116,6180,6246,6304,6376,6437,6507,6577,6642,6707,6778,6873,6978,7081,7162,7245,7326,7415,7508,7601,7694,7779,7874,7967,8044,8136,8214,8294,8372,8458,8540,8633,8711,8802,8883,8972,9075,9176,9260,9356,9453,9548,9641,9733,9826,9919,10012,10095,10182,10277,10370,10472,10564,10645,10740,10833,10910,10954,10995,11040,11088,11132,11175,11224,11271,11315,11371,11424,11466,11513,11561,11621,11659,11709,11753,11792,11842,11894,11932,11979,12026,12067,12106,12144,12188,12236,12278,12316,12358,12412,12459,12496,12545,12587,12628,12669,12711,12754,12792,12828,12906,12984,13281,13551,13633,13715,13857,13935,14022,14107,14174,14237,14329,14421,14486,14549,14611,14682,14792,14903,15013,15080,15160,15231,15298,15383,15468,15531,15619,15683,15825,15925,15973,16116,16179,16241,16306,16377,16435,16493,16559,16611,16673,16749,16825,16879,16992,17271,17502,17712,17925,18135,18357,18573,18777,18815,19169,19956,20197,20437,20694,20947,21200,21435,21682,21921,22165,22386,22581,23253,23544,23840,24143,24809,25343,25817,26028,26228,26404,26512,27088,28033,29628,31569"}, "to": {"startLines": "2,11,12,13,14,19,20,21,25,26,27,28,29,30,31,32,33,34,38,40,41,42,43,44,45,47,49,50,51,52,57,64,65,66,67,68,69,70,75,76,77,78,79,80,81,82,83,84,85,87,88,89,96,97,98,99,100,101,111,112,113,114,115,116,117,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,368,369,372,376,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,572,577,581,585,589,593,597,601,605,606,612,623,627,631,635,639,643,647,651,655,659,663,667,680,685,690,695,708,716,726,730,734,830,849,969,1034,1079", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,380,430,484,538,701,747,796,922,971,1020,1079,1133,1188,1248,1307,1359,1409,1537,1662,1710,1759,1807,1864,1911,2026,2134,2188,2242,2296,2444,2682,2732,2781,2842,2902,2958,3018,3188,3248,3301,3358,3413,3469,3526,3575,3626,3681,3735,3853,3909,3964,4251,4316,4374,4423,4471,4522,5177,5234,5291,5353,5420,5492,5536,6719,6775,6838,6911,6981,7040,7097,7144,7199,7244,7293,7348,7402,7452,7503,7557,7616,7666,7724,7780,7833,7896,7961,8024,8076,8136,8200,8266,8324,8396,8457,8527,8597,8662,8727,10676,10771,10876,10979,11060,11143,11224,11313,11406,11499,11592,11677,11772,11865,11942,12034,12112,12192,12270,12356,12438,12531,12609,12700,12781,12870,12973,13074,13158,13254,13351,13446,13539,13631,13724,13817,13910,13993,14080,14175,14268,14370,14462,14543,14638,14731,17267,17311,17352,17397,17445,17489,17532,17581,17628,17672,17728,17781,17823,17870,17918,17978,18016,18066,18110,18149,18199,18251,18289,18336,18383,18424,18463,18501,18545,18593,18635,18673,18715,18769,18816,18853,18902,18944,18985,19026,19068,19111,19149,21361,21439,21660,21957,24751,24833,24915,25057,25135,25222,25307,25374,25437,25529,25621,25686,25749,25811,25882,25992,26103,26213,26280,26360,26431,26498,26583,26668,26731,26819,27529,27671,27771,27819,27962,28025,28087,28152,28223,28281,28339,28405,28457,28519,28595,28671,28725,36144,36423,36654,36864,37077,37287,37509,37725,37929,37967,38321,39108,39349,39589,39846,40099,40352,40587,40834,41073,41317,41538,41733,42405,42696,42992,43295,43961,44495,44969,45180,45380,49985,50597,54828,57448,59043", "endLines": "10,11,12,13,18,19,20,24,25,26,27,28,29,30,31,32,33,37,38,40,41,42,43,44,45,47,49,50,51,56,63,64,65,66,67,68,69,74,75,76,77,78,79,80,81,82,83,84,85,87,88,95,96,97,98,99,100,101,111,112,113,114,115,116,117,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,368,369,375,379,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,576,580,584,588,592,596,600,604,605,611,622,626,630,634,638,642,646,650,654,658,662,666,679,684,689,694,707,715,725,729,733,737,832,864,994,1078,1135", "endColumns": "17,49,53,53,9,45,48,9,48,48,58,53,54,59,58,51,49,9,64,47,48,47,56,46,54,51,53,53,53,9,9,49,48,60,59,55,59,9,59,52,56,54,55,56,48,50,54,53,58,55,54,9,64,57,48,47,50,45,56,56,61,66,71,43,56,55,62,72,69,58,56,46,54,44,48,54,53,49,50,53,58,49,57,55,52,62,64,62,51,59,63,65,57,71,60,69,69,64,64,70,94,104,102,80,82,80,88,92,92,92,84,94,92,76,91,77,79,77,85,81,92,77,90,80,88,102,100,83,95,96,94,92,91,92,92,92,82,86,94,92,101,91,80,94,92,76,43,40,44,47,43,42,48,46,43,55,52,41,46,47,59,37,49,43,38,49,51,37,46,46,40,38,37,43,47,41,37,41,53,46,36,48,41,40,40,41,42,37,35,77,77,12,12,81,81,141,77,86,84,66,62,91,91,64,62,61,70,109,110,109,66,79,70,66,84,84,62,87,63,141,99,47,142,62,61,64,70,57,57,65,51,61,75,75,53,112,10,10,10,10,10,10,10,10,37,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,22,22,22,22,22", "endOffsets": "375,425,479,533,696,742,791,917,966,1015,1074,1128,1183,1243,1302,1354,1404,1532,1597,1705,1754,1802,1859,1906,1961,2073,2183,2237,2291,2439,2677,2727,2776,2837,2897,2953,3013,3183,3243,3296,3353,3408,3464,3521,3570,3621,3676,3730,3789,3904,3959,4246,4311,4369,4418,4466,4517,4563,5229,5286,5348,5415,5487,5531,5588,6770,6833,6906,6976,7035,7092,7139,7194,7239,7288,7343,7397,7447,7498,7552,7611,7661,7719,7775,7828,7891,7956,8019,8071,8131,8195,8261,8319,8391,8452,8522,8592,8657,8722,8793,10766,10871,10974,11055,11138,11219,11308,11401,11494,11587,11672,11767,11860,11937,12029,12107,12187,12265,12351,12433,12526,12604,12695,12776,12865,12968,13069,13153,13249,13346,13441,13534,13626,13719,13812,13905,13988,14075,14170,14263,14365,14457,14538,14633,14726,14803,17306,17347,17392,17440,17484,17527,17576,17623,17667,17723,17776,17818,17865,17913,17973,18011,18061,18105,18144,18194,18246,18284,18331,18378,18419,18458,18496,18540,18588,18630,18668,18710,18764,18811,18848,18897,18939,18980,19021,19063,19106,19144,19180,21434,21512,21952,22222,24828,24910,25052,25130,25217,25302,25369,25432,25524,25616,25681,25744,25806,25877,25987,26098,26208,26275,26355,26426,26493,26578,26663,26726,26814,26878,27666,27766,27814,27957,28020,28082,28147,28218,28276,28334,28400,28452,28514,28590,28666,28720,28833,36418,36649,36859,37072,37282,37504,37720,37924,37962,38316,39103,39344,39584,39841,40094,40347,40582,40829,41068,41312,41533,41728,42400,42691,42987,43290,43956,44490,44964,45175,45375,45551,50088,51168,55768,59038,60979"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\fe28f0070187b5cca4efcaab56677fb2\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "332,338", "startColumns": "4,4", "startOffsets": "19414,19719", "endColumns": "53,66", "endOffsets": "19463,19781"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\26497276d619c831fa389bb2f1588ec8\\transformed\\media3-exoplayer-1.7.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,187,252,316,393,458,548,632", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "120,182,247,311,388,453,543,627,696"}, "to": {"startLines": "439,440,441,442,443,444,445,446,447", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "26883,26953,27015,27080,27144,27221,27286,27376,27460", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "26948,27010,27075,27139,27216,27281,27371,27455,27524"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\fc417f0dcf28f5cd08e849c99534415e\\transformed\\fragment-1.5.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "328,340,364,921,926", "startColumns": "4,4,4,4,4", "startOffsets": "19185,19828,21116,53660,53830", "endLines": "328,340,364,925,929", "endColumns": "56,64,63,24,24", "endOffsets": "19237,19888,21175,53825,53974"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\7fcb9610de1d6578fc689f0d62ec1e8a\\transformed\\savedstate-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "362", "startColumns": "4", "startOffsets": "21012", "endColumns": "53", "endOffsets": "21061"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\ae738502719aa702fcbb63f9de86724f\\transformed\\coil-base-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "282", "startColumns": "4", "startOffsets": "17104", "endColumns": "49", "endOffsets": "17149"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\61e78a9aa048918f3a91576673b36af5\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "380", "startColumns": "4", "startOffsets": "22227", "endColumns": "82", "endOffsets": "22305"}}, {"source": "C:\\Users\\<USER>\\StudioProject\\shipin\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "1", "startColumns": "4", "startOffsets": "16", "endColumns": "43", "endOffsets": "55"}, "to": {"startLines": "381", "startColumns": "4", "startOffsets": "22310", "endColumns": "43", "endOffsets": "22349"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\4c56c0fb8a9fdb1c153785f7a46f8077\\transformed\\media3-session-1.7.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,144,215,288,387,449,509,583,672,745,841,936,1019,1103,1184,1269,1349,1413,1507,1586,1680,1754,1845,1917,2005,2073,2139,2215,2297,2384,2479,2545,2667,2728,2794", "endColumns": "88,70,72,98,61,59,73,88,72,95,94,82,83,80,84,79,63,93,78,93,73,90,71,87,67,65,75,81,86,94,65,121,60,65,66", "endOffsets": "139,210,283,382,444,504,578,667,740,836,931,1014,1098,1179,1264,1344,1408,1502,1581,1675,1749,1840,1912,2000,2068,2134,2210,2292,2379,2474,2540,2662,2723,2789,2856"}, "to": {"startLines": "120,121,125,245,336,366,393,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,528,529,530,531,532,533,751,753,754,759,761", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5725,5814,6026,14808,19604,21233,23096,23329,23418,23491,23587,23682,23765,23849,23930,24015,24095,24159,24253,24332,24426,24500,24591,24663,33391,33459,33525,33601,33683,33770,46242,46418,46540,46802,46997", "endColumns": "88,70,72,98,61,59,73,88,72,95,94,82,83,80,84,79,63,93,78,93,73,90,71,87,67,65,75,81,86,94,65,121,60,65,66", "endOffsets": "5809,5880,6094,14902,19661,21288,23165,23413,23486,23582,23677,23760,23844,23925,24010,24090,24154,24248,24327,24421,24495,24586,24658,24746,33454,33520,33596,33678,33765,33860,46303,46535,46596,46863,47059"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\80630b05ea7fb0c577cb3dfcc6eeb05c\\transformed\\core-splashscreen-1.0.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,21,23,32,45", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,174,243,315,378,450,524,600,676,753,824,893,964,1032,1113,1205,1298,1407,1528,1988,2763", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,20,22,31,44,48", "endColumns": "59,58,68,71,62,71,73,75,75,76,70,68,70,67,80,91,92,12,12,12,12,12", "endOffsets": "110,169,238,310,373,445,519,595,671,748,819,888,959,1027,1108,1200,1293,1402,1523,1983,2758,3031"}, "to": {"startLines": "46,86,102,103,104,105,192,193,194,195,196,197,198,367,549,550,551,552,554,763,772,785", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1966,3794,4568,4637,4709,4772,10162,10236,10312,10388,10465,10536,10605,21293,34668,34749,34841,34934,35043,47147,47607,48382", "endLines": "46,86,102,103,104,105,192,193,194,195,196,197,198,367,549,550,551,553,555,771,784,788", "endColumns": "59,58,68,71,62,71,73,75,75,76,70,68,70,67,80,91,92,12,12,12,12,12", "endOffsets": "2021,3848,4632,4704,4767,4839,10231,10307,10383,10460,10531,10600,10671,21356,34744,34836,34929,35038,35159,47602,48377,48650"}}, {"source": "C:\\Users\\<USER>\\StudioProject\\shipin\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "56", "endColumns": "83", "endOffsets": "135"}, "to": {"startLines": "762", "startColumns": "4", "startOffsets": "47064", "endColumns": "82", "endOffsets": "47142"}}, {"source": "C:\\Users\\<USER>\\StudioProject\\shipin\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "7,2,3,4,5,6,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "286,55,102,149,196,241,328", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "323,97,144,191,236,281,365"}, "to": {"startLines": "108,122,123,124,126,127,128", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5004,5885,5932,5979,6099,6144,6189", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "5041,5927,5974,6021,6139,6184,6226"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\1fa1c77ec5ce78b692b7b2587ffef552\\transformed\\core-viewtree-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "359", "startColumns": "4", "startOffsets": "20866", "endColumns": "42", "endOffsets": "20904"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\01dc5809f9f3fc5db6925fb2fcbfb1ca\\transformed\\navigation-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "337,791,1023,1026", "startColumns": "4,4,4,4", "startOffsets": "19666,48794,57036,57151", "endLines": "337,797,1025,1028", "endColumns": "52,24,24,24", "endOffsets": "19714,49093,57146,57261"}}]}]}