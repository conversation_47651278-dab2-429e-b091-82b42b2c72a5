package com.libretv.android.data.network

import okhttp3.Interceptor
import okhttp3.Response
import java.io.IOException

/**
 * 网络配置常量
 */
object NetworkConfig {
    
    // 默认的代理URL前缀
    const val PROXY_URL_PREFIX = "/proxy/"
    
    // 请求超时时间
    const val CONNECT_TIMEOUT = 10L
    const val READ_TIMEOUT = 30L
    const val WRITE_TIMEOUT = 30L
    
    // 用户代理
    const val USER_AGENT = "Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Mobile Safari/537.36"
    
    // 默认API源配置 (与LibreTV保持一致)
    val DEFAULT_API_SOURCES = mapOf(
        "dyttzy" to ApiSourceConfig(
            api = "http://caiji.dyttzyapi.com/api.php/provide/vod",
            name = "电影天堂资源",
            detail = "http://caiji.dyttzyapi.com"
        ),
        "ruyi" to ApiSourceConfig(
            api = "https://cj.rycjapi.com/api.php/provide/vod",
            name = "如意资源"
        ),
        "bfzy" to ApiSourceConfig(
            api = "https://bfzyapi.com/api.php/provide/vod",
            name = "暴风资源"
        ),
        "tyyszy" to ApiSourceConfig(
            api = "https://tyyszy.com/api.php/provide/vod",
            name = "天涯资源"
        ),
        "ffzy" to ApiSourceConfig(
            api = "http://ffzy5.tv/api.php/provide/vod",
            name = "非凡影视",
            detail = "http://ffzy5.tv"
        ),
        "heimuer" to ApiSourceConfig(
            api = "https://json.heimuer.xyz/api.php/provide/vod",
            name = "黑木耳",
            detail = "https://heimuer.tv"
        ),
        "zy360" to ApiSourceConfig(
            api = "https://360zy.com/api.php/provide/vod",
            name = "360资源"
        ),
        "iqiyi" to ApiSourceConfig(
            api = "https://www.iqiyizyapi.com/api.php/provide/vod",
            name = "iqiyi资源"
        ),
        "wolong" to ApiSourceConfig(
            api = "https://wolongzyw.com/api.php/provide/vod",
            name = "卧龙资源"
        ),
        "hwba" to ApiSourceConfig(
            api = "https://cjhwba.com/api.php/provide/vod",
            name = "华为吧资源"
        ),
        "jisu" to ApiSourceConfig(
            api = "https://jszyapi.com/api.php/provide/vod",
            name = "极速资源",
            detail = "https://jszyapi.com"
        ),
        "dbzy" to ApiSourceConfig(
            api = "https://dbzy.com/api.php/provide/vod",
            name = "豆瓣资源"
        ),
        "mozhua" to ApiSourceConfig(
            api = "https://mozhuazy.com/api.php/provide/vod",
            name = "魔爪资源"
        ),
        "mdzy" to ApiSourceConfig(
            api = "https://www.mdzyapi.com/api.php/provide/vod",
            name = "魔都资源"
        ),
        "zuid" to ApiSourceConfig(
            api = "https://api.zuidapi.com/api.php/provide/vod",
            name = "最大资源"
        ),
        "yinghua" to ApiSourceConfig(
            api = "https://m3u8.apiyhzy.com/api.php/provide/vod",
            name = "樱花资源"
        ),
        "baidu" to ApiSourceConfig(
            api = "https://api.apibdzy.com/api.php/provide/vod",
            name = "百度云资源"
        ),
        "wujin" to ApiSourceConfig(
            api = "https://api.wujinapi.me/api.php/provide/vod",
            name = "无尽资源"
        ),
        "wwzy" to ApiSourceConfig(
            api = "https://wwzy.tv/api.php/provide/vod",
            name = "旺旺短剧"
        ),
        "ikun" to ApiSourceConfig(
            api = "https://ikunzyapi.com/api.php/provide/vod",
            name = "iKun资源"
        )
    )
}

/**
 * API源配置
 */
data class ApiSourceConfig(
    val api: String,
    val name: String,
    val detail: String? = null,
    val adult: Boolean = false
)

/**
 * 用户代理拦截器
 */
class UserAgentInterceptor : Interceptor {
    @Throws(IOException::class)
    override fun intercept(chain: Interceptor.Chain): Response {
        val originalRequest = chain.request()
        val requestWithUserAgent = originalRequest.newBuilder()
            .header("User-Agent", NetworkConfig.USER_AGENT)
            .header("Accept", "*/*")
            .header("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8")
            .build()
        return chain.proceed(requestWithUserAgent)
    }
}

/**
 * 错误处理拦截器
 */
class ErrorHandlingInterceptor : Interceptor {
    @Throws(IOException::class)
    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()
        val response = chain.proceed(request)
        
        // 这里可以添加统一的错误处理逻辑
        if (!response.isSuccessful) {
            // 记录错误日志
            android.util.Log.w("NetworkError", "Request failed: ${request.url} - ${response.code}")
        }
        
        return response
    }
}
