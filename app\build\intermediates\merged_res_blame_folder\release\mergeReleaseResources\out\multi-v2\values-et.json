{"logs": [{"outputFile": "com.libretv.android.app-mergeReleaseResources-2:/values-et/values-et.xml", "map": [{"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\955c05c442e591726e4833ad1e0fc303\\transformed\\core-1.16.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,350,453,559,664,784", "endColumns": "94,101,97,102,105,104,119,100", "endOffsets": "145,247,345,448,554,659,779,880"}, "to": {"startLines": "20,21,22,23,24,25,26,174", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "832,927,1029,1127,1230,1336,1441,14968", "endColumns": "94,101,97,102,105,104,119,100", "endOffsets": "922,1024,1122,1225,1331,1436,1556,15064"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\406d1819cde1682167f3af9259cf68c0\\transformed\\ui-release\\res\\values-et\\values-et.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "186,279,359,455,550,632,710,801,892,976,1058,1143,1215,1301,1376,1451,1523,1600,1671", "endColumns": "92,79,95,94,81,77,90,90,83,81,84,71,85,74,74,71,76,70,121", "endOffsets": "274,354,450,545,627,705,796,887,971,1053,1138,1210,1296,1371,1446,1518,1595,1666,1788"}, "to": {"startLines": "27,28,29,31,32,100,101,165,166,167,168,169,170,171,172,173,175,176,177", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1561,1654,1734,1908,2003,7317,7395,14246,14337,14421,14503,14588,14660,14746,14821,14896,15069,15146,15217", "endColumns": "92,79,95,94,81,77,90,90,83,81,84,71,85,74,74,71,76,70,121", "endOffsets": "1649,1729,1825,1998,2080,7390,7481,14332,14416,14498,14583,14655,14741,14816,14891,14963,15141,15212,15334"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\6b27351f1a96970e2ba142187de324d4\\transformed\\media3-ui-1.7.1\\res\\values-et\\values-et.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,323,514,702,783,864,940,1031,1124,1194,1258,1342,1425,1490,1554,1617,1687,1807,1925,2044,2116,2200,2269,2338,2432,2526,2591,2657,2710,2770,2818,2879,2944,3014,3079,3145,3209,3269,3334,3386,3448,3524,3600,3655", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,80,80,75,90,92,69,63,83,82,64,63,62,69,119,117,118,71,83,68,68,93,93,64,65,52,59,47,60,64,69,64,65,63,59,64,51,61,75,75,54,66", "endOffsets": "318,509,697,778,859,935,1026,1119,1189,1253,1337,1420,1485,1549,1612,1682,1802,1920,2039,2111,2195,2264,2333,2427,2521,2586,2652,2705,2765,2813,2874,2939,3009,3074,3140,3204,3264,3329,3381,3443,3519,3595,3650,3717"}, "to": {"startLines": "2,11,15,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,373,564,3603,3684,3765,3841,3932,4025,4095,4159,4243,4326,4391,4455,4518,4588,4708,4826,4945,5017,5101,5170,5239,5333,5427,5492,6252,6305,6365,6413,6474,6539,6609,6674,6740,6804,6864,6929,6981,7043,7119,7195,7250", "endLines": "10,14,18,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99", "endColumns": "17,12,12,80,80,75,90,92,69,63,83,82,64,63,62,69,119,117,118,71,83,68,68,93,93,64,65,52,59,47,60,64,69,64,65,63,59,64,51,61,75,75,54,66", "endOffsets": "368,559,747,3679,3760,3836,3927,4020,4090,4154,4238,4321,4386,4450,4513,4583,4703,4821,4940,5012,5096,5165,5234,5328,5422,5487,5553,6300,6360,6408,6469,6534,6604,6669,6735,6799,6859,6924,6976,7038,7114,7190,7245,7312"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\4c56c0fb8a9fdb1c153785f7a46f8077\\transformed\\media3-session-1.7.1\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,133,240,311,413,505,601,688,775,869,949,1016,1112,1198,1292,1372,1475,1555,1651,1723,1794,1875,1958,2060", "endColumns": "77,106,70,101,91,95,86,86,93,79,66,95,85,93,79,102,79,95,71,70,80,82,101,104", "endOffsets": "128,235,306,408,500,596,683,770,864,944,1011,1107,1193,1287,1367,1470,1550,1646,1718,1789,1870,1953,2055,2160"}, "to": {"startLines": "30,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,159,160,161,162,163,164", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1830,2085,2192,2263,2365,2457,2553,2640,2727,2821,2901,2968,3064,3150,3244,3324,3427,3507,13732,13804,13875,13956,14039,14141", "endColumns": "77,106,70,101,91,95,86,86,93,79,66,95,85,93,79,102,79,95,71,70,80,82,101,104", "endOffsets": "1903,2187,2258,2360,2452,2548,2635,2722,2816,2896,2963,3059,3145,3239,3319,3422,3502,3598,13799,13870,13951,14034,14136,14241"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\26497276d619c831fa389bb2f1588ec8\\transformed\\media3-exoplayer-1.7.1\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,196,263,337,419,490,580,672", "endColumns": "73,66,66,73,81,70,89,91,76", "endOffsets": "124,191,258,332,414,485,575,667,744"}, "to": {"startLines": "74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "5558,5632,5699,5766,5840,5922,5993,6083,6175", "endColumns": "73,66,66,73,81,70,89,91,76", "endOffsets": "5627,5694,5761,5835,5917,5988,6078,6170,6247"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\0b493fc5385f598ec7f47c0a10bc88bb\\transformed\\material3-release\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,297,411,530,629,730,848,981,1101,1249,1336,1437,1531,1630,1746,1873,1979,2114,2247,2378,2553,2679,2798,2919,3041,3136,3233,3353,3487,3592,3695,3800,3931,4066,4174,4277,4354,4450,4546,4650,4737,4822,4928,5008,5094,5195,5299,5393,5497,5584,5693,5794,5901,6018,6098,6202", "endColumns": "119,121,113,118,98,100,117,132,119,147,86,100,93,98,115,126,105,134,132,130,174,125,118,120,121,94,96,119,133,104,102,104,130,134,107,102,76,95,95,103,86,84,105,79,85,100,103,93,103,86,108,100,106,116,79,103,98", "endOffsets": "170,292,406,525,624,725,843,976,1096,1244,1331,1432,1526,1625,1741,1868,1974,2109,2242,2373,2548,2674,2793,2914,3036,3131,3228,3348,3482,3587,3690,3795,3926,4061,4169,4272,4349,4445,4541,4645,4732,4817,4923,5003,5089,5190,5294,5388,5492,5579,5688,5789,5896,6013,6093,6197,6296"}, "to": {"startLines": "102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7486,7606,7728,7842,7961,8060,8161,8279,8412,8532,8680,8767,8868,8962,9061,9177,9304,9410,9545,9678,9809,9984,10110,10229,10350,10472,10567,10664,10784,10918,11023,11126,11231,11362,11497,11605,11708,11785,11881,11977,12081,12168,12253,12359,12439,12525,12626,12730,12824,12928,13015,13124,13225,13332,13449,13529,13633", "endColumns": "119,121,113,118,98,100,117,132,119,147,86,100,93,98,115,126,105,134,132,130,174,125,118,120,121,94,96,119,133,104,102,104,130,134,107,102,76,95,95,103,86,84,105,79,85,100,103,93,103,86,108,100,106,116,79,103,98", "endOffsets": "7601,7723,7837,7956,8055,8156,8274,8407,8527,8675,8762,8863,8957,9056,9172,9299,9405,9540,9673,9804,9979,10105,10224,10345,10467,10562,10659,10779,10913,11018,11121,11226,11357,11492,11600,11703,11780,11876,11972,12076,12163,12248,12354,12434,12520,12621,12725,12819,12923,13010,13119,13220,13327,13444,13524,13628,13727"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\2425a4573e4fb65053b44e46c3806d87\\transformed\\foundation-release\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,135,225", "endColumns": "79,89,88", "endOffsets": "130,220,309"}, "to": {"startLines": "19,178,179", "startColumns": "4,4,4", "startOffsets": "752,15339,15429", "endColumns": "79,89,88", "endOffsets": "827,15424,15513"}}]}]}