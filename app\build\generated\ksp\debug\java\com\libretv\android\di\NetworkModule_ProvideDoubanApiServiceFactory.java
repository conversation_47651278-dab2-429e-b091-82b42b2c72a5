package com.libretv.android.di;

import com.libretv.android.data.network.DoubanApiService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;
import retrofit2.Retrofit;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("com.libretv.android.di.DoubanRetrofit")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class NetworkModule_ProvideDoubanApiServiceFactory implements Factory<DoubanApiService> {
  private final Provider<Retrofit> retrofitProvider;

  public NetworkModule_ProvideDoubanApiServiceFactory(Provider<Retrofit> retrofitProvider) {
    this.retrofitProvider = retrofitProvider;
  }

  @Override
  public DoubanApiService get() {
    return provideDoubanApiService(retrofitProvider.get());
  }

  public static NetworkModule_ProvideDoubanApiServiceFactory create(
      Provider<Retrofit> retrofitProvider) {
    return new NetworkModule_ProvideDoubanApiServiceFactory(retrofitProvider);
  }

  public static DoubanApiService provideDoubanApiService(Retrofit retrofit) {
    return Preconditions.checkNotNullFromProvides(NetworkModule.INSTANCE.provideDoubanApiService(retrofit));
  }
}
