package com.libretv.android.di;

import com.libretv.android.data.database.LibreTVDatabase;
import com.libretv.android.data.database.WatchHistoryDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class DatabaseModule_ProvideWatchHistoryDaoFactory implements Factory<WatchHistoryDao> {
  private final Provider<LibreTVDatabase> databaseProvider;

  public DatabaseModule_ProvideWatchHistoryDaoFactory(Provider<LibreTVDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public WatchHistoryDao get() {
    return provideWatchHistoryDao(databaseProvider.get());
  }

  public static DatabaseModule_ProvideWatchHistoryDaoFactory create(
      Provider<LibreTVDatabase> databaseProvider) {
    return new DatabaseModule_ProvideWatchHistoryDaoFactory(databaseProvider);
  }

  public static WatchHistoryDao provideWatchHistoryDao(LibreTVDatabase database) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideWatchHistoryDao(database));
  }
}
