{"logs": [{"outputFile": "com.libretv.android.app-mergeDebugResources-2:/values-nb/values-nb.xml", "map": [{"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\6b27351f1a96970e2ba142187de324d4\\transformed\\media3-ui-1.7.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,482,672,758,843,921,1007,1095,1170,1234,1327,1418,1491,1558,1624,1694,1803,1913,2020,2093,2176,2252,2325,2428,2530,2594,2659,2712,2770,2818,2879,2949,3017,3083,3153,3217,3276,3340,3392,3452,3526,3600,3653", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,85,84,77,85,87,74,63,92,90,72,66,65,69,108,109,106,72,82,75,72,102,101,63,64,52,57,47,60,69,67,65,69,63,58,63,51,59,73,73,52,64", "endOffsets": "280,477,667,753,838,916,1002,1090,1165,1229,1322,1413,1486,1553,1619,1689,1798,1908,2015,2088,2171,2247,2320,2423,2525,2589,2654,2707,2765,2813,2874,2944,3012,3078,3148,3212,3271,3335,3387,3447,3521,3595,3648,3713"}, "to": {"startLines": "2,11,15,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,532,3517,3603,3688,3766,3852,3940,4015,4079,4172,4263,4336,4403,4469,4539,4648,4758,4865,4938,5021,5097,5170,5273,5375,5439,6197,6250,6308,6356,6417,6487,6555,6621,6691,6755,6814,6878,6930,6990,7064,7138,7191", "endLines": "10,14,18,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99", "endColumns": "17,12,12,85,84,77,85,87,74,63,92,90,72,66,65,69,108,109,106,72,82,75,72,102,101,63,64,52,57,47,60,69,67,65,69,63,58,63,51,59,73,73,52,64", "endOffsets": "330,527,717,3598,3683,3761,3847,3935,4010,4074,4167,4258,4331,4398,4464,4534,4643,4753,4860,4933,5016,5092,5165,5268,5370,5434,5499,6245,6303,6351,6412,6482,6550,6616,6686,6750,6809,6873,6925,6985,7059,7133,7186,7251"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\2425a4573e4fb65053b44e46c3806d87\\transformed\\foundation-release\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,130,220", "endColumns": "74,89,87", "endOffsets": "125,215,303"}, "to": {"startLines": "19,179,180", "startColumns": "4,4,4", "startOffsets": "722,15227,15317", "endColumns": "74,89,87", "endOffsets": "792,15312,15400"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\406d1819cde1682167f3af9259cf68c0\\transformed\\ui-release\\res\\values-nb\\values-nb.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "181,274,355,452,552,640,716,804,893,975,1055,1137,1207,1292,1366,1437,1507,1584,1651", "endColumns": "92,80,96,99,87,75,87,88,81,79,81,69,84,73,70,69,76,66,119", "endOffsets": "269,350,447,547,635,711,799,888,970,1050,1132,1202,1287,1361,1432,1502,1579,1646,1766"}, "to": {"startLines": "27,28,29,31,32,100,101,166,167,168,169,170,171,172,173,174,176,177,178", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1523,1616,1697,1867,1967,7256,7332,14159,14248,14330,14410,14492,14562,14647,14721,14792,14963,15040,15107", "endColumns": "92,80,96,99,87,75,87,88,81,79,81,69,84,73,70,69,76,66,119", "endOffsets": "1611,1692,1789,1962,2050,7327,7415,14243,14325,14405,14487,14557,14642,14716,14787,14857,15035,15102,15222"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\3bf0e9913a8fb17f961a2eb6334c8d97\\transformed\\material-release\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "88", "endOffsets": "139"}, "to": {"startLines": "159", "startColumns": "4", "startOffsets": "13560", "endColumns": "88", "endOffsets": "13644"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\26497276d619c831fa389bb2f1588ec8\\transformed\\media3-exoplayer-1.7.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,193,257,328,408,486,580,677", "endColumns": "75,61,63,70,79,77,93,96,70", "endOffsets": "126,188,252,323,403,481,575,672,743"}, "to": {"startLines": "74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "5504,5580,5642,5706,5777,5857,5935,6029,6126", "endColumns": "75,61,63,70,79,77,93,96,70", "endOffsets": "5575,5637,5701,5772,5852,5930,6024,6121,6192"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\4c56c0fb8a9fdb1c153785f7a46f8077\\transformed\\media3-session-1.7.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,225,290,387,486,563,647,726,818,904,969,1074,1161,1257,1331,1420,1498,1590,1666,1736,1815,1898,1996", "endColumns": "72,96,64,96,98,76,83,78,91,85,64,104,86,95,73,88,77,91,75,69,78,82,97,103", "endOffsets": "123,220,285,382,481,558,642,721,813,899,964,1069,1156,1252,1326,1415,1493,1585,1661,1731,1810,1893,1991,2095"}, "to": {"startLines": "30,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,160,161,162,163,164,165", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1794,2055,2152,2217,2314,2413,2490,2574,2653,2745,2831,2896,3001,3088,3184,3258,3347,3425,13649,13725,13795,13874,13957,14055", "endColumns": "72,96,64,96,98,76,83,78,91,85,64,104,86,95,73,88,77,91,75,69,78,82,97,103", "endOffsets": "1862,2147,2212,2309,2408,2485,2569,2648,2740,2826,2891,2996,3083,3179,3253,3342,3420,3512,13720,13790,13869,13952,14050,14154"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\0b493fc5385f598ec7f47c0a10bc88bb\\transformed\\material3-release\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,287,394,507,606,700,811,955,1077,1227,1311,1411,1500,1594,1701,1819,1924,2051,2173,2306,2473,2600,2716,2837,2958,3048,3146,3265,3396,3497,3607,3710,3844,3985,4090,4188,4268,4362,4453,4562,4646,4730,4841,4921,5005,5106,5205,5296,5396,5484,5589,5691,5796,5913,5993,6096", "endColumns": "116,114,106,112,98,93,110,143,121,149,83,99,88,93,106,117,104,126,121,132,166,126,115,120,120,89,97,118,130,100,109,102,133,140,104,97,79,93,90,108,83,83,110,79,83,100,98,90,99,87,104,101,104,116,79,102,98", "endOffsets": "167,282,389,502,601,695,806,950,1072,1222,1306,1406,1495,1589,1696,1814,1919,2046,2168,2301,2468,2595,2711,2832,2953,3043,3141,3260,3391,3492,3602,3705,3839,3980,4085,4183,4263,4357,4448,4557,4641,4725,4836,4916,5000,5101,5200,5291,5391,5479,5584,5686,5791,5908,5988,6091,6190"}, "to": {"startLines": "102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7420,7537,7652,7759,7872,7971,8065,8176,8320,8442,8592,8676,8776,8865,8959,9066,9184,9289,9416,9538,9671,9838,9965,10081,10202,10323,10413,10511,10630,10761,10862,10972,11075,11209,11350,11455,11553,11633,11727,11818,11927,12011,12095,12206,12286,12370,12471,12570,12661,12761,12849,12954,13056,13161,13278,13358,13461", "endColumns": "116,114,106,112,98,93,110,143,121,149,83,99,88,93,106,117,104,126,121,132,166,126,115,120,120,89,97,118,130,100,109,102,133,140,104,97,79,93,90,108,83,83,110,79,83,100,98,90,99,87,104,101,104,116,79,102,98", "endOffsets": "7532,7647,7754,7867,7966,8060,8171,8315,8437,8587,8671,8771,8860,8954,9061,9179,9284,9411,9533,9666,9833,9960,10076,10197,10318,10408,10506,10625,10756,10857,10967,11070,11204,11345,11450,11548,11628,11722,11813,11922,12006,12090,12201,12281,12365,12466,12565,12656,12756,12844,12949,13051,13156,13273,13353,13456,13555"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\955c05c442e591726e4833ad1e0fc303\\transformed\\core-1.16.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,447,555,661,781", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "144,246,343,442,550,656,776,877"}, "to": {"startLines": "20,21,22,23,24,25,26,175", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "797,891,993,1090,1189,1297,1403,14862", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "886,988,1085,1184,1292,1398,1518,14958"}}]}]}