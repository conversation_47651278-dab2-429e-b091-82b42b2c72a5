package com.libretv.android.presentation.screens.settings

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.libretv.android.data.preferences.SettingsManager
import com.libretv.android.data.repository.SearchRepository
import com.libretv.android.data.repository.VideoRepository
import com.libretv.android.data.repository.WatchHistoryRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 设置ViewModel
 */
@HiltViewModel
class SettingsViewModel @Inject constructor(
    private val settingsManager: SettingsManager,
    private val videoRepository: VideoRepository,
    private val searchRepository: SearchRepository,
    private val watchHistoryRepository: WatchHistoryRepository
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(SettingsUiState())
    val uiState: StateFlow<SettingsUiState> = _uiState.asStateFlow()

    init {
        // 监听设置变化并更新UI状态
        viewModelScope.launch {
            settingsManager.isAutoPlayNext.collect { autoPlayNext ->
                _uiState.value = _uiState.value.copy(autoPlayNext = autoPlayNext)
            }
        }
        viewModelScope.launch {
            settingsManager.isRememberProgress.collect { rememberProgress ->
                _uiState.value = _uiState.value.copy(rememberProgress = rememberProgress)
            }
        }
        viewModelScope.launch {
            settingsManager.isPlayerAutoFullscreen.collect { playerAutoFullscreen ->
                _uiState.value = _uiState.value.copy(playerAutoFullscreen = playerAutoFullscreen)
            }
        }
        viewModelScope.launch {
            settingsManager.isAdultContentEnabled.collect { adultContentEnabled ->
                _uiState.value = _uiState.value.copy(adultContentEnabled = adultContentEnabled)
            }
        }
        viewModelScope.launch {
            settingsManager.themeMode.collect { themeMode ->
                _uiState.value = _uiState.value.copy(themeMode = themeMode)
            }
        }
        viewModelScope.launch {
            settingsManager.gridColumns.collect { gridColumns ->
                _uiState.value = _uiState.value.copy(gridColumns = gridColumns)
            }
        }
        viewModelScope.launch {
            settingsManager.isCacheEnabled.collect { cacheEnabled ->
                _uiState.value = _uiState.value.copy(cacheEnabled = cacheEnabled)
            }
        }
        viewModelScope.launch {
            settingsManager.cacheSizeMB.collect { cacheSizeMB ->
                _uiState.value = _uiState.value.copy(cacheSizeMB = cacheSizeMB)
            }
        }
    }
    
    fun setAutoPlayNext(enabled: Boolean) {
        viewModelScope.launch {
            settingsManager.setAutoPlayNext(enabled)
        }
    }
    
    fun setRememberProgress(enabled: Boolean) {
        viewModelScope.launch {
            settingsManager.setRememberProgress(enabled)
        }
    }
    
    fun setPlayerAutoFullscreen(enabled: Boolean) {
        viewModelScope.launch {
            settingsManager.setPlayerAutoFullscreen(enabled)
        }
    }
    
    fun setAdultContentEnabled(enabled: Boolean) {
        viewModelScope.launch {
            settingsManager.setAdultContentEnabled(enabled)
        }
    }
    
    fun setThemeMode(mode: String) {
        viewModelScope.launch {
            settingsManager.setThemeMode(mode)
        }
    }
    
    fun setGridColumns(columns: Int) {
        viewModelScope.launch {
            settingsManager.setGridColumns(columns)
        }
    }
    
    fun setCacheEnabled(enabled: Boolean) {
        viewModelScope.launch {
            settingsManager.setCacheEnabled(enabled)
        }
    }
    
    fun setCacheSizeMB(sizeMB: Int) {
        viewModelScope.launch {
            settingsManager.setCacheSizeMB(sizeMB)
        }
    }
    
    fun clearCache() {
        viewModelScope.launch {
            try {
                // 清理视频缓存
                videoRepository.cleanOldCache(0) // 清理所有缓存
                
                // 清理搜索历史
                searchRepository.clearAllHistory()
                
                // 可以添加更多缓存清理逻辑
                android.util.Log.d("SettingsViewModel", "Cache cleared successfully")
                
            } catch (e: Exception) {
                android.util.Log.e("SettingsViewModel", "Failed to clear cache", e)
            }
        }
    }
    
    fun exportSettings(): String {
        // TODO: 实现设置导出功能
        return ""
    }
    
    fun importSettings(settingsJson: String) {
        // TODO: 实现设置导入功能
    }
    
    fun resetToDefault() {
        viewModelScope.launch {
            try {
                settingsManager.clearAllSettings()
                android.util.Log.d("SettingsViewModel", "Settings reset to default")
            } catch (e: Exception) {
                android.util.Log.e("SettingsViewModel", "Failed to reset settings", e)
            }
        }
    }
}

/**
 * 设置UI状态
 */
data class SettingsUiState(
    val autoPlayNext: Boolean = true,
    val rememberProgress: Boolean = true,
    val playerAutoFullscreen: Boolean = false,
    val adultContentEnabled: Boolean = false,
    val themeMode: String = "system",
    val gridColumns: Int = 2,
    val cacheEnabled: Boolean = true,
    val cacheSizeMB: Int = 500
)
