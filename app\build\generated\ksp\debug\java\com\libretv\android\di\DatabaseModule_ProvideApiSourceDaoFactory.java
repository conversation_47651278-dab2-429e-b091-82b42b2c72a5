package com.libretv.android.di;

import com.libretv.android.data.database.ApiSourceDao;
import com.libretv.android.data.database.LibreTVDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class DatabaseModule_ProvideApiSourceDaoFactory implements Factory<ApiSourceDao> {
  private final Provider<LibreTVDatabase> databaseProvider;

  public DatabaseModule_ProvideApiSourceDaoFactory(Provider<LibreTVDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public ApiSourceDao get() {
    return provideApiSourceDao(databaseProvider.get());
  }

  public static DatabaseModule_ProvideApiSourceDaoFactory create(
      Provider<LibreTVDatabase> databaseProvider) {
    return new DatabaseModule_ProvideApiSourceDaoFactory(databaseProvider);
  }

  public static ApiSourceDao provideApiSourceDao(LibreTVDatabase database) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideApiSourceDao(database));
  }
}
