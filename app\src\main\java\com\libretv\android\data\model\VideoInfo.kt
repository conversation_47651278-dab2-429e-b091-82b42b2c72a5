package com.libretv.android.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * 视频信息数据模型
 * 兼容LibreTV的苹果CMS V10 API格式
 */
@Serializable
@Entity(tableName = "videos")
data class VideoInfo(
    @PrimaryKey
    @SerialName("vod_id")
    val vodId: String,
    
    @SerialName("vod_name")
    val vodName: String,
    
    @SerialName("vod_pic")
    val vodPic: String? = null,
    
    @SerialName("vod_remarks")
    val vodRemarks: String? = null,
    
    @SerialName("vod_year")
    val vodYear: String? = null,
    
    @SerialName("vod_area")
    val vodArea: String? = null,
    
    @SerialName("vod_director")
    val vodDirector: String? = null,
    
    @SerialName("vod_actor")
    val vodActor: String? = null,
    
    @SerialName("vod_content")
    val vodContent: String? = null,
    
    @SerialName("vod_play_url")
    val vodPlayUrl: String? = null,
    
    @SerialName("type_name")
    val typeName: String? = null,
    
    // 扩展字段
    @SerialName("source_name")
    val sourceName: String? = null,
    
    @SerialName("source_code")
    val sourceCode: String? = null,
    
    @SerialName("api_url")
    val apiUrl: String? = null,
    
    // 本地字段
    val lastWatchTime: Long = 0L,
    val watchProgress: Long = 0L,
    val isFavorite: Boolean = false
)

/**
 * API响应数据模型
 */
@Serializable
data class ApiResponse<T>(
    val code: Int,
    val msg: String? = null,
    val list: List<T> = emptyList(),
    val episodes: List<String> = emptyList(),
    @SerialName("detailUrl")
    val detailUrl: String? = null,
    @SerialName("videoInfo")
    val videoInfo: VideoDetailInfo? = null
)

/**
 * 视频详情信息
 */
@Serializable
data class VideoDetailInfo(
    val title: String? = null,
    val cover: String? = null,
    val desc: String? = null,
    val type: String? = null,
    val year: String? = null,
    val area: String? = null,
    val director: String? = null,
    val actor: String? = null,
    val remarks: String? = null,
    @SerialName("source_name")
    val sourceName: String? = null,
    @SerialName("source_code")
    val sourceCode: String? = null
)

/**
 * 搜索历史记录
 */
@Entity(tableName = "search_history")
data class SearchHistory(
    @PrimaryKey
    val query: String,
    val timestamp: Long = System.currentTimeMillis()
)

/**
 * 观看历史记录
 */
@Entity(tableName = "watch_history")
data class WatchHistory(
    @PrimaryKey
    val id: String, // videoId + sourceCode
    val videoId: String,
    val videoTitle: String,
    val videoCover: String? = null,
    val sourceCode: String,
    val sourceName: String,
    val episodeIndex: Int = 0,
    val episodeTitle: String? = null,
    val watchProgress: Long = 0L, // 观看进度(毫秒)
    val totalDuration: Long = 0L, // 总时长(毫秒)
    val lastWatchTime: Long = System.currentTimeMillis(),
    val episodes: List<String> = emptyList()
)

/**
 * API源配置
 */
@Entity(tableName = "api_sources")
data class ApiSource(
    @PrimaryKey
    val code: String,
    val name: String,
    val api: String,
    val detail: String? = null,
    val isAdult: Boolean = false,
    val isEnabled: Boolean = true,
    val isCustom: Boolean = false,
    val order: Int = 0
)
