package com.libretv.android.data.repository;

import com.libretv.android.data.database.ApiSourceDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class ApiSourceRepository_Factory implements Factory<ApiSourceRepository> {
  private final Provider<ApiSourceDao> apiSourceDaoProvider;

  public ApiSourceRepository_Factory(Provider<ApiSourceDao> apiSourceDaoProvider) {
    this.apiSourceDaoProvider = apiSourceDaoProvider;
  }

  @Override
  public ApiSourceRepository get() {
    return newInstance(apiSourceDaoProvider.get());
  }

  public static ApiSourceRepository_Factory create(Provider<ApiSourceDao> apiSourceDaoProvider) {
    return new ApiSourceRepository_Factory(apiSourceDaoProvider);
  }

  public static ApiSourceRepository newInstance(ApiSourceDao apiSourceDao) {
    return new ApiSourceRepository(apiSourceDao);
  }
}
