package com.libretv.android.presentation.screens.history;

import com.libretv.android.data.repository.WatchHistoryRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class HistoryViewModel_Factory implements Factory<HistoryViewModel> {
  private final Provider<WatchHistoryRepository> watchHistoryRepositoryProvider;

  public HistoryViewModel_Factory(Provider<WatchHistoryRepository> watchHistoryRepositoryProvider) {
    this.watchHistoryRepositoryProvider = watchHistoryRepositoryProvider;
  }

  @Override
  public HistoryViewModel get() {
    return newInstance(watchHistoryRepositoryProvider.get());
  }

  public static HistoryViewModel_Factory create(
      Provider<WatchHistoryRepository> watchHistoryRepositoryProvider) {
    return new HistoryViewModel_Factory(watchHistoryRepositoryProvider);
  }

  public static HistoryViewModel newInstance(WatchHistoryRepository watchHistoryRepository) {
    return new HistoryViewModel(watchHistoryRepository);
  }
}
