package com.libretv.android.data.database

import androidx.room.*
import com.libretv.android.data.model.VideoInfo
import kotlinx.coroutines.flow.Flow

/**
 * 视频数据访问对象
 */
@Dao
interface VideoDao {
    
    @Query("SELECT * FROM videos WHERE vodId = :vodId")
    suspend fun getVideoById(vodId: String): VideoInfo?
    
    @Query("SELECT * FROM videos WHERE isFavorite = 1 ORDER BY lastWatchTime DESC")
    fun getFavoriteVideos(): Flow<List<VideoInfo>>
    
    @Query("SELECT * FROM videos WHERE vodName LIKE '%' || :query || '%' ORDER BY lastWatchTime DESC")
    suspend fun searchVideos(query: String): List<VideoInfo>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertVideo(video: VideoInfo)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertVideos(videos: List<VideoInfo>)
    
    @Update
    suspend fun updateVideo(video: VideoInfo)
    
    @Delete
    suspend fun deleteVideo(video: VideoInfo)
    
    @Query("DELETE FROM videos WHERE isFavorite = 0 AND lastWatchTime < :timestamp")
    suspend fun deleteOldVideos(timestamp: Long)
    
    @Query("UPDATE videos SET isFavorite = :isFavorite WHERE vodId = :vodId")
    suspend fun updateFavoriteStatus(vodId: String, isFavorite: Boolean)
    
    @Query("UPDATE videos SET watchProgress = :progress, lastWatchTime = :timestamp WHERE vodId = :vodId")
    suspend fun updateWatchProgress(vodId: String, progress: Long, timestamp: Long)
}
