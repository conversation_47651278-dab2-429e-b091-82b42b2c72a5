{"logs": [{"outputFile": "com.libretv.android.app-mergeDebugResources-2:/values-bs/values-bs.xml", "map": [{"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\4c56c0fb8a9fdb1c153785f7a46f8077\\transformed\\media3-session-1.7.1\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,141,247,321,425,522,625,725,813,914,1012,1092,1197,1297,1391,1470,1566,1648,1743,1816,1892,1974,2062,2160", "endColumns": "85,105,73,103,96,102,99,87,100,97,79,104,99,93,78,95,81,94,72,75,81,87,97,102", "endOffsets": "136,242,316,420,517,620,720,808,909,1007,1087,1192,1292,1386,1465,1561,1643,1738,1811,1887,1969,2057,2155,2258"}, "to": {"startLines": "32,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,162,163,164,165,166,167", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1983,2254,2360,2434,2538,2635,2738,2838,2926,3027,3125,3205,3310,3410,3504,3583,3679,3761,14286,14359,14435,14517,14605,14703", "endColumns": "85,105,73,103,96,102,99,87,100,97,79,104,99,93,78,95,81,94,72,75,81,87,97,102", "endOffsets": "2064,2355,2429,2533,2630,2733,2833,2921,3022,3120,3200,3305,3405,3499,3578,3674,3756,3851,14354,14430,14512,14600,14698,14801"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\0b493fc5385f598ec7f47c0a10bc88bb\\transformed\\material3-release\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,294,415,535,634,732,847,992,1112,1250,1335,1435,1528,1626,1743,1870,1975,2110,2244,2385,2555,2690,2813,2940,3068,3162,3260,3381,3509,3606,3709,3818,3957,4102,4211,4311,4396,4489,4584,4711,4805,4896,5005,5093,5176,5273,5377,5470,5567,5655,5763,5860,5962,6100,6190,6298", "endColumns": "118,119,120,119,98,97,114,144,119,137,84,99,92,97,116,126,104,134,133,140,169,134,122,126,127,93,97,120,127,96,102,108,138,144,108,99,84,92,94,126,93,90,108,87,82,96,103,92,96,87,107,96,101,137,89,107,98", "endOffsets": "169,289,410,530,629,727,842,987,1107,1245,1330,1430,1523,1621,1738,1865,1970,2105,2239,2380,2550,2685,2808,2935,3063,3157,3255,3376,3504,3601,3704,3813,3952,4097,4206,4306,4391,4484,4579,4706,4800,4891,5000,5088,5171,5268,5372,5465,5562,5650,5758,5855,5957,6095,6185,6293,6392"}, "to": {"startLines": "104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7849,7968,8088,8209,8329,8428,8526,8641,8786,8906,9044,9129,9229,9322,9420,9537,9664,9769,9904,10038,10179,10349,10484,10607,10734,10862,10956,11054,11175,11303,11400,11503,11612,11751,11896,12005,12105,12190,12283,12378,12505,12599,12690,12799,12887,12970,13067,13171,13264,13361,13449,13557,13654,13756,13894,13984,14092", "endColumns": "118,119,120,119,98,97,114,144,119,137,84,99,92,97,116,126,104,134,133,140,169,134,122,126,127,93,97,120,127,96,102,108,138,144,108,99,84,92,94,126,93,90,108,87,82,96,103,92,96,87,107,96,101,137,89,107,98", "endOffsets": "7963,8083,8204,8324,8423,8521,8636,8781,8901,9039,9124,9224,9317,9415,9532,9659,9764,9899,10033,10174,10344,10479,10602,10729,10857,10951,11049,11170,11298,11395,11498,11607,11746,11891,12000,12100,12185,12278,12373,12500,12594,12685,12794,12882,12965,13062,13166,13259,13356,13444,13552,13649,13751,13889,13979,14087,14186"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\3bf0e9913a8fb17f961a2eb6334c8d97\\transformed\\material-release\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "94", "endOffsets": "145"}, "to": {"startLines": "161", "startColumns": "4", "startOffsets": "14191", "endColumns": "94", "endOffsets": "14281"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\6b27351f1a96970e2ba142187de324d4\\transformed\\media3-ui-1.7.1\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,293,571,835,921,1008,1096,1194,1301,1371,1438,1534,1626,1691,1764,1827,1895,2009,2125,2241,2321,2405,2476,2547,2648,2750,2822,2892,2945,3003,3051,3112,3184,3251,3315,3386,3450,3509,3574,3626,3693,3774,3855,3911", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "17,12,12,85,86,87,97,106,69,66,95,91,64,72,62,67,113,115,115,79,83,70,70,100,101,71,69,52,57,47,60,71,66,63,70,63,58,64,51,66,80,80,55,67", "endOffsets": "288,566,830,916,1003,1091,1189,1296,1366,1433,1529,1621,1686,1759,1822,1890,2004,2120,2236,2316,2400,2471,2542,2643,2745,2817,2887,2940,2998,3046,3107,3179,3246,3310,3381,3445,3504,3569,3621,3688,3769,3850,3906,3974"}, "to": {"startLines": "2,11,16,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,343,621,3856,3942,4029,4117,4215,4322,4392,4459,4555,4647,4712,4785,4848,4916,5030,5146,5262,5342,5426,5497,5568,5669,5771,5843,6593,6646,6704,6752,6813,6885,6952,7016,7087,7151,7210,7275,7327,7394,7475,7556,7612", "endLines": "10,15,20,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101", "endColumns": "17,12,12,85,86,87,97,106,69,66,95,91,64,72,62,67,113,115,115,79,83,70,70,100,101,71,69,52,57,47,60,71,66,63,70,63,58,64,51,66,80,80,55,67", "endOffsets": "338,616,880,3937,4024,4112,4210,4317,4387,4454,4550,4642,4707,4780,4843,4911,5025,5141,5257,5337,5421,5492,5563,5664,5766,5838,5908,6641,6699,6747,6808,6880,6947,7011,7082,7146,7205,7270,7322,7389,7470,7551,7607,7675"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\955c05c442e591726e4833ad1e0fc303\\transformed\\core-1.16.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,353,457,561,663,780", "endColumns": "97,101,97,103,103,101,116,100", "endOffsets": "148,250,348,452,556,658,775,876"}, "to": {"startLines": "22,23,24,25,26,27,28,177", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "975,1073,1175,1273,1377,1481,1583,15547", "endColumns": "97,101,97,103,103,101,116,100", "endOffsets": "1068,1170,1268,1372,1476,1578,1695,15643"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\2425a4573e4fb65053b44e46c3806d87\\transformed\\foundation-release\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,145,235", "endColumns": "89,89,91", "endOffsets": "140,230,322"}, "to": {"startLines": "21,181,182", "startColumns": "4,4,4", "startOffsets": "885,15916,16006", "endColumns": "89,89,91", "endOffsets": "970,16001,16093"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\26497276d619c831fa389bb2f1588ec8\\transformed\\media3-exoplayer-1.7.1\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,191,256,329,408,481,581,662", "endColumns": "74,60,64,72,78,72,99,80,72", "endOffsets": "125,186,251,324,403,476,576,657,730"}, "to": {"startLines": "76,77,78,79,80,81,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "5913,5988,6049,6114,6187,6266,6339,6439,6520", "endColumns": "74,60,64,72,78,72,99,80,72", "endOffsets": "5983,6044,6109,6182,6261,6334,6434,6515,6588"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\406d1819cde1682167f3af9259cf68c0\\transformed\\ui-release\\res\\values-bs\\values-bs.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "196,297,385,479,578,664,741,833,925,1010,1091,1177,1250,1341,1418,1497,1574,1654,1724", "endColumns": "100,87,93,98,85,76,91,91,84,80,85,72,90,76,78,76,79,69,117", "endOffsets": "292,380,474,573,659,736,828,920,1005,1086,1172,1245,1336,1413,1492,1569,1649,1719,1837"}, "to": {"startLines": "29,30,31,33,34,102,103,168,169,170,171,172,173,174,175,176,178,179,180", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1700,1801,1889,2069,2168,7680,7757,14806,14898,14983,15064,15150,15223,15314,15391,15470,15648,15728,15798", "endColumns": "100,87,93,98,85,76,91,91,84,80,85,72,90,76,78,76,79,69,117", "endOffsets": "1796,1884,1978,2163,2249,7752,7844,14893,14978,15059,15145,15218,15309,15386,15465,15542,15723,15793,15911"}}]}]}