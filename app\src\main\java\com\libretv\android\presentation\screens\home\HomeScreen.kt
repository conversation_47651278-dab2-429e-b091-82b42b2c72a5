package com.libretv.android.presentation.screens.home

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Search
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.libretv.android.presentation.components.VideoCard
import com.libretv.android.presentation.components.DoubanMovieCard
import com.libretv.android.presentation.components.SearchBar

/**
 * 首页屏幕
 * 显示推荐内容、最近观看等
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HomeScreen(
    onNavigateToSearch: () -> Unit,
    onNavigateToVideo: (String, String) -> Unit,
    viewModel: HomeViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    
    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 搜索栏
        item {
            SearchBar(
                query = "",
                onQueryChange = { },
                onSearch = { onNavigateToSearch() },
                placeholder = "搜索电影、电视剧...",
                readOnly = true,
                modifier = Modifier.fillMaxWidth()
            ) {
                // 搜索建议内容
            }
        }
        
        // 最近观看
        if (uiState.recentWatched.isNotEmpty()) {
            item {
                SectionTitle("最近观看")
            }
            
            item {
                LazyRow(
                    horizontalArrangement = Arrangement.spacedBy(12.dp),
                    contentPadding = PaddingValues(horizontal = 4.dp)
                ) {
                    items(uiState.recentWatched) { video ->
                        VideoCard(
                            video = video,
                            onClick = { onNavigateToVideo(video.videoId, video.sourceCode) },
                            modifier = Modifier.width(160.dp)
                        )
                    }
                }
            }
        }
        
        // 豆瓣推荐
        if (uiState.doubanRecommendations.isNotEmpty()) {
            item {
                SectionTitle("豆瓣推荐")
            }
            
            item {
                LazyRow(
                    horizontalArrangement = Arrangement.spacedBy(12.dp),
                    contentPadding = PaddingValues(horizontal = 4.dp)
                ) {
                    items(uiState.doubanRecommendations) { movie ->
                        DoubanMovieCard(
                            movie = movie,
                            onClick = { 
                                // 搜索这个电影
                                viewModel.searchDoubanMovie(movie.title)
                            },
                            modifier = Modifier.width(120.dp)
                        )
                    }
                }
            }
        }
        
        // 热门分类
        item {
            SectionTitle("热门分类")
        }
        
        item {
            CategoryGrid(
                categories = listOf(
                    "电影" to "🎬",
                    "电视剧" to "📺", 
                    "动漫" to "🎭",
                    "综艺" to "🎪",
                    "纪录片" to "📹",
                    "短剧" to "🎞️"
                ),
                onCategoryClick = { category ->
                    // TODO: 导航到分类页面
                }
            )
        }
        
        // 加载状态
        if (uiState.isLoading) {
            item {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(32.dp),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator()
                }
            }
        }
        
        // 错误状态
        uiState.error?.let { error ->
            item {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.errorContainer
                    )
                ) {
                    Text(
                        text = error,
                        modifier = Modifier.padding(16.dp),
                        color = MaterialTheme.colorScheme.onErrorContainer
                    )
                }
            }
        }
    }
}

@Composable
private fun SectionTitle(title: String) {
    Text(
        text = title,
        style = MaterialTheme.typography.titleLarge,
        fontWeight = FontWeight.Bold,
        modifier = Modifier.padding(vertical = 8.dp)
    )
}

@Composable
private fun CategoryGrid(
    categories: List<Pair<String, String>>,
    onCategoryClick: (String) -> Unit
) {
    // 简单的网格布局
    Column {
        categories.chunked(3).forEach { rowCategories ->
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                rowCategories.forEach { (name, emoji) ->
                    Card(
                        onClick = { onCategoryClick(name) },
                        modifier = Modifier
                            .weight(1f)
                            .height(80.dp)
                    ) {
                        Column(
                            modifier = Modifier
                                .fillMaxSize()
                                .padding(8.dp),
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.Center
                        ) {
                            Text(
                                text = emoji,
                                style = MaterialTheme.typography.headlineMedium
                            )
                            Text(
                                text = name,
                                style = MaterialTheme.typography.bodyMedium
                            )
                        }
                    }
                }
                // 填充剩余空间
                repeat(3 - rowCategories.size) {
                    Spacer(modifier = Modifier.weight(1f))
                }
            }
            Spacer(modifier = Modifier.height(8.dp))
        }
    }
}
