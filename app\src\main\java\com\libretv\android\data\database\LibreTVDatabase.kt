package com.libretv.android.data.database

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverter
import androidx.room.TypeConverters
import android.content.Context
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.libretv.android.data.model.ApiSource
import com.libretv.android.data.model.SearchHistory
import com.libretv.android.data.model.VideoInfo
import com.libretv.android.data.model.WatchHistory

/**
 * LibreTV数据库
 */
@Database(
    entities = [
        VideoInfo::class,
        SearchHistory::class,
        WatchHistory::class,
        ApiSource::class
    ],
    version = 1,
    exportSchema = false
)
@TypeConverters(Converters::class)
abstract class LibreTVDatabase : RoomDatabase() {
    
    abstract fun videoDao(): VideoDao
    abstract fun searchHistoryDao(): SearchHistoryDao
    abstract fun watchHistoryDao(): WatchHistoryDao
    abstract fun apiSourceDao(): ApiSourceDao
    
    companion object {
        const val DATABASE_NAME = "libretv_database"
        
        fun create(context: Context): LibreTVDatabase {
            return Room.databaseBuilder(
                context.applicationContext,
                LibreTVDatabase::class.java,
                DATABASE_NAME
            )
            .fallbackToDestructiveMigration()
            .build()
        }
    }
}

/**
 * 类型转换器
 */
class Converters {
    private val gson = Gson()
    
    @TypeConverter
    fun fromStringList(value: List<String>): String {
        return gson.toJson(value)
    }
    
    @TypeConverter
    fun toStringList(value: String): List<String> {
        val listType = object : TypeToken<List<String>>() {}.type
        return gson.fromJson(value, listType) ?: emptyList()
    }
}
