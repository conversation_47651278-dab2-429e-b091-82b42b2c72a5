package com.libretv.android.di;

import com.libretv.android.data.database.LibreTVDatabase;
import com.libretv.android.data.database.VideoDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class DatabaseModule_ProvideVideoDaoFactory implements Factory<VideoDao> {
  private final Provider<LibreTVDatabase> databaseProvider;

  public DatabaseModule_ProvideVideoDaoFactory(Provider<LibreTVDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public VideoDao get() {
    return provideVideoDao(databaseProvider.get());
  }

  public static DatabaseModule_ProvideVideoDaoFactory create(
      Provider<LibreTVDatabase> databaseProvider) {
    return new DatabaseModule_ProvideVideoDaoFactory(databaseProvider);
  }

  public static VideoDao provideVideoDao(LibreTVDatabase database) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideVideoDao(database));
  }
}
