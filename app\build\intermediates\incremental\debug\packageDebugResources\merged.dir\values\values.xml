<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="black">#FF000000</color>
    <color name="error">#DC2626</color>
    <color name="libretv_background">#FEFEFE</color>
    <color name="libretv_on_background">#1C1B1F</color>
    <color name="libretv_on_primary_container">#1E1B4B</color>
    <color name="libretv_on_secondary_container">#4C1D95</color>
    <color name="libretv_on_surface">#1C1B1F</color>
    <color name="libretv_primary">#6366F1</color>
    <color name="libretv_primary_container">#E0E7FF</color>
    <color name="libretv_secondary">#8B5CF6</color>
    <color name="libretv_secondary_container">#EDE9FE</color>
    <color name="libretv_surface">#FEFEFE</color>
    <color name="on_error">#FFFFFF</color>
    <color name="on_success">#FFFFFF</color>
    <color name="on_warning">#FFFFFF</color>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="success">#059669</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="warning">#D97706</color>
    <color name="white">#FFFFFFFF</color>
    <string name="app_name">LibreTV</string>
    <string name="cancel">取消</string>
    <string name="clear">清空</string>
    <string name="delete">删除</string>
    <string name="error">错误</string>
    <string name="loading">加载中...</string>
    <string name="nav_history">历史</string>
    <string name="nav_home">首页</string>
    <string name="nav_search">搜索</string>
    <string name="nav_settings">设置</string>
    <string name="ok">确定</string>
    <string name="player_error">播放失败</string>
    <string name="player_loading">加载中...</string>
    <string name="player_retry">重试</string>
    <string name="retry">重试</string>
    <string name="search_error">搜索失败</string>
    <string name="search_hint">搜索电影、电视剧...</string>
    <string name="search_no_results">没有找到相关内容</string>
    <string name="settings_about">关于</string>
    <string name="settings_cache">缓存设置</string>
    <string name="settings_content">内容设置</string>
    <string name="settings_interface">界面设置</string>
    <string name="settings_playback">播放设置</string>
    <string name="success">成功</string>
    <style name="Theme.LibreTV" parent="android:Theme.Material.Light.NoActionBar">
        
        <item name="android:statusBarColor">@color/libretv_primary</item>
    </style>
    <style name="Theme.LibreTV.Player" parent="Theme.LibreTV">
        
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowBackground">@color/black</item>
        <item name="android:keepScreenOn">true</item>
    </style>
</resources>