package com.libretv.android.presentation

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.core.splashscreen.SplashScreen.Companion.installSplashScreen
import com.libretv.android.presentation.navigation.LibreTVNavigation
import com.libretv.android.presentation.theme.LibreTVTheme
import dagger.hilt.android.AndroidEntryPoint

/**
 * LibreTV主Activity
 * 基于LibreTV Web版本的Android实现
 */
@AndroidEntryPoint
class MainActivity : ComponentActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        // 安装启动画面
        installSplashScreen()

        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        setContent {
            LibreTVApp()
        }
    }
}

@Composable
fun LibreTVApp() {
    LibreTVTheme {
        Surface(
            modifier = Modifier.fillMaxSize(),
            color = MaterialTheme.colorScheme.background
        ) {
            LibreTVNavigation()
        }
    }
}