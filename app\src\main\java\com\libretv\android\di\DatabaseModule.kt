package com.libretv.android.di

import android.content.Context
import androidx.room.Room
import com.libretv.android.data.database.*
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 数据库依赖注入模块
 */
@Module
@InstallIn(SingletonComponent::class)
object DatabaseModule {
    
    @Provides
    @Singleton
    fun provideLibreTVDatabase(@ApplicationContext context: Context): LibreTVDatabase {
        return LibreTVDatabase.create(context)
    }
    
    @Provides
    fun provideVideoDao(database: LibreTVDatabase): VideoDao {
        return database.videoDao()
    }
    
    @Provides
    fun provideSearchHistoryDao(database: LibreTVDatabase): SearchHistoryDao {
        return database.searchHistoryDao()
    }
    
    @Provides
    fun provideWatchHistoryDao(database: LibreTVDatabase): WatchHistoryDao {
        return database.watchHistoryDao()
    }
    
    @Provides
    fun provideApiSourceDao(database: LibreTVDatabase): ApiSourceDao {
        return database.apiSourceDao()
    }
}
