1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.libretv.android.debug"
4    android:versionCode="1"
5    android:versionName="1.0.3-debug" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <!-- 网络权限 -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:6:5-67
12-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:7:5-79
13-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
14-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:8:5-76
14-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:8:22-73
15
16    <!-- 存储权限 -->
17    <uses-permission
17-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:11:5-12:38
18        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
18-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:11:22-78
19        android:maxSdkVersion="28" />
19-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:12:9-35
20    <uses-permission
20-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:13:5-14:38
21        android:name="android.permission.READ_EXTERNAL_STORAGE"
21-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:13:22-77
22        android:maxSdkVersion="32" />
22-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:14:9-35
23
24    <!-- 媒体权限 -->
25    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
25-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:17:5-18:32
25-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:17:22-72
26    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
26-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:19:5-20:32
26-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:19:22-72
27
28    <!-- 唤醒锁权限 (用于播放时保持屏幕常亮) -->
29    <uses-permission android:name="android.permission.WAKE_LOCK" />
29-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:23:5-68
29-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:23:22-65
30
31    <!-- 前台服务权限 (用于后台播放) -->
32    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
32-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:26:5-77
32-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:26:22-74
33    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />
33-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:27:5-92
33-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:27:22-89
34
35    <permission
35-->[androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\955c05c442e591726e4833ad1e0fc303\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
36        android:name="com.libretv.android.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
36-->[androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\955c05c442e591726e4833ad1e0fc303\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
37        android:protectionLevel="signature" />
37-->[androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\955c05c442e591726e4833ad1e0fc303\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
38
39    <uses-permission android:name="com.libretv.android.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
39-->[androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\955c05c442e591726e4833ad1e0fc303\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
39-->[androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\955c05c442e591726e4833ad1e0fc303\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
40
41    <application
41-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:29:5-79:19
42        android:name="com.libretv.android.LibreTVApplication"
42-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:30:9-43
43        android:allowBackup="true"
43-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:31:9-35
44        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
44-->[androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\955c05c442e591726e4833ad1e0fc303\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
45        android:dataExtractionRules="@xml/data_extraction_rules"
45-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:32:9-65
46        android:debuggable="true"
47        android:extractNativeLibs="false"
48        android:fullBackupContent="@xml/backup_rules"
48-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:33:9-54
49        android:icon="@mipmap/ic_launcher"
49-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:34:9-43
50        android:label="@string/app_name"
50-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:35:9-41
51        android:requestLegacyExternalStorage="true"
51-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:40:9-52
52        android:roundIcon="@mipmap/ic_launcher_round"
52-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:36:9-54
53        android:supportsRtl="true"
53-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:37:9-35
54        android:testOnly="true"
55        android:theme="@style/Theme.LibreTV"
55-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:38:9-45
56        android:usesCleartextTraffic="true" >
56-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:39:9-44
57
58        <!-- 主Activity -->
59        <activity
59-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:44:9-64:20
60            android:name="com.libretv.android.presentation.MainActivity"
60-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:45:13-54
61            android:exported="true"
61-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:46:13-36
62            android:label="@string/app_name"
62-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:47:13-45
63            android:launchMode="singleTop"
63-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:50:13-43
64            android:screenOrientation="portrait"
64-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:49:13-49
65            android:theme="@style/Theme.LibreTV" >
65-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:48:13-49
66            <intent-filter>
66-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:51:13-54:29
67                <action android:name="android.intent.action.MAIN" />
67-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:52:17-69
67-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:52:25-66
68
69                <category android:name="android.intent.category.LAUNCHER" />
69-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:53:17-77
69-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:53:27-74
70            </intent-filter>
71
72            <!-- 支持从浏览器打开视频链接 -->
73            <intent-filter android:autoVerify="true" >
73-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:57:13-63:29
73-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:57:28-53
74                <action android:name="android.intent.action.VIEW" />
74-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:58:17-69
74-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:58:25-66
75
76                <category android:name="android.intent.category.DEFAULT" />
76-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:59:17-76
76-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:59:27-73
77                <category android:name="android.intent.category.BROWSABLE" />
77-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:60:17-78
77-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:60:27-75
78
79                <data
79-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:61:17-96
80                    android:host="*"
80-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:61:45-61
81                    android:pathPattern=".*\\.m3u8"
81-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:61:62-93
82                    android:scheme="http" />
82-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:61:23-44
83                <data
83-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:61:17-96
84                    android:host="*"
84-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:61:45-61
85                    android:pathPattern=".*\\.m3u8"
85-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:61:62-93
86                    android:scheme="https" />
86-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:61:23-44
87            </intent-filter>
88        </activity>
89
90        <!-- 文件提供者 -->
91        <provider
92            android:name="androidx.core.content.FileProvider"
92-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:70:13-62
93            android:authorities="com.libretv.android.debug.fileprovider"
93-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:71:13-64
94            android:exported="false"
94-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:72:13-37
95            android:grantUriPermissions="true" >
95-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:73:13-47
96            <meta-data
96-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:74:13-76:54
97                android:name="android.support.FILE_PROVIDER_PATHS"
97-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:75:17-67
98                android:resource="@xml/file_paths" />
98-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:76:17-51
99        </provider>
100
101        <activity
101-->[androidx.compose.ui:ui-tooling-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\083c6a44399ed0680db45eea281a3bef\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
102            android:name="androidx.compose.ui.tooling.PreviewActivity"
102-->[androidx.compose.ui:ui-tooling-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\083c6a44399ed0680db45eea281a3bef\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
103            android:exported="true" />
103-->[androidx.compose.ui:ui-tooling-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\083c6a44399ed0680db45eea281a3bef\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
104
105        <provider
105-->[androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\9b142462af8a97f10019a2008da92615\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
106            android:name="androidx.startup.InitializationProvider"
106-->[androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\9b142462af8a97f10019a2008da92615\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
107            android:authorities="com.libretv.android.debug.androidx-startup"
107-->[androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\9b142462af8a97f10019a2008da92615\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
108            android:exported="false" >
108-->[androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\9b142462af8a97f10019a2008da92615\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
109            <meta-data
109-->[androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\9b142462af8a97f10019a2008da92615\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
110                android:name="androidx.emoji2.text.EmojiCompatInitializer"
110-->[androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\9b142462af8a97f10019a2008da92615\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
111                android:value="androidx.startup" />
111-->[androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\9b142462af8a97f10019a2008da92615\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
112            <meta-data
112-->[androidx.lifecycle:lifecycle-process:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\3707669c1e417c1f43de410084b2b4ed\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
113                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
113-->[androidx.lifecycle:lifecycle-process:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\3707669c1e417c1f43de410084b2b4ed\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
114                android:value="androidx.startup" />
114-->[androidx.lifecycle:lifecycle-process:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\3707669c1e417c1f43de410084b2b4ed\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
115            <meta-data
115-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
116                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
116-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
117                android:value="androidx.startup" />
117-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
118        </provider>
119
120        <activity
120-->[androidx.compose.ui:ui-test-manifest:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\7fc30fe9ba0c67d0c79e5cbdf2ea58c6\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:23:9-26:79
121            android:name="androidx.activity.ComponentActivity"
121-->[androidx.compose.ui:ui-test-manifest:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\7fc30fe9ba0c67d0c79e5cbdf2ea58c6\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:24:13-63
122            android:exported="true"
122-->[androidx.compose.ui:ui-test-manifest:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\7fc30fe9ba0c67d0c79e5cbdf2ea58c6\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:25:13-36
123            android:theme="@android:style/Theme.Material.Light.NoActionBar" />
123-->[androidx.compose.ui:ui-test-manifest:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\7fc30fe9ba0c67d0c79e5cbdf2ea58c6\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:26:13-76
124
125        <service
125-->[androidx.room:room-runtime-android:2.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\97e337fecb0b99825c65abd4fbf1529f\transformed\room-runtime-release\AndroidManifest.xml:24:9-28:63
126            android:name="androidx.room.MultiInstanceInvalidationService"
126-->[androidx.room:room-runtime-android:2.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\97e337fecb0b99825c65abd4fbf1529f\transformed\room-runtime-release\AndroidManifest.xml:25:13-74
127            android:directBootAware="true"
127-->[androidx.room:room-runtime-android:2.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\97e337fecb0b99825c65abd4fbf1529f\transformed\room-runtime-release\AndroidManifest.xml:26:13-43
128            android:exported="false" />
128-->[androidx.room:room-runtime-android:2.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\97e337fecb0b99825c65abd4fbf1529f\transformed\room-runtime-release\AndroidManifest.xml:27:13-37
129
130        <receiver
130-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
131            android:name="androidx.profileinstaller.ProfileInstallReceiver"
131-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
132            android:directBootAware="false"
132-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
133            android:enabled="true"
133-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
134            android:exported="true"
134-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
135            android:permission="android.permission.DUMP" >
135-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
136            <intent-filter>
136-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
137                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
137-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
137-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
138            </intent-filter>
139            <intent-filter>
139-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
140                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
140-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
140-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
141            </intent-filter>
142            <intent-filter>
142-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
143                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
143-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
143-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
144            </intent-filter>
145            <intent-filter>
145-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
146                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
146-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
146-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
147            </intent-filter>
148        </receiver>
149    </application>
150
151</manifest>
