1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.libretv.android.debug"
4    android:versionCode="1"
5    android:versionName="1.0.3-debug" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <!-- 网络权限 -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:6:5-67
12-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:7:5-79
13-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
14-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:8:5-76
14-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:8:22-73
15
16    <!-- 存储权限 -->
17    <uses-permission
17-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:11:5-12:38
18        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
18-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:11:22-78
19        android:maxSdkVersion="28" />
19-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:12:9-35
20    <uses-permission
20-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:13:5-14:38
21        android:name="android.permission.READ_EXTERNAL_STORAGE"
21-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:13:22-77
22        android:maxSdkVersion="32" />
22-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:14:9-35
23
24    <!-- 媒体权限 -->
25    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
25-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:17:5-18:32
25-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:17:22-72
26    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
26-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:19:5-20:32
26-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:19:22-72
27
28    <!-- 唤醒锁权限 (用于播放时保持屏幕常亮) -->
29    <uses-permission android:name="android.permission.WAKE_LOCK" />
29-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:23:5-68
29-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:23:22-65
30
31    <!-- 前台服务权限 (用于后台播放) -->
32    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
32-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:26:5-77
32-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:26:22-74
33    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />
33-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:27:5-92
33-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:27:22-89
34
35    <permission
35-->[androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\955c05c442e591726e4833ad1e0fc303\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
36        android:name="com.libretv.android.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
36-->[androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\955c05c442e591726e4833ad1e0fc303\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
37        android:protectionLevel="signature" />
37-->[androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\955c05c442e591726e4833ad1e0fc303\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
38
39    <uses-permission android:name="com.libretv.android.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
39-->[androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\955c05c442e591726e4833ad1e0fc303\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
39-->[androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\955c05c442e591726e4833ad1e0fc303\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
40
41    <application
41-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:29:5-96:19
42        android:name="com.libretv.android.LibreTVApplication"
42-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:30:9-43
43        android:allowBackup="true"
43-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:31:9-35
44        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
44-->[androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\955c05c442e591726e4833ad1e0fc303\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
45        android:dataExtractionRules="@xml/data_extraction_rules"
45-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:32:9-65
46        android:debuggable="true"
47        android:extractNativeLibs="false"
48        android:fullBackupContent="@xml/backup_rules"
48-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:33:9-54
49        android:icon="@mipmap/ic_launcher"
49-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:34:9-43
50        android:label="@string/app_name"
50-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:35:9-41
51        android:requestLegacyExternalStorage="true"
51-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:40:9-52
52        android:roundIcon="@mipmap/ic_launcher_round"
52-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:36:9-54
53        android:supportsRtl="true"
53-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:37:9-35
54        android:theme="@style/Theme.LibreTV"
54-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:38:9-45
55        android:usesCleartextTraffic="true" >
55-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:39:9-44
56
57        <!-- 主Activity -->
58        <activity
58-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:44:9-64:20
59            android:name="com.libretv.android.presentation.MainActivity"
59-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:45:13-54
60            android:exported="true"
60-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:46:13-36
61            android:label="@string/app_name"
61-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:47:13-45
62            android:launchMode="singleTop"
62-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:50:13-43
63            android:screenOrientation="portrait"
63-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:49:13-49
64            android:theme="@style/Theme.LibreTV" >
64-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:48:13-49
65            <intent-filter>
65-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:51:13-54:29
66                <action android:name="android.intent.action.MAIN" />
66-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:52:17-69
66-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:52:25-66
67
68                <category android:name="android.intent.category.LAUNCHER" />
68-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:53:17-77
68-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:53:27-74
69            </intent-filter>
70
71            <!-- 支持从浏览器打开视频链接 -->
72            <intent-filter android:autoVerify="true" >
72-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:57:13-63:29
72-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:57:28-53
73                <action android:name="android.intent.action.VIEW" />
73-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:58:17-69
73-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:58:25-66
74
75                <category android:name="android.intent.category.DEFAULT" />
75-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:59:17-76
75-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:59:27-73
76                <category android:name="android.intent.category.BROWSABLE" />
76-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:60:17-78
76-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:60:27-75
77
78                <data
78-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:61:17-96
79                    android:host="*"
79-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:61:45-61
80                    android:pathPattern=".*\\.m3u8"
80-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:61:62-93
81                    android:scheme="http" />
81-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:61:23-44
82                <data
82-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:61:17-96
83                    android:host="*"
83-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:61:45-61
84                    android:pathPattern=".*\\.m3u8"
84-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:61:62-93
85                    android:scheme="https" />
85-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:61:23-44
86            </intent-filter>
87        </activity>
88
89        <!-- 播放器Activity -->
90        <activity
90-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:67:9-73:46
91            android:name="com.libretv.android.presentation.player.PlayerActivity"
91-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:68:13-63
92            android:configChanges="orientation|screenSize|keyboardHidden"
92-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:72:13-74
93            android:exported="false"
93-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:69:13-37
94            android:launchMode="singleTop"
94-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:73:13-43
95            android:screenOrientation="sensorLandscape"
95-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:71:13-56
96            android:theme="@style/Theme.LibreTV.Player" />
96-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:70:13-56
97
98        <!-- 媒体播放服务 -->
99        <service
99-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:76:9-83:19
100            android:name="com.libretv.android.presentation.player.PlaybackService"
100-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:77:13-64
101            android:exported="false"
101-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:78:13-37
102            android:foregroundServiceType="mediaPlayback" >
102-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:79:13-58
103            <intent-filter>
103-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:80:13-82:29
104                <action android:name="androidx.media3.session.MediaSessionService" />
104-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:81:17-86
104-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:81:25-83
105            </intent-filter>
106        </service>
107
108        <!-- 文件提供者 -->
109        <provider
110            android:name="androidx.core.content.FileProvider"
110-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:87:13-62
111            android:authorities="com.libretv.android.debug.fileprovider"
111-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:88:13-64
112            android:exported="false"
112-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:89:13-37
113            android:grantUriPermissions="true" >
113-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:90:13-47
114            <meta-data
114-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:91:13-93:54
115                android:name="android.support.FILE_PROVIDER_PATHS"
115-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:92:17-67
116                android:resource="@xml/file_paths" />
116-->C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:93:17-51
117        </provider>
118
119        <activity
119-->[androidx.compose.ui:ui-tooling-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\083c6a44399ed0680db45eea281a3bef\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
120            android:name="androidx.compose.ui.tooling.PreviewActivity"
120-->[androidx.compose.ui:ui-tooling-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\083c6a44399ed0680db45eea281a3bef\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
121            android:exported="true" />
121-->[androidx.compose.ui:ui-tooling-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\083c6a44399ed0680db45eea281a3bef\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
122
123        <provider
123-->[androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\9b142462af8a97f10019a2008da92615\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
124            android:name="androidx.startup.InitializationProvider"
124-->[androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\9b142462af8a97f10019a2008da92615\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
125            android:authorities="com.libretv.android.debug.androidx-startup"
125-->[androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\9b142462af8a97f10019a2008da92615\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
126            android:exported="false" >
126-->[androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\9b142462af8a97f10019a2008da92615\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
127            <meta-data
127-->[androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\9b142462af8a97f10019a2008da92615\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
128                android:name="androidx.emoji2.text.EmojiCompatInitializer"
128-->[androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\9b142462af8a97f10019a2008da92615\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
129                android:value="androidx.startup" />
129-->[androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\9b142462af8a97f10019a2008da92615\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
130            <meta-data
130-->[androidx.lifecycle:lifecycle-process:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\3707669c1e417c1f43de410084b2b4ed\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
131                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
131-->[androidx.lifecycle:lifecycle-process:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\3707669c1e417c1f43de410084b2b4ed\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
132                android:value="androidx.startup" />
132-->[androidx.lifecycle:lifecycle-process:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\3707669c1e417c1f43de410084b2b4ed\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
133            <meta-data
133-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
134                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
134-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
135                android:value="androidx.startup" />
135-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
136        </provider>
137
138        <activity
138-->[androidx.compose.ui:ui-test-manifest:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\7fc30fe9ba0c67d0c79e5cbdf2ea58c6\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:23:9-26:79
139            android:name="androidx.activity.ComponentActivity"
139-->[androidx.compose.ui:ui-test-manifest:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\7fc30fe9ba0c67d0c79e5cbdf2ea58c6\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:24:13-63
140            android:exported="true"
140-->[androidx.compose.ui:ui-test-manifest:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\7fc30fe9ba0c67d0c79e5cbdf2ea58c6\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:25:13-36
141            android:theme="@android:style/Theme.Material.Light.NoActionBar" />
141-->[androidx.compose.ui:ui-test-manifest:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\7fc30fe9ba0c67d0c79e5cbdf2ea58c6\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:26:13-76
142
143        <service
143-->[androidx.room:room-runtime-android:2.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\97e337fecb0b99825c65abd4fbf1529f\transformed\room-runtime-release\AndroidManifest.xml:24:9-28:63
144            android:name="androidx.room.MultiInstanceInvalidationService"
144-->[androidx.room:room-runtime-android:2.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\97e337fecb0b99825c65abd4fbf1529f\transformed\room-runtime-release\AndroidManifest.xml:25:13-74
145            android:directBootAware="true"
145-->[androidx.room:room-runtime-android:2.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\97e337fecb0b99825c65abd4fbf1529f\transformed\room-runtime-release\AndroidManifest.xml:26:13-43
146            android:exported="false" />
146-->[androidx.room:room-runtime-android:2.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\97e337fecb0b99825c65abd4fbf1529f\transformed\room-runtime-release\AndroidManifest.xml:27:13-37
147
148        <receiver
148-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
149            android:name="androidx.profileinstaller.ProfileInstallReceiver"
149-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
150            android:directBootAware="false"
150-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
151            android:enabled="true"
151-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
152            android:exported="true"
152-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
153            android:permission="android.permission.DUMP" >
153-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
154            <intent-filter>
154-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
155                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
155-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
155-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
156            </intent-filter>
157            <intent-filter>
157-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
158                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
158-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
158-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
159            </intent-filter>
160            <intent-filter>
160-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
161                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
161-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
161-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
162            </intent-filter>
163            <intent-filter>
163-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
164                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
164-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
164-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
165            </intent-filter>
166        </receiver>
167    </application>
168
169</manifest>
