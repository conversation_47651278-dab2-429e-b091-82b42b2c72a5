package com.libretv.android.di;

import android.content.Context;
import com.libretv.android.data.database.LibreTVDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class DatabaseModule_ProvideLibreTVDatabaseFactory implements Factory<LibreTVDatabase> {
  private final Provider<Context> contextProvider;

  public DatabaseModule_ProvideLibreTVDatabaseFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public LibreTVDatabase get() {
    return provideLibreTVDatabase(contextProvider.get());
  }

  public static DatabaseModule_ProvideLibreTVDatabaseFactory create(
      Provider<Context> contextProvider) {
    return new DatabaseModule_ProvideLibreTVDatabaseFactory(contextProvider);
  }

  public static LibreTVDatabase provideLibreTVDatabase(Context context) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideLibreTVDatabase(context));
  }
}
