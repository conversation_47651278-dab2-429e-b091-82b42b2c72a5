{"logs": [{"outputFile": "com.libretv.android.app-mergeDebugResources-2:/values-sk/values-sk.xml", "map": [{"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\6b27351f1a96970e2ba142187de324d4\\transformed\\media3-ui-1.7.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,293,624,938,1019,1099,1181,1284,1383,1462,1527,1618,1712,1782,1848,1913,1990,2112,2229,2350,2424,2506,2579,2661,2761,2860,2927,2992,3045,3103,3151,3212,3284,3358,3421,3494,3559,3619,3684,3736,3800,3878,3956,4010", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63", "endColumns": "17,12,12,80,79,81,102,98,78,64,90,93,69,65,64,76,121,116,120,73,81,72,81,99,98,66,64,52,57,47,60,71,73,62,72,64,59,64,51,63,77,77,53,65", "endOffsets": "288,619,933,1014,1094,1176,1279,1378,1457,1522,1613,1707,1777,1843,1908,1985,2107,2224,2345,2419,2501,2574,2656,2756,2855,2922,2987,3040,3098,3146,3207,3279,3353,3416,3489,3554,3614,3679,3731,3795,3873,3951,4005,4071"}, "to": {"startLines": "2,11,17,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,343,674,3875,3956,4036,4118,4221,4320,4399,4464,4555,4649,4719,4785,4850,4927,5049,5166,5287,5361,5443,5516,5598,5698,5797,5864,6600,6653,6711,6759,6820,6892,6966,7029,7102,7167,7227,7292,7344,7408,7486,7564,7618", "endLines": "10,16,22,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103", "endColumns": "17,12,12,80,79,81,102,98,78,64,90,93,69,65,64,76,121,116,120,73,81,72,81,99,98,66,64,52,57,47,60,71,73,62,72,64,59,64,51,63,77,77,53,65", "endOffsets": "338,669,983,3951,4031,4113,4216,4315,4394,4459,4550,4644,4714,4780,4845,4922,5044,5161,5282,5356,5438,5511,5593,5693,5792,5859,5924,6648,6706,6754,6815,6887,6961,7024,7097,7162,7222,7287,7339,7403,7481,7559,7613,7679"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\4c56c0fb8a9fdb1c153785f7a46f8077\\transformed\\media3-session-1.7.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,240,316,420,513,591,668,758,856,934,1006,1106,1189,1283,1366,1462,1542,1648,1721,1790,1877,1968,2061", "endColumns": "73,110,75,103,92,77,76,89,97,77,71,99,82,93,82,95,79,105,72,68,86,90,92,105", "endOffsets": "124,235,311,415,508,586,663,753,851,929,1001,1101,1184,1278,1361,1457,1537,1643,1716,1785,1872,1963,2056,2162"}, "to": {"startLines": "34,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,164,165,166,167,168,169", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2087,2356,2467,2543,2647,2740,2818,2895,2985,3083,3161,3233,3333,3416,3510,3593,3689,3769,14146,14219,14288,14375,14466,14559", "endColumns": "73,110,75,103,92,77,76,89,97,77,71,99,82,93,82,95,79,105,72,68,86,90,92,105", "endOffsets": "2156,2462,2538,2642,2735,2813,2890,2980,3078,3156,3228,3328,3411,3505,3588,3684,3764,3870,14214,14283,14370,14461,14554,14660"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\406d1819cde1682167f3af9259cf68c0\\transformed\\ui-release\\res\\values-sk\\values-sk.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "194,289,373,468,571,663,742,836,926,1007,1090,1177,1249,1339,1417,1493,1568,1646,1714", "endColumns": "94,83,94,102,91,78,93,89,80,82,86,71,89,77,75,74,77,67,113", "endOffsets": "284,368,463,566,658,737,831,921,1002,1085,1172,1244,1334,1412,1488,1563,1641,1709,1823"}, "to": {"startLines": "31,32,33,35,36,104,105,170,171,172,173,174,175,176,177,178,180,181,182", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1813,1908,1992,2161,2264,7684,7763,14665,14755,14836,14919,15006,15078,15168,15246,15322,15498,15576,15644", "endColumns": "94,83,94,102,91,78,93,89,80,82,86,71,89,77,75,74,77,67,113", "endOffsets": "1903,1987,2082,2259,2351,7758,7852,14750,14831,14914,15001,15073,15163,15241,15317,15392,15571,15639,15753"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\0b493fc5385f598ec7f47c0a10bc88bb\\transformed\\material3-release\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,285,395,510,608,703,815,950,1066,1218,1303,1404,1496,1593,1709,1831,1937,2070,2203,2337,2501,2629,2753,2883,3003,3096,3193,3314,3437,3535,3638,3747,3888,4037,4146,4246,4330,4424,4519,4635,4722,4809,4910,4990,5076,5173,5276,5369,5466,5554,5659,5756,5855,5975,6055,6157", "endColumns": "114,114,109,114,97,94,111,134,115,151,84,100,91,96,115,121,105,132,132,133,163,127,123,129,119,92,96,120,122,97,102,108,140,148,108,99,83,93,94,115,86,86,100,79,85,96,102,92,96,87,104,96,98,119,79,101,92", "endOffsets": "165,280,390,505,603,698,810,945,1061,1213,1298,1399,1491,1588,1704,1826,1932,2065,2198,2332,2496,2624,2748,2878,2998,3091,3188,3309,3432,3530,3633,3742,3883,4032,4141,4241,4325,4419,4514,4630,4717,4804,4905,4985,5071,5168,5271,5364,5461,5549,5654,5751,5850,5970,6050,6152,6245"}, "to": {"startLines": "106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7857,7972,8087,8197,8312,8410,8505,8617,8752,8868,9020,9105,9206,9298,9395,9511,9633,9739,9872,10005,10139,10303,10431,10555,10685,10805,10898,10995,11116,11239,11337,11440,11549,11690,11839,11948,12048,12132,12226,12321,12437,12524,12611,12712,12792,12878,12975,13078,13171,13268,13356,13461,13558,13657,13777,13857,13959", "endColumns": "114,114,109,114,97,94,111,134,115,151,84,100,91,96,115,121,105,132,132,133,163,127,123,129,119,92,96,120,122,97,102,108,140,148,108,99,83,93,94,115,86,86,100,79,85,96,102,92,96,87,104,96,98,119,79,101,92", "endOffsets": "7967,8082,8192,8307,8405,8500,8612,8747,8863,9015,9100,9201,9293,9390,9506,9628,9734,9867,10000,10134,10298,10426,10550,10680,10800,10893,10990,11111,11234,11332,11435,11544,11685,11834,11943,12043,12127,12221,12316,12432,12519,12606,12707,12787,12873,12970,13073,13166,13263,13351,13456,13553,13652,13772,13852,13954,14047"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\955c05c442e591726e4833ad1e0fc303\\transformed\\core-1.16.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,354,452,562,670,792", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "146,248,349,447,557,665,787,888"}, "to": {"startLines": "24,25,26,27,28,29,30,179", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "1076,1172,1274,1375,1473,1583,1691,15397", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "1167,1269,1370,1468,1578,1686,1808,15493"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\2425a4573e4fb65053b44e46c3806d87\\transformed\\foundation-release\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,143,227", "endColumns": "87,83,86", "endOffsets": "138,222,309"}, "to": {"startLines": "23,183,184", "startColumns": "4,4,4", "startOffsets": "988,15758,15842", "endColumns": "87,83,86", "endOffsets": "1071,15837,15924"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\26497276d619c831fa389bb2f1588ec8\\transformed\\media3-exoplayer-1.7.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,194,258,329,406,480,564,646", "endColumns": "76,61,63,70,76,73,83,81,79", "endOffsets": "127,189,253,324,401,475,559,641,721"}, "to": {"startLines": "78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "5929,6006,6068,6132,6203,6280,6354,6438,6520", "endColumns": "76,61,63,70,76,73,83,81,79", "endOffsets": "6001,6063,6127,6198,6275,6349,6433,6515,6595"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\3bf0e9913a8fb17f961a2eb6334c8d97\\transformed\\material-release\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "93", "endOffsets": "144"}, "to": {"startLines": "163", "startColumns": "4", "startOffsets": "14052", "endColumns": "93", "endOffsets": "14141"}}]}]}