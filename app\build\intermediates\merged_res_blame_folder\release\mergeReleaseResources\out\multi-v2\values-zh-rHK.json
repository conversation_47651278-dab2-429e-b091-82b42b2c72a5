{"logs": [{"outputFile": "com.libretv.android.app-mergeReleaseResources-2:/values-zh-rHK/values-zh-rHK.xml", "map": [{"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\6b27351f1a96970e2ba142187de324d4\\transformed\\media3-ui-1.7.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,281,444,602,672,741,811,887,962,1017,1078,1152,1226,1288,1349,1408,1473,1562,1648,1737,1800,1867,1932,1987,2061,2134,2195,2258,2310,2368,2415,2476,2532,2594,2651,2711,2767,2822,2885,2934,2987,3054,3121,3170", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,69,68,69,75,74,54,60,73,73,61,60,58,64,88,85,88,62,66,64,54,73,72,60,62,51,57,46,60,55,61,56,59,55,54,62,48,52,66,66,48,60", "endOffsets": "276,439,597,667,736,806,882,957,1012,1073,1147,1221,1283,1344,1403,1468,1557,1643,1732,1795,1862,1927,1982,2056,2129,2190,2253,2305,2363,2410,2471,2527,2589,2646,2706,2762,2817,2880,2929,2982,3049,3116,3165,3226"}, "to": {"startLines": "2,11,15,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,331,494,3038,3108,3177,3247,3323,3398,3453,3514,3588,3662,3724,3785,3844,3909,3998,4084,4173,4236,4303,4368,4423,4497,4570,4631,5252,5304,5362,5409,5470,5526,5588,5645,5705,5761,5816,5879,5928,5981,6048,6115,6164", "endLines": "10,14,18,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99", "endColumns": "17,12,12,69,68,69,75,74,54,60,73,73,61,60,58,64,88,85,88,62,66,64,54,73,72,60,62,51,57,46,60,55,61,56,59,55,54,62,48,52,66,66,48,60", "endOffsets": "326,489,647,3103,3172,3242,3318,3393,3448,3509,3583,3657,3719,3780,3839,3904,3993,4079,4168,4231,4298,4363,4418,4492,4565,4626,4689,5299,5357,5404,5465,5521,5583,5640,5700,5756,5811,5874,5923,5976,6043,6110,6159,6220"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\0b493fc5385f598ec7f47c0a10bc88bb\\transformed\\material3-release\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,158,260,364,465,556,645,750,855,960,1076,1158,1254,1338,1426,1531,1644,1745,1853,1959,2067,2183,2288,2390,2495,2601,2686,2781,2886,2995,3085,3187,3285,3394,3508,3608,3699,3772,3862,3951,4042,4125,4207,4296,4376,4458,4555,4649,4742,4835,4919,5015,5111,5206,5314,5394,5486", "endColumns": "102,101,103,100,90,88,104,104,104,115,81,95,83,87,104,112,100,107,105,107,115,104,101,104,105,84,94,104,108,89,101,97,108,113,99,90,72,89,88,90,82,81,88,79,81,96,93,92,92,83,95,95,94,107,79,91,89", "endOffsets": "153,255,359,460,551,640,745,850,955,1071,1153,1249,1333,1421,1526,1639,1740,1848,1954,2062,2178,2283,2385,2490,2596,2681,2776,2881,2990,3080,3182,3280,3389,3503,3603,3694,3767,3857,3946,4037,4120,4202,4291,4371,4453,4550,4644,4737,4830,4914,5010,5106,5201,5309,5389,5481,5571"}, "to": {"startLines": "102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6376,6479,6581,6685,6786,6877,6966,7071,7176,7281,7397,7479,7575,7659,7747,7852,7965,8066,8174,8280,8388,8504,8609,8711,8816,8922,9007,9102,9207,9316,9406,9508,9606,9715,9829,9929,10020,10093,10183,10272,10363,10446,10528,10617,10697,10779,10876,10970,11063,11156,11240,11336,11432,11527,11635,11715,11807", "endColumns": "102,101,103,100,90,88,104,104,104,115,81,95,83,87,104,112,100,107,105,107,115,104,101,104,105,84,94,104,108,89,101,97,108,113,99,90,72,89,88,90,82,81,88,79,81,96,93,92,92,83,95,95,94,107,79,91,89", "endOffsets": "6474,6576,6680,6781,6872,6961,7066,7171,7276,7392,7474,7570,7654,7742,7847,7960,8061,8169,8275,8383,8499,8604,8706,8811,8917,9002,9097,9202,9311,9401,9503,9601,9710,9824,9924,10015,10088,10178,10267,10358,10441,10523,10612,10692,10774,10871,10965,11058,11151,11235,11331,11427,11522,11630,11710,11802,11892"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\4c56c0fb8a9fdb1c153785f7a46f8077\\transformed\\media3-session-1.7.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,122,201,261,340,417,485,553,616,688,754,807,884,951,1028,1093,1169,1233,1302,1367,1431,1500,1572,1647", "endColumns": "66,78,59,78,76,67,67,62,71,65,52,76,66,76,64,75,63,68,64,63,68,71,74,78", "endOffsets": "117,196,256,335,412,480,548,611,683,749,802,879,946,1023,1088,1164,1228,1297,1362,1426,1495,1567,1642,1721"}, "to": {"startLines": "30,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,159,160,161,162,163,164", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1622,1858,1937,1997,2076,2153,2221,2289,2352,2424,2490,2543,2620,2687,2764,2829,2905,2969,11897,11962,12026,12095,12167,12242", "endColumns": "66,78,59,78,76,67,67,62,71,65,52,76,66,76,64,75,63,68,64,63,68,71,74,78", "endOffsets": "1684,1932,1992,2071,2148,2216,2284,2347,2419,2485,2538,2615,2682,2759,2824,2900,2964,3033,11957,12021,12090,12162,12237,12316"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\955c05c442e591726e4833ad1e0fc303\\transformed\\core-1.16.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "20,21,22,23,24,25,26,174", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "723,815,914,1008,1102,1195,1288,12981", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "810,909,1003,1097,1190,1283,1379,13077"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\406d1819cde1682167f3af9259cf68c0\\transformed\\ui-release\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "177,253,327,415,506,584,658,735,813,887,960,1035,1102,1183,1256,1326,1395,1470,1535", "endColumns": "75,73,87,90,77,73,76,77,73,72,74,66,80,72,69,68,74,64,115", "endOffsets": "248,322,410,501,579,653,730,808,882,955,1030,1097,1178,1251,1321,1390,1465,1530,1646"}, "to": {"startLines": "27,28,29,31,32,100,101,165,166,167,168,169,170,171,172,173,175,176,177", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1384,1460,1534,1689,1780,6225,6299,12321,12399,12473,12546,12621,12688,12769,12842,12912,13082,13157,13222", "endColumns": "75,73,87,90,77,73,76,77,73,72,74,66,80,72,69,68,74,64,115", "endOffsets": "1455,1529,1617,1775,1853,6294,6371,12394,12468,12541,12616,12683,12764,12837,12907,12976,13152,13217,13333"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\2425a4573e4fb65053b44e46c3806d87\\transformed\\foundation-release\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,126,207", "endColumns": "70,80,76", "endOffsets": "121,202,279"}, "to": {"startLines": "19,178,179", "startColumns": "4,4,4", "startOffsets": "652,13338,13419", "endColumns": "70,80,76", "endOffsets": "718,13414,13491"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\26497276d619c831fa389bb2f1588ec8\\transformed\\media3-exoplayer-1.7.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,111,167,225,278,350,404,478,554", "endColumns": "55,55,57,52,71,53,73,75,58", "endOffsets": "106,162,220,273,345,399,473,549,608"}, "to": {"startLines": "74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "4694,4750,4806,4864,4917,4989,5043,5117,5193", "endColumns": "55,55,57,52,71,53,73,75,58", "endOffsets": "4745,4801,4859,4912,4984,5038,5112,5188,5247"}}]}]}