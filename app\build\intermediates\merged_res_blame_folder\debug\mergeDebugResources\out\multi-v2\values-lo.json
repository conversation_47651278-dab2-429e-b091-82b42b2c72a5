{"logs": [{"outputFile": "com.libretv.android.app-mergeDebugResources-2:/values-lo/values-lo.xml", "map": [{"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\26497276d619c831fa389bb2f1588ec8\\transformed\\media3-exoplayer-1.7.1\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,185,251,316,391,461,553,640", "endColumns": "68,60,65,64,74,69,91,86,71", "endOffsets": "119,180,246,311,386,456,548,635,707"}, "to": {"startLines": "74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "5395,5464,5525,5591,5656,5731,5801,5893,5980", "endColumns": "68,60,65,64,74,69,91,86,71", "endOffsets": "5459,5520,5586,5651,5726,5796,5888,5975,6047"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\2425a4573e4fb65053b44e46c3806d87\\transformed\\foundation-release\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,142,228", "endColumns": "86,85,84", "endOffsets": "137,223,308"}, "to": {"startLines": "19,179,180", "startColumns": "4,4,4", "startOffsets": "705,15040,15126", "endColumns": "86,85,84", "endOffsets": "787,15121,15206"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\0b493fc5385f598ec7f47c0a10bc88bb\\transformed\\material3-release\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,281,391,502,599,695,808,937,1058,1189,1274,1374,1464,1564,1682,1802,1907,2034,2159,2289,2437,2558,2672,2791,2903,2994,3093,3206,3331,3425,3541,3647,3774,3908,4018,4115,4195,4293,4389,4496,4582,4668,4773,4859,4946,5049,5151,5246,5349,5435,5536,5634,5736,5863,5949,6049", "endColumns": "113,111,109,110,96,95,112,128,120,130,84,99,89,99,117,119,104,126,124,129,147,120,113,118,111,90,98,112,124,93,115,105,126,133,109,96,79,97,95,106,85,85,104,85,86,102,101,94,102,85,100,97,101,126,85,99,94", "endOffsets": "164,276,386,497,594,690,803,932,1053,1184,1269,1369,1459,1559,1677,1797,1902,2029,2154,2284,2432,2553,2667,2786,2898,2989,3088,3201,3326,3420,3536,3642,3769,3903,4013,4110,4190,4288,4384,4491,4577,4663,4768,4854,4941,5044,5146,5241,5344,5430,5531,5629,5731,5858,5944,6044,6139"}, "to": {"startLines": "102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7278,7392,7504,7614,7725,7822,7918,8031,8160,8281,8412,8497,8597,8687,8787,8905,9025,9130,9257,9382,9512,9660,9781,9895,10014,10126,10217,10316,10429,10554,10648,10764,10870,10997,11131,11241,11338,11418,11516,11612,11719,11805,11891,11996,12082,12169,12272,12374,12469,12572,12658,12759,12857,12959,13086,13172,13272", "endColumns": "113,111,109,110,96,95,112,128,120,130,84,99,89,99,117,119,104,126,124,129,147,120,113,118,111,90,98,112,124,93,115,105,126,133,109,96,79,97,95,106,85,85,104,85,86,102,101,94,102,85,100,97,101,126,85,99,94", "endOffsets": "7387,7499,7609,7720,7817,7913,8026,8155,8276,8407,8492,8592,8682,8782,8900,9020,9125,9252,9377,9507,9655,9776,9890,10009,10121,10212,10311,10424,10549,10643,10759,10865,10992,11126,11236,11333,11413,11511,11607,11714,11800,11886,11991,12077,12164,12267,12369,12464,12567,12653,12754,12852,12954,13081,13167,13267,13362"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\955c05c442e591726e4833ad1e0fc303\\transformed\\core-1.16.0\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,353,451,552,650,761", "endColumns": "95,102,98,97,100,97,110,100", "endOffsets": "146,249,348,446,547,645,756,857"}, "to": {"startLines": "20,21,22,23,24,25,26,175", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "792,888,991,1090,1188,1289,1387,14674", "endColumns": "95,102,98,97,100,97,110,100", "endOffsets": "883,986,1085,1183,1284,1382,1493,14770"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\406d1819cde1682167f3af9259cf68c0\\transformed\\ui-release\\res\\values-lo\\values-lo.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "193,283,360,469,567,656,745,835,921,1004,1084,1168,1242,1330,1411,1486,1561,1639,1705", "endColumns": "89,76,108,97,88,88,89,85,82,79,83,73,87,80,74,74,77,65,120", "endOffsets": "278,355,464,562,651,740,830,916,999,1079,1163,1237,1325,1406,1481,1556,1634,1700,1821"}, "to": {"startLines": "27,28,29,31,32,100,101,166,167,168,169,170,171,172,173,174,176,177,178", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1498,1588,1665,1853,1951,7099,7188,13948,14034,14117,14197,14281,14355,14443,14524,14599,14775,14853,14919", "endColumns": "89,76,108,97,88,88,89,85,82,79,83,73,87,80,74,74,77,65,120", "endOffsets": "1583,1660,1769,1946,2035,7183,7273,14029,14112,14192,14276,14350,14438,14519,14594,14669,14848,14914,15035"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\6b27351f1a96970e2ba142187de324d4\\transformed\\media3-ui-1.7.1\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,477,655,737,817,894,982,1064,1140,1204,1297,1389,1459,1523,1586,1656,1766,1873,1983,2051,2128,2198,2274,2358,2440,2502,2565,2618,2676,2724,2785,2844,2912,2973,3039,3103,3162,3226,3280,3340,3414,3488,3544", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,81,79,76,87,81,75,63,92,91,69,63,62,69,109,106,109,67,76,69,75,83,81,61,62,52,57,47,60,58,67,60,65,63,58,63,53,59,73,73,55,67", "endOffsets": "281,472,650,732,812,889,977,1059,1135,1199,1292,1384,1454,1518,1581,1651,1761,1868,1978,2046,2123,2193,2269,2353,2435,2497,2560,2613,2671,2719,2780,2839,2907,2968,3034,3098,3157,3221,3275,3335,3409,3483,3539,3607"}, "to": {"startLines": "2,11,15,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,336,527,3485,3567,3647,3724,3812,3894,3970,4034,4127,4219,4289,4353,4416,4486,4596,4703,4813,4881,4958,5028,5104,5188,5270,5332,6052,6105,6163,6211,6272,6331,6399,6460,6526,6590,6649,6713,6767,6827,6901,6975,7031", "endLines": "10,14,18,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99", "endColumns": "17,12,12,81,79,76,87,81,75,63,92,91,69,63,62,69,109,106,109,67,76,69,75,83,81,61,62,52,57,47,60,58,67,60,65,63,58,63,53,59,73,73,55,67", "endOffsets": "331,522,700,3562,3642,3719,3807,3889,3965,4029,4122,4214,4284,4348,4411,4481,4591,4698,4808,4876,4953,5023,5099,5183,5265,5327,5390,6100,6158,6206,6267,6326,6394,6455,6521,6585,6644,6708,6762,6822,6896,6970,7026,7094"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\4c56c0fb8a9fdb1c153785f7a46f8077\\transformed\\media3-session-1.7.1\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,134,228,303,400,487,574,653,737,827,905,972,1071,1158,1251,1331,1428,1497,1579,1652,1719,1799,1880,1971", "endColumns": "78,93,74,96,86,86,78,83,89,77,66,98,86,92,79,96,68,81,72,66,79,80,90,96", "endOffsets": "129,223,298,395,482,569,648,732,822,900,967,1066,1153,1246,1326,1423,1492,1574,1647,1714,1794,1875,1966,2063"}, "to": {"startLines": "30,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,160,161,162,163,164,165", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1774,2040,2134,2209,2306,2393,2480,2559,2643,2733,2811,2878,2977,3064,3157,3237,3334,3403,13459,13532,13599,13679,13760,13851", "endColumns": "78,93,74,96,86,86,78,83,89,77,66,98,86,92,79,96,68,81,72,66,79,80,90,96", "endOffsets": "1848,2129,2204,2301,2388,2475,2554,2638,2728,2806,2873,2972,3059,3152,3232,3329,3398,3480,13527,13594,13674,13755,13846,13943"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\3bf0e9913a8fb17f961a2eb6334c8d97\\transformed\\material-release\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "91", "endOffsets": "142"}, "to": {"startLines": "159", "startColumns": "4", "startOffsets": "13367", "endColumns": "91", "endOffsets": "13454"}}]}]}