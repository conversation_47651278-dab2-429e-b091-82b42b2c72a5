{"logs": [{"outputFile": "com.libretv.android.app-mergeDebugResources-2:/values-ur/values-ur.xml", "map": [{"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\406d1819cde1682167f3af9259cf68c0\\transformed\\ui-release\\res\\values-ur\\values-ur.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "179,273,356,446,543,631,712,805,893,979,1062,1147,1222,1305,1383,1457,1530,1605,1671", "endColumns": "93,82,89,96,87,80,92,87,85,82,84,74,82,77,73,72,74,65,116", "endOffsets": "268,351,441,538,626,707,800,888,974,1057,1142,1217,1300,1378,1452,1525,1600,1666,1783"}, "to": {"startLines": "27,28,29,31,32,100,101,166,167,168,169,170,171,172,173,174,176,177,178", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1519,1613,1696,1863,1960,7203,7284,14275,14363,14449,14532,14617,14692,14775,14853,14927,15101,15176,15242", "endColumns": "93,82,89,96,87,80,92,87,85,82,84,74,82,77,73,72,74,65,116", "endOffsets": "1608,1691,1781,1955,2043,7279,7372,14358,14444,14527,14612,14687,14770,14848,14922,14995,15171,15237,15354"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\6b27351f1a96970e2ba142187de324d4\\transformed\\media3-ui-1.7.1\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,282,488,675,763,854,939,1034,1129,1197,1259,1348,1437,1507,1572,1634,1702,1812,1924,2033,2107,2188,2258,2326,2412,2501,2565,2628,2681,2739,2787,2848,2908,2977,3037,3100,3160,3223,3288,3341,3398,3469,3540,3594", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,87,90,84,94,94,67,61,88,88,69,64,61,67,109,111,108,73,80,69,67,85,88,63,62,52,57,47,60,59,68,59,62,59,62,64,52,56,70,70,53,65", "endOffsets": "277,483,670,758,849,934,1029,1124,1192,1254,1343,1432,1502,1567,1629,1697,1807,1919,2028,2102,2183,2253,2321,2407,2496,2560,2623,2676,2734,2782,2843,2903,2972,3032,3095,3155,3218,3283,3336,3393,3464,3535,3589,3655"}, "to": {"startLines": "2,11,15,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,332,538,3534,3622,3713,3798,3893,3988,4056,4118,4207,4296,4366,4431,4493,4561,4671,4783,4892,4966,5047,5117,5185,5271,5360,5424,6171,6224,6282,6330,6391,6451,6520,6580,6643,6703,6766,6831,6884,6941,7012,7083,7137", "endLines": "10,14,18,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99", "endColumns": "17,12,12,87,90,84,94,94,67,61,88,88,69,64,61,67,109,111,108,73,80,69,67,85,88,63,62,52,57,47,60,59,68,59,62,59,62,64,52,56,70,70,53,65", "endOffsets": "327,533,720,3617,3708,3793,3888,3983,4051,4113,4202,4291,4361,4426,4488,4556,4666,4778,4887,4961,5042,5112,5180,5266,5355,5419,5482,6219,6277,6325,6386,6446,6515,6575,6638,6698,6761,6826,6879,6936,7007,7078,7132,7198"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\4c56c0fb8a9fdb1c153785f7a46f8077\\transformed\\media3-session-1.7.1\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,240,307,402,503,588,675,761,854,940,1007,1104,1185,1272,1351,1440,1513,1618,1686,1754,1831,1910,1998", "endColumns": "76,107,66,94,100,84,86,85,92,85,66,96,80,86,78,88,72,104,67,67,76,78,87,92", "endOffsets": "127,235,302,397,498,583,670,756,849,935,1002,1099,1180,1267,1346,1435,1508,1613,1681,1749,1826,1905,1993,2086"}, "to": {"startLines": "30,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,160,161,162,163,164,165", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1786,2048,2156,2223,2318,2419,2504,2591,2677,2770,2856,2923,3020,3101,3188,3267,3356,3429,13802,13870,13938,14015,14094,14182", "endColumns": "76,107,66,94,100,84,86,85,92,85,66,96,80,86,78,88,72,104,67,67,76,78,87,92", "endOffsets": "1858,2151,2218,2313,2414,2499,2586,2672,2765,2851,2918,3015,3096,3183,3262,3351,3424,3529,13865,13933,14010,14089,14177,14270"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\955c05c442e591726e4833ad1e0fc303\\transformed\\core-1.16.0\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,357,461,564,662,776", "endColumns": "97,101,101,103,102,97,113,100", "endOffsets": "148,250,352,456,559,657,771,872"}, "to": {"startLines": "20,21,22,23,24,25,26,175", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "798,896,998,1100,1204,1307,1405,15000", "endColumns": "97,101,101,103,102,97,113,100", "endOffsets": "891,993,1095,1199,1302,1400,1514,15096"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\3bf0e9913a8fb17f961a2eb6334c8d97\\transformed\\material-release\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "86", "endOffsets": "137"}, "to": {"startLines": "159", "startColumns": "4", "startOffsets": "13715", "endColumns": "86", "endOffsets": "13797"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\0b493fc5385f598ec7f47c0a10bc88bb\\transformed\\material3-release\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,295,411,529,627,724,839,974,1098,1238,1323,1427,1523,1623,1740,1870,1979,2123,2266,2395,2593,2718,2837,2960,3098,3195,3290,3414,3538,3639,3744,3850,3993,4142,4248,4352,4428,4524,4621,4733,4823,4914,5029,5109,5194,5297,5403,5500,5603,5688,5794,5893,5996,6117,6197,6299", "endColumns": "117,121,115,117,97,96,114,134,123,139,84,103,95,99,116,129,108,143,142,128,197,124,118,122,137,96,94,123,123,100,104,105,142,148,105,103,75,95,96,111,89,90,114,79,84,102,105,96,102,84,105,98,102,120,79,101,93", "endOffsets": "168,290,406,524,622,719,834,969,1093,1233,1318,1422,1518,1618,1735,1865,1974,2118,2261,2390,2588,2713,2832,2955,3093,3190,3285,3409,3533,3634,3739,3845,3988,4137,4243,4347,4423,4519,4616,4728,4818,4909,5024,5104,5189,5292,5398,5495,5598,5683,5789,5888,5991,6112,6192,6294,6388"}, "to": {"startLines": "102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7377,7495,7617,7733,7851,7949,8046,8161,8296,8420,8560,8645,8749,8845,8945,9062,9192,9301,9445,9588,9717,9915,10040,10159,10282,10420,10517,10612,10736,10860,10961,11066,11172,11315,11464,11570,11674,11750,11846,11943,12055,12145,12236,12351,12431,12516,12619,12725,12822,12925,13010,13116,13215,13318,13439,13519,13621", "endColumns": "117,121,115,117,97,96,114,134,123,139,84,103,95,99,116,129,108,143,142,128,197,124,118,122,137,96,94,123,123,100,104,105,142,148,105,103,75,95,96,111,89,90,114,79,84,102,105,96,102,84,105,98,102,120,79,101,93", "endOffsets": "7490,7612,7728,7846,7944,8041,8156,8291,8415,8555,8640,8744,8840,8940,9057,9187,9296,9440,9583,9712,9910,10035,10154,10277,10415,10512,10607,10731,10855,10956,11061,11167,11310,11459,11565,11669,11745,11841,11938,12050,12140,12231,12346,12426,12511,12614,12720,12817,12920,13005,13111,13210,13313,13434,13514,13616,13710"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\2425a4573e4fb65053b44e46c3806d87\\transformed\\foundation-release\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,128,213", "endColumns": "72,84,86", "endOffsets": "123,208,295"}, "to": {"startLines": "19,179,180", "startColumns": "4,4,4", "startOffsets": "725,15359,15444", "endColumns": "72,84,86", "endOffsets": "793,15439,15526"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\26497276d619c831fa389bb2f1588ec8\\transformed\\media3-exoplayer-1.7.1\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,194,266,336,413,484,575,660", "endColumns": "71,66,71,69,76,70,90,84,78", "endOffsets": "122,189,261,331,408,479,570,655,734"}, "to": {"startLines": "74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "5487,5559,5626,5698,5768,5845,5916,6007,6092", "endColumns": "71,66,71,69,76,70,90,84,78", "endOffsets": "5554,5621,5693,5763,5840,5911,6002,6087,6166"}}]}]}