{"<This is a virtual key for removed outputs; DO NOT USE>": [], "src\\main\\java\\com\\libretv\\android\\presentation\\screens\\history\\HistoryViewModel.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\presentation\\screens\\history\\HistoryViewModel_HiltModules.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\presentation\\screens\\history\\HistoryViewModel_Factory.java"], "src\\main\\java\\com\\libretv\\android\\presentation\\screens\\home\\HomeViewModel.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\presentation\\screens\\home\\HomeViewModel_HiltModules.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\presentation\\screens\\home\\HomeViewModel_Factory.java"], "src\\main\\java\\com\\libretv\\android\\presentation\\screens\\search\\SearchViewModel.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\presentation\\screens\\search\\SearchViewModel_HiltModules.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\presentation\\screens\\search\\SearchViewModel_Factory.java"], "src\\main\\java\\com\\libretv\\android\\presentation\\screens\\settings\\SettingsViewModel.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\presentation\\screens\\settings\\SettingsViewModel_HiltModules.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\presentation\\screens\\settings\\SettingsViewModel_Factory.java"], "src\\main\\java\\com\\example\\shipin\\MainActivity.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\presentation\\MainActivity_GeneratedInjector.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\presentation\\Hilt_MainActivity.java"], "src\\main\\java\\com\\libretv\\android\\LibreTVApplication.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\LibreTVApplication_GeneratedInjector.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\dagger\\hilt\\internal\\aggregatedroot\\codegen\\_com_libretv_android_LibreTVApplication.java"], "src\\main\\java\\com\\libretv\\android\\di\\DatabaseModule.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\hilt_aggregated_deps\\_com_libretv_android_di_DatabaseModule.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\di\\DatabaseModule_ProvideLibreTVDatabaseFactory.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\di\\DatabaseModule_ProvideVideoDaoFactory.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\di\\DatabaseModule_ProvideSearchHistoryDaoFactory.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\di\\DatabaseModule_ProvideWatchHistoryDaoFactory.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\di\\DatabaseModule_ProvideApiSourceDaoFactory.java"], "src\\main\\java\\com\\libretv\\android\\di\\NetworkModule.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\hilt_aggregated_deps\\_com_libretv_android_di_NetworkModule.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\di\\NetworkModule_ProvideGsonFactory.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\di\\NetworkModule_ProvideHttpLoggingInterceptorFactory.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\di\\NetworkModule_ProvideOkHttpClientFactory.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\di\\NetworkModule_ProvideApiRetrofitFactory.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\di\\NetworkModule_ProvideInternalRetrofitFactory.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\di\\NetworkModule_ProvideDoubanRetrofitFactory.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\di\\NetworkModule_ProvideApiServiceFactory.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\di\\NetworkModule_ProvideInternalApiServiceFactory.java", "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\di\\NetworkModule_ProvideDoubanApiServiceFactory.java"], "src\\main\\java\\com\\libretv\\android\\data\\database\\LibreTVDatabase.kt": ["build\\generated\\ksp\\debug\\kotlin\\com\\libretv\\android\\data\\database\\VideoDao_Impl.kt", "build\\generated\\ksp\\debug\\kotlin\\com\\libretv\\android\\data\\database\\SearchHistoryDao_Impl.kt", "build\\generated\\ksp\\debug\\kotlin\\com\\libretv\\android\\data\\database\\WatchHistoryDao_Impl.kt", "build\\generated\\ksp\\debug\\kotlin\\com\\libretv\\android\\data\\database\\ApiSourceDao_Impl.kt", "build\\generated\\ksp\\debug\\kotlin\\com\\libretv\\android\\data\\database\\LibreTVDatabase_Impl.kt"], "src\\main\\java\\com\\libretv\\android\\data\\preferences\\SettingsManager.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\data\\preferences\\SettingsManager_Factory.java"], "src\\main\\java\\com\\libretv\\android\\data\\repository\\ApiSourceRepository.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\data\\repository\\ApiSourceRepository_Factory.java"], "src\\main\\java\\com\\libretv\\android\\data\\repository\\SearchRepository.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\data\\repository\\SearchRepository_Factory.java"], "src\\main\\java\\com\\libretv\\android\\data\\repository\\VideoRepository.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\data\\repository\\VideoRepository_Factory.java"], "src\\main\\java\\com\\libretv\\android\\data\\repository\\WatchHistoryRepository.kt": ["build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\data\\repository\\WatchHistoryRepository_Factory.java"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\LibreTVApplication_GeneratedInjector.java": ["build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_libretv_android_LibreTVApplication_GeneratedInjector.java"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\presentation\\MainActivity_GeneratedInjector.java": ["build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_libretv_android_presentation_MainActivity_GeneratedInjector.java"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\presentation\\screens\\history\\HistoryViewModel_HiltModules.java": ["build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_libretv_android_presentation_screens_history_HistoryViewModel_HiltModules_BindsModule.java", "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_libretv_android_presentation_screens_history_HistoryViewModel_HiltModules_KeyModule.java", "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\libretv\\android\\presentation\\screens\\history\\HistoryViewModel_HiltModules_KeyModule_ProvideFactory.java"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\presentation\\screens\\home\\HomeViewModel_HiltModules.java": ["build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_libretv_android_presentation_screens_home_HomeViewModel_HiltModules_BindsModule.java", "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_libretv_android_presentation_screens_home_HomeViewModel_HiltModules_KeyModule.java", "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\libretv\\android\\presentation\\screens\\home\\HomeViewModel_HiltModules_KeyModule_ProvideFactory.java"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\presentation\\screens\\search\\SearchViewModel_HiltModules.java": ["build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_libretv_android_presentation_screens_search_SearchViewModel_HiltModules_BindsModule.java", "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_libretv_android_presentation_screens_search_SearchViewModel_HiltModules_KeyModule.java", "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\libretv\\android\\presentation\\screens\\search\\SearchViewModel_HiltModules_KeyModule_ProvideFactory.java"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\presentation\\screens\\settings\\SettingsViewModel_HiltModules.java": ["build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_libretv_android_presentation_screens_settings_SettingsViewModel_HiltModules_BindsModule.java", "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_libretv_android_presentation_screens_settings_SettingsViewModel_HiltModules_KeyModule.java", "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\libretv\\android\\presentation\\screens\\settings\\SettingsViewModel_HiltModules_KeyModule_ProvideFactory.java"]}