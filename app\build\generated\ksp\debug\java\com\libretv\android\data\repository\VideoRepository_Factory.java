package com.libretv.android.data.repository;

import com.libretv.android.data.database.ApiSourceDao;
import com.libretv.android.data.database.VideoDao;
import com.libretv.android.data.network.ApiService;
import com.libretv.android.data.network.InternalApiService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class VideoRepository_Factory implements Factory<VideoRepository> {
  private final Provider<ApiService> apiServiceProvider;

  private final Provider<InternalApiService> internalApiServiceProvider;

  private final Provider<VideoDao> videoDaoProvider;

  private final Provider<ApiSourceDao> apiSourceDaoProvider;

  public VideoRepository_Factory(Provider<ApiService> apiServiceProvider,
      Provider<InternalApiService> internalApiServiceProvider, Provider<VideoDao> videoDaoProvider,
      Provider<ApiSourceDao> apiSourceDaoProvider) {
    this.apiServiceProvider = apiServiceProvider;
    this.internalApiServiceProvider = internalApiServiceProvider;
    this.videoDaoProvider = videoDaoProvider;
    this.apiSourceDaoProvider = apiSourceDaoProvider;
  }

  @Override
  public VideoRepository get() {
    return newInstance(apiServiceProvider.get(), internalApiServiceProvider.get(), videoDaoProvider.get(), apiSourceDaoProvider.get());
  }

  public static VideoRepository_Factory create(Provider<ApiService> apiServiceProvider,
      Provider<InternalApiService> internalApiServiceProvider, Provider<VideoDao> videoDaoProvider,
      Provider<ApiSourceDao> apiSourceDaoProvider) {
    return new VideoRepository_Factory(apiServiceProvider, internalApiServiceProvider, videoDaoProvider, apiSourceDaoProvider);
  }

  public static VideoRepository newInstance(ApiService apiService,
      InternalApiService internalApiService, VideoDao videoDao, ApiSourceDao apiSourceDao) {
    return new VideoRepository(apiService, internalApiService, videoDao, apiSourceDao);
  }
}
