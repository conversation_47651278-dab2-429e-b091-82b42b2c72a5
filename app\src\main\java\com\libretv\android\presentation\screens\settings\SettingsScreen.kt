package com.libretv.android.presentation.screens.settings

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel

/**
 * 设置屏幕
 */
@Composable
fun SettingsScreen(
    viewModel: SettingsViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    
    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        item {
            Text(
                text = "设置",
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 16.dp)
            )
        }
        
        // 播放设置
        item {
            SettingsSection(title = "播放设置") {
                SwitchSettingItem(
                    title = "自动连播",
                    subtitle = "播放完成后自动播放下一集",
                    icon = Icons.Default.PlayArrow,
                    checked = uiState.autoPlayNext,
                    onCheckedChange = viewModel::setAutoPlayNext
                )
                
                SwitchSettingItem(
                    title = "记住播放进度",
                    subtitle = "自动保存观看进度",
                    icon = Icons.Default.History,
                    checked = uiState.rememberProgress,
                    onCheckedChange = viewModel::setRememberProgress
                )
                
                SwitchSettingItem(
                    title = "自动全屏",
                    subtitle = "播放时自动进入全屏模式",
                    icon = Icons.Default.Fullscreen,
                    checked = uiState.playerAutoFullscreen,
                    onCheckedChange = viewModel::setPlayerAutoFullscreen
                )
            }
        }
        
        // 内容设置
        item {
            SettingsSection(title = "内容设置") {
                SwitchSettingItem(
                    title = "成人内容",
                    subtitle = "显示成人内容源",
                    icon = Icons.Default.Warning,
                    checked = uiState.adultContentEnabled,
                    onCheckedChange = viewModel::setAdultContentEnabled
                )
                
                ClickableSettingItem(
                    title = "API源管理",
                    subtitle = "管理视频源",
                    icon = Icons.Default.Source,
                    onClick = { /* TODO: 导航到API源管理页面 */ }
                )
            }
        }
        
        // 界面设置
        item {
            SettingsSection(title = "界面设置") {
                ClickableSettingItem(
                    title = "主题模式",
                    subtitle = when (uiState.themeMode) {
                        "light" -> "浅色模式"
                        "dark" -> "深色模式"
                        else -> "跟随系统"
                    },
                    icon = Icons.Default.Palette,
                    onClick = { /* TODO: 显示主题选择对话框 */ }
                )
                
                ClickableSettingItem(
                    title = "网格列数",
                    subtitle = "${uiState.gridColumns}列",
                    icon = Icons.Default.GridView,
                    onClick = { /* TODO: 显示列数选择对话框 */ }
                )
            }
        }
        
        // 缓存设置
        item {
            SettingsSection(title = "缓存设置") {
                SwitchSettingItem(
                    title = "启用缓存",
                    subtitle = "缓存视频数据以提高加载速度",
                    icon = Icons.Default.Storage,
                    checked = uiState.cacheEnabled,
                    onCheckedChange = viewModel::setCacheEnabled
                )
                
                ClickableSettingItem(
                    title = "缓存大小",
                    subtitle = "${uiState.cacheSizeMB}MB",
                    icon = Icons.Default.Folder,
                    onClick = { /* TODO: 显示缓存大小选择对话框 */ }
                )
                
                ClickableSettingItem(
                    title = "清理缓存",
                    subtitle = "清理所有缓存数据",
                    icon = Icons.Default.Delete,
                    onClick = { viewModel.clearCache() }
                )
            }
        }
        
        // 关于
        item {
            SettingsSection(title = "关于") {
                ClickableSettingItem(
                    title = "版本信息",
                    subtitle = "LibreTV Android v1.0.3",
                    icon = Icons.Default.Info,
                    onClick = { /* TODO: 显示版本信息 */ }
                )
                
                ClickableSettingItem(
                    title = "开源许可",
                    subtitle = "查看开源许可证",
                    icon = Icons.Default.Code,
                    onClick = { /* TODO: 显示开源许可 */ }
                )
            }
        }
    }
}

@Composable
private fun SettingsSection(
    title: String,
    content: @Composable ColumnScope.() -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.primary,
                modifier = Modifier.padding(bottom = 12.dp)
            )
            content()
        }
    }
}

@Composable
private fun SwitchSettingItem(
    title: String,
    subtitle: String,
    icon: ImageVector,
    checked: Boolean,
    onCheckedChange: (Boolean) -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            tint = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.size(24.dp)
        )
        
        Column(
            modifier = Modifier
                .weight(1f)
                .padding(horizontal = 16.dp)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.bodyLarge
            )
            Text(
                text = subtitle,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
        
        Switch(
            checked = checked,
            onCheckedChange = onCheckedChange
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun ClickableSettingItem(
    title: String,
    subtitle: String,
    icon: ImageVector,
    onClick: () -> Unit
) {
    Card(
        onClick = onClick,
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.size(24.dp)
            )
            
            Column(
                modifier = Modifier
                    .weight(1f)
                    .padding(horizontal = 16.dp)
            ) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.bodyLarge
                )
                Text(
                    text = subtitle,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            Icon(
                imageVector = Icons.Default.ChevronRight,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}
