package com.libretv.android.data.repository

import com.libretv.android.data.database.SearchHistoryDao
import com.libretv.android.data.model.SearchHistory
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 搜索历史数据仓库
 */
@Singleton
class SearchRepository @Inject constructor(
    private val searchHistoryDao: SearchHistoryDao
) {
    
    /**
     * 获取最近搜索记录
     */
    fun getRecentSearches(limit: Int = 10): Flow<List<SearchHistory>> {
        return searchHistoryDao.getRecentSearches(limit)
    }
    
    /**
     * 添加搜索记录
     */
    suspend fun addSearchHistory(query: String) {
        if (query.isNotBlank()) {
            val searchHistory = SearchHistory(
                query = query.trim(),
                timestamp = System.currentTimeMillis()
            )
            searchHistoryDao.insertSearch(searchHistory)
            
            // 限制历史记录数量
            val count = searchHistoryDao.getHistoryCount()
            if (count > 50) {
                searchHistoryDao.deleteOldestEntries(count - 50)
            }
        }
    }
    
    /**
     * 删除搜索记录
     */
    suspend fun deleteSearchHistory(search: SearchHistory) {
        searchHistoryDao.deleteSearch(search)
    }
    
    /**
     * 清空所有搜索历史
     */
    suspend fun clearAllHistory() {
        searchHistoryDao.clearAllHistory()
    }
    
    /**
     * 搜索历史记录
     */
    suspend fun searchHistory(query: String): List<SearchHistory> {
        return searchHistoryDao.searchHistory(query)
    }
    
    /**
     * 清理旧的搜索历史
     */
    suspend fun cleanOldHistory(daysAgo: Int = 30) {
        val timestamp = System.currentTimeMillis() - (daysAgo * 24 * 60 * 60 * 1000L)
        searchHistoryDao.deleteOldHistory(timestamp)
    }
}
