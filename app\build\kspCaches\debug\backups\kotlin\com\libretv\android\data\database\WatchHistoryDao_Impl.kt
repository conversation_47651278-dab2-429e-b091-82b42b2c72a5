package com.libretv.android.`data`.database

import androidx.room.EntityDeleteOrUpdateAdapter
import androidx.room.EntityInsertAdapter
import androidx.room.RoomDatabase
import androidx.room.coroutines.createFlow
import androidx.room.util.getColumnIndexOrThrow
import androidx.room.util.performSuspending
import androidx.sqlite.SQLiteStatement
import com.libretv.android.`data`.model.WatchHistory
import javax.`annotation`.processing.Generated
import kotlin.Int
import kotlin.Long
import kotlin.String
import kotlin.Suppress
import kotlin.Unit
import kotlin.collections.List
import kotlin.collections.MutableList
import kotlin.collections.mutableListOf
import kotlin.reflect.KClass
import kotlinx.coroutines.flow.Flow

@Generated(value = ["androidx.room.RoomProcessor"])
@Suppress(names = ["UNCHECKED_CAST", "DEPRECATION", "REDUNDANT_PROJECTION", "REMOVAL"])
public class WatchHistoryDao_Impl(
  __db: RoomDatabase,
) : WatchHistoryDao {
  private val __db: RoomDatabase

  private val __insertAdapterOfWatchHistory: EntityInsertAdapter<WatchHistory>

  private val __converters: Converters = Converters()

  private val __deleteAdapterOfWatchHistory: EntityDeleteOrUpdateAdapter<WatchHistory>

  private val __updateAdapterOfWatchHistory: EntityDeleteOrUpdateAdapter<WatchHistory>
  init {
    this.__db = __db
    this.__insertAdapterOfWatchHistory = object : EntityInsertAdapter<WatchHistory>() {
      protected override fun createQuery(): String =
          "INSERT OR REPLACE INTO `watch_history` (`id`,`videoId`,`videoTitle`,`videoCover`,`sourceCode`,`sourceName`,`episodeIndex`,`episodeTitle`,`watchProgress`,`totalDuration`,`lastWatchTime`,`episodes`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?)"

      protected override fun bind(statement: SQLiteStatement, entity: WatchHistory) {
        statement.bindText(1, entity.id)
        statement.bindText(2, entity.videoId)
        statement.bindText(3, entity.videoTitle)
        val _tmpVideoCover: String? = entity.videoCover
        if (_tmpVideoCover == null) {
          statement.bindNull(4)
        } else {
          statement.bindText(4, _tmpVideoCover)
        }
        statement.bindText(5, entity.sourceCode)
        statement.bindText(6, entity.sourceName)
        statement.bindLong(7, entity.episodeIndex.toLong())
        val _tmpEpisodeTitle: String? = entity.episodeTitle
        if (_tmpEpisodeTitle == null) {
          statement.bindNull(8)
        } else {
          statement.bindText(8, _tmpEpisodeTitle)
        }
        statement.bindLong(9, entity.watchProgress)
        statement.bindLong(10, entity.totalDuration)
        statement.bindLong(11, entity.lastWatchTime)
        val _tmp: String = __converters.fromStringList(entity.episodes)
        statement.bindText(12, _tmp)
      }
    }
    this.__deleteAdapterOfWatchHistory = object : EntityDeleteOrUpdateAdapter<WatchHistory>() {
      protected override fun createQuery(): String = "DELETE FROM `watch_history` WHERE `id` = ?"

      protected override fun bind(statement: SQLiteStatement, entity: WatchHistory) {
        statement.bindText(1, entity.id)
      }
    }
    this.__updateAdapterOfWatchHistory = object : EntityDeleteOrUpdateAdapter<WatchHistory>() {
      protected override fun createQuery(): String =
          "UPDATE OR ABORT `watch_history` SET `id` = ?,`videoId` = ?,`videoTitle` = ?,`videoCover` = ?,`sourceCode` = ?,`sourceName` = ?,`episodeIndex` = ?,`episodeTitle` = ?,`watchProgress` = ?,`totalDuration` = ?,`lastWatchTime` = ?,`episodes` = ? WHERE `id` = ?"

      protected override fun bind(statement: SQLiteStatement, entity: WatchHistory) {
        statement.bindText(1, entity.id)
        statement.bindText(2, entity.videoId)
        statement.bindText(3, entity.videoTitle)
        val _tmpVideoCover: String? = entity.videoCover
        if (_tmpVideoCover == null) {
          statement.bindNull(4)
        } else {
          statement.bindText(4, _tmpVideoCover)
        }
        statement.bindText(5, entity.sourceCode)
        statement.bindText(6, entity.sourceName)
        statement.bindLong(7, entity.episodeIndex.toLong())
        val _tmpEpisodeTitle: String? = entity.episodeTitle
        if (_tmpEpisodeTitle == null) {
          statement.bindNull(8)
        } else {
          statement.bindText(8, _tmpEpisodeTitle)
        }
        statement.bindLong(9, entity.watchProgress)
        statement.bindLong(10, entity.totalDuration)
        statement.bindLong(11, entity.lastWatchTime)
        val _tmp: String = __converters.fromStringList(entity.episodes)
        statement.bindText(12, _tmp)
        statement.bindText(13, entity.id)
      }
    }
  }

  public override suspend fun insertWatchHistory(history: WatchHistory): Unit =
      performSuspending(__db, false, true) { _connection ->
    __insertAdapterOfWatchHistory.insert(_connection, history)
  }

  public override suspend fun deleteWatchHistory(history: WatchHistory): Unit =
      performSuspending(__db, false, true) { _connection ->
    __deleteAdapterOfWatchHistory.handle(_connection, history)
  }

  public override suspend fun updateWatchHistory(history: WatchHistory): Unit =
      performSuspending(__db, false, true) { _connection ->
    __updateAdapterOfWatchHistory.handle(_connection, history)
  }

  public override fun getAllWatchHistory(): Flow<List<WatchHistory>> {
    val _sql: String = "SELECT * FROM watch_history ORDER BY lastWatchTime DESC"
    return createFlow(__db, false, arrayOf("watch_history")) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        val _columnIndexOfId: Int = getColumnIndexOrThrow(_stmt, "id")
        val _columnIndexOfVideoId: Int = getColumnIndexOrThrow(_stmt, "videoId")
        val _columnIndexOfVideoTitle: Int = getColumnIndexOrThrow(_stmt, "videoTitle")
        val _columnIndexOfVideoCover: Int = getColumnIndexOrThrow(_stmt, "videoCover")
        val _columnIndexOfSourceCode: Int = getColumnIndexOrThrow(_stmt, "sourceCode")
        val _columnIndexOfSourceName: Int = getColumnIndexOrThrow(_stmt, "sourceName")
        val _columnIndexOfEpisodeIndex: Int = getColumnIndexOrThrow(_stmt, "episodeIndex")
        val _columnIndexOfEpisodeTitle: Int = getColumnIndexOrThrow(_stmt, "episodeTitle")
        val _columnIndexOfWatchProgress: Int = getColumnIndexOrThrow(_stmt, "watchProgress")
        val _columnIndexOfTotalDuration: Int = getColumnIndexOrThrow(_stmt, "totalDuration")
        val _columnIndexOfLastWatchTime: Int = getColumnIndexOrThrow(_stmt, "lastWatchTime")
        val _columnIndexOfEpisodes: Int = getColumnIndexOrThrow(_stmt, "episodes")
        val _result: MutableList<WatchHistory> = mutableListOf()
        while (_stmt.step()) {
          val _item: WatchHistory
          val _tmpId: String
          _tmpId = _stmt.getText(_columnIndexOfId)
          val _tmpVideoId: String
          _tmpVideoId = _stmt.getText(_columnIndexOfVideoId)
          val _tmpVideoTitle: String
          _tmpVideoTitle = _stmt.getText(_columnIndexOfVideoTitle)
          val _tmpVideoCover: String?
          if (_stmt.isNull(_columnIndexOfVideoCover)) {
            _tmpVideoCover = null
          } else {
            _tmpVideoCover = _stmt.getText(_columnIndexOfVideoCover)
          }
          val _tmpSourceCode: String
          _tmpSourceCode = _stmt.getText(_columnIndexOfSourceCode)
          val _tmpSourceName: String
          _tmpSourceName = _stmt.getText(_columnIndexOfSourceName)
          val _tmpEpisodeIndex: Int
          _tmpEpisodeIndex = _stmt.getLong(_columnIndexOfEpisodeIndex).toInt()
          val _tmpEpisodeTitle: String?
          if (_stmt.isNull(_columnIndexOfEpisodeTitle)) {
            _tmpEpisodeTitle = null
          } else {
            _tmpEpisodeTitle = _stmt.getText(_columnIndexOfEpisodeTitle)
          }
          val _tmpWatchProgress: Long
          _tmpWatchProgress = _stmt.getLong(_columnIndexOfWatchProgress)
          val _tmpTotalDuration: Long
          _tmpTotalDuration = _stmt.getLong(_columnIndexOfTotalDuration)
          val _tmpLastWatchTime: Long
          _tmpLastWatchTime = _stmt.getLong(_columnIndexOfLastWatchTime)
          val _tmpEpisodes: List<String>
          val _tmp: String
          _tmp = _stmt.getText(_columnIndexOfEpisodes)
          _tmpEpisodes = __converters.toStringList(_tmp)
          _item =
              WatchHistory(_tmpId,_tmpVideoId,_tmpVideoTitle,_tmpVideoCover,_tmpSourceCode,_tmpSourceName,_tmpEpisodeIndex,_tmpEpisodeTitle,_tmpWatchProgress,_tmpTotalDuration,_tmpLastWatchTime,_tmpEpisodes)
          _result.add(_item)
        }
        _result
      } finally {
        _stmt.close()
      }
    }
  }

  public override fun getRecentWatchHistory(limit: Int): Flow<List<WatchHistory>> {
    val _sql: String = "SELECT * FROM watch_history ORDER BY lastWatchTime DESC LIMIT ?"
    return createFlow(__db, false, arrayOf("watch_history")) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        var _argIndex: Int = 1
        _stmt.bindLong(_argIndex, limit.toLong())
        val _columnIndexOfId: Int = getColumnIndexOrThrow(_stmt, "id")
        val _columnIndexOfVideoId: Int = getColumnIndexOrThrow(_stmt, "videoId")
        val _columnIndexOfVideoTitle: Int = getColumnIndexOrThrow(_stmt, "videoTitle")
        val _columnIndexOfVideoCover: Int = getColumnIndexOrThrow(_stmt, "videoCover")
        val _columnIndexOfSourceCode: Int = getColumnIndexOrThrow(_stmt, "sourceCode")
        val _columnIndexOfSourceName: Int = getColumnIndexOrThrow(_stmt, "sourceName")
        val _columnIndexOfEpisodeIndex: Int = getColumnIndexOrThrow(_stmt, "episodeIndex")
        val _columnIndexOfEpisodeTitle: Int = getColumnIndexOrThrow(_stmt, "episodeTitle")
        val _columnIndexOfWatchProgress: Int = getColumnIndexOrThrow(_stmt, "watchProgress")
        val _columnIndexOfTotalDuration: Int = getColumnIndexOrThrow(_stmt, "totalDuration")
        val _columnIndexOfLastWatchTime: Int = getColumnIndexOrThrow(_stmt, "lastWatchTime")
        val _columnIndexOfEpisodes: Int = getColumnIndexOrThrow(_stmt, "episodes")
        val _result: MutableList<WatchHistory> = mutableListOf()
        while (_stmt.step()) {
          val _item: WatchHistory
          val _tmpId: String
          _tmpId = _stmt.getText(_columnIndexOfId)
          val _tmpVideoId: String
          _tmpVideoId = _stmt.getText(_columnIndexOfVideoId)
          val _tmpVideoTitle: String
          _tmpVideoTitle = _stmt.getText(_columnIndexOfVideoTitle)
          val _tmpVideoCover: String?
          if (_stmt.isNull(_columnIndexOfVideoCover)) {
            _tmpVideoCover = null
          } else {
            _tmpVideoCover = _stmt.getText(_columnIndexOfVideoCover)
          }
          val _tmpSourceCode: String
          _tmpSourceCode = _stmt.getText(_columnIndexOfSourceCode)
          val _tmpSourceName: String
          _tmpSourceName = _stmt.getText(_columnIndexOfSourceName)
          val _tmpEpisodeIndex: Int
          _tmpEpisodeIndex = _stmt.getLong(_columnIndexOfEpisodeIndex).toInt()
          val _tmpEpisodeTitle: String?
          if (_stmt.isNull(_columnIndexOfEpisodeTitle)) {
            _tmpEpisodeTitle = null
          } else {
            _tmpEpisodeTitle = _stmt.getText(_columnIndexOfEpisodeTitle)
          }
          val _tmpWatchProgress: Long
          _tmpWatchProgress = _stmt.getLong(_columnIndexOfWatchProgress)
          val _tmpTotalDuration: Long
          _tmpTotalDuration = _stmt.getLong(_columnIndexOfTotalDuration)
          val _tmpLastWatchTime: Long
          _tmpLastWatchTime = _stmt.getLong(_columnIndexOfLastWatchTime)
          val _tmpEpisodes: List<String>
          val _tmp: String
          _tmp = _stmt.getText(_columnIndexOfEpisodes)
          _tmpEpisodes = __converters.toStringList(_tmp)
          _item =
              WatchHistory(_tmpId,_tmpVideoId,_tmpVideoTitle,_tmpVideoCover,_tmpSourceCode,_tmpSourceName,_tmpEpisodeIndex,_tmpEpisodeTitle,_tmpWatchProgress,_tmpTotalDuration,_tmpLastWatchTime,_tmpEpisodes)
          _result.add(_item)
        }
        _result
      } finally {
        _stmt.close()
      }
    }
  }

  public override suspend fun getWatchHistoryById(id: String): WatchHistory? {
    val _sql: String = "SELECT * FROM watch_history WHERE id = ?"
    return performSuspending(__db, true, false) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        var _argIndex: Int = 1
        _stmt.bindText(_argIndex, id)
        val _columnIndexOfId: Int = getColumnIndexOrThrow(_stmt, "id")
        val _columnIndexOfVideoId: Int = getColumnIndexOrThrow(_stmt, "videoId")
        val _columnIndexOfVideoTitle: Int = getColumnIndexOrThrow(_stmt, "videoTitle")
        val _columnIndexOfVideoCover: Int = getColumnIndexOrThrow(_stmt, "videoCover")
        val _columnIndexOfSourceCode: Int = getColumnIndexOrThrow(_stmt, "sourceCode")
        val _columnIndexOfSourceName: Int = getColumnIndexOrThrow(_stmt, "sourceName")
        val _columnIndexOfEpisodeIndex: Int = getColumnIndexOrThrow(_stmt, "episodeIndex")
        val _columnIndexOfEpisodeTitle: Int = getColumnIndexOrThrow(_stmt, "episodeTitle")
        val _columnIndexOfWatchProgress: Int = getColumnIndexOrThrow(_stmt, "watchProgress")
        val _columnIndexOfTotalDuration: Int = getColumnIndexOrThrow(_stmt, "totalDuration")
        val _columnIndexOfLastWatchTime: Int = getColumnIndexOrThrow(_stmt, "lastWatchTime")
        val _columnIndexOfEpisodes: Int = getColumnIndexOrThrow(_stmt, "episodes")
        val _result: WatchHistory?
        if (_stmt.step()) {
          val _tmpId: String
          _tmpId = _stmt.getText(_columnIndexOfId)
          val _tmpVideoId: String
          _tmpVideoId = _stmt.getText(_columnIndexOfVideoId)
          val _tmpVideoTitle: String
          _tmpVideoTitle = _stmt.getText(_columnIndexOfVideoTitle)
          val _tmpVideoCover: String?
          if (_stmt.isNull(_columnIndexOfVideoCover)) {
            _tmpVideoCover = null
          } else {
            _tmpVideoCover = _stmt.getText(_columnIndexOfVideoCover)
          }
          val _tmpSourceCode: String
          _tmpSourceCode = _stmt.getText(_columnIndexOfSourceCode)
          val _tmpSourceName: String
          _tmpSourceName = _stmt.getText(_columnIndexOfSourceName)
          val _tmpEpisodeIndex: Int
          _tmpEpisodeIndex = _stmt.getLong(_columnIndexOfEpisodeIndex).toInt()
          val _tmpEpisodeTitle: String?
          if (_stmt.isNull(_columnIndexOfEpisodeTitle)) {
            _tmpEpisodeTitle = null
          } else {
            _tmpEpisodeTitle = _stmt.getText(_columnIndexOfEpisodeTitle)
          }
          val _tmpWatchProgress: Long
          _tmpWatchProgress = _stmt.getLong(_columnIndexOfWatchProgress)
          val _tmpTotalDuration: Long
          _tmpTotalDuration = _stmt.getLong(_columnIndexOfTotalDuration)
          val _tmpLastWatchTime: Long
          _tmpLastWatchTime = _stmt.getLong(_columnIndexOfLastWatchTime)
          val _tmpEpisodes: List<String>
          val _tmp: String
          _tmp = _stmt.getText(_columnIndexOfEpisodes)
          _tmpEpisodes = __converters.toStringList(_tmp)
          _result =
              WatchHistory(_tmpId,_tmpVideoId,_tmpVideoTitle,_tmpVideoCover,_tmpSourceCode,_tmpSourceName,_tmpEpisodeIndex,_tmpEpisodeTitle,_tmpWatchProgress,_tmpTotalDuration,_tmpLastWatchTime,_tmpEpisodes)
        } else {
          _result = null
        }
        _result
      } finally {
        _stmt.close()
      }
    }
  }

  public override suspend fun getWatchHistory(videoId: String, sourceCode: String): WatchHistory? {
    val _sql: String = "SELECT * FROM watch_history WHERE videoId = ? AND sourceCode = ?"
    return performSuspending(__db, true, false) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        var _argIndex: Int = 1
        _stmt.bindText(_argIndex, videoId)
        _argIndex = 2
        _stmt.bindText(_argIndex, sourceCode)
        val _columnIndexOfId: Int = getColumnIndexOrThrow(_stmt, "id")
        val _columnIndexOfVideoId: Int = getColumnIndexOrThrow(_stmt, "videoId")
        val _columnIndexOfVideoTitle: Int = getColumnIndexOrThrow(_stmt, "videoTitle")
        val _columnIndexOfVideoCover: Int = getColumnIndexOrThrow(_stmt, "videoCover")
        val _columnIndexOfSourceCode: Int = getColumnIndexOrThrow(_stmt, "sourceCode")
        val _columnIndexOfSourceName: Int = getColumnIndexOrThrow(_stmt, "sourceName")
        val _columnIndexOfEpisodeIndex: Int = getColumnIndexOrThrow(_stmt, "episodeIndex")
        val _columnIndexOfEpisodeTitle: Int = getColumnIndexOrThrow(_stmt, "episodeTitle")
        val _columnIndexOfWatchProgress: Int = getColumnIndexOrThrow(_stmt, "watchProgress")
        val _columnIndexOfTotalDuration: Int = getColumnIndexOrThrow(_stmt, "totalDuration")
        val _columnIndexOfLastWatchTime: Int = getColumnIndexOrThrow(_stmt, "lastWatchTime")
        val _columnIndexOfEpisodes: Int = getColumnIndexOrThrow(_stmt, "episodes")
        val _result: WatchHistory?
        if (_stmt.step()) {
          val _tmpId: String
          _tmpId = _stmt.getText(_columnIndexOfId)
          val _tmpVideoId: String
          _tmpVideoId = _stmt.getText(_columnIndexOfVideoId)
          val _tmpVideoTitle: String
          _tmpVideoTitle = _stmt.getText(_columnIndexOfVideoTitle)
          val _tmpVideoCover: String?
          if (_stmt.isNull(_columnIndexOfVideoCover)) {
            _tmpVideoCover = null
          } else {
            _tmpVideoCover = _stmt.getText(_columnIndexOfVideoCover)
          }
          val _tmpSourceCode: String
          _tmpSourceCode = _stmt.getText(_columnIndexOfSourceCode)
          val _tmpSourceName: String
          _tmpSourceName = _stmt.getText(_columnIndexOfSourceName)
          val _tmpEpisodeIndex: Int
          _tmpEpisodeIndex = _stmt.getLong(_columnIndexOfEpisodeIndex).toInt()
          val _tmpEpisodeTitle: String?
          if (_stmt.isNull(_columnIndexOfEpisodeTitle)) {
            _tmpEpisodeTitle = null
          } else {
            _tmpEpisodeTitle = _stmt.getText(_columnIndexOfEpisodeTitle)
          }
          val _tmpWatchProgress: Long
          _tmpWatchProgress = _stmt.getLong(_columnIndexOfWatchProgress)
          val _tmpTotalDuration: Long
          _tmpTotalDuration = _stmt.getLong(_columnIndexOfTotalDuration)
          val _tmpLastWatchTime: Long
          _tmpLastWatchTime = _stmt.getLong(_columnIndexOfLastWatchTime)
          val _tmpEpisodes: List<String>
          val _tmp: String
          _tmp = _stmt.getText(_columnIndexOfEpisodes)
          _tmpEpisodes = __converters.toStringList(_tmp)
          _result =
              WatchHistory(_tmpId,_tmpVideoId,_tmpVideoTitle,_tmpVideoCover,_tmpSourceCode,_tmpSourceName,_tmpEpisodeIndex,_tmpEpisodeTitle,_tmpWatchProgress,_tmpTotalDuration,_tmpLastWatchTime,_tmpEpisodes)
        } else {
          _result = null
        }
        _result
      } finally {
        _stmt.close()
      }
    }
  }

  public override suspend fun clearAllHistory() {
    val _sql: String = "DELETE FROM watch_history"
    return performSuspending(__db, false, true) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        _stmt.step()
      } finally {
        _stmt.close()
      }
    }
  }

  public override suspend fun deleteOldHistory(timestamp: Long) {
    val _sql: String = "DELETE FROM watch_history WHERE lastWatchTime < ?"
    return performSuspending(__db, false, true) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        var _argIndex: Int = 1
        _stmt.bindLong(_argIndex, timestamp)
        _stmt.step()
      } finally {
        _stmt.close()
      }
    }
  }

  public override suspend fun updateProgress(
    id: String,
    progress: Long,
    timestamp: Long,
  ) {
    val _sql: String = "UPDATE watch_history SET watchProgress = ?, lastWatchTime = ? WHERE id = ?"
    return performSuspending(__db, false, true) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        var _argIndex: Int = 1
        _stmt.bindLong(_argIndex, progress)
        _argIndex = 2
        _stmt.bindLong(_argIndex, timestamp)
        _argIndex = 3
        _stmt.bindText(_argIndex, id)
        _stmt.step()
      } finally {
        _stmt.close()
      }
    }
  }

  public override suspend fun updateEpisode(
    id: String,
    episodeIndex: Int,
    episodeTitle: String?,
    timestamp: Long,
  ) {
    val _sql: String =
        "UPDATE watch_history SET episodeIndex = ?, episodeTitle = ?, lastWatchTime = ? WHERE id = ?"
    return performSuspending(__db, false, true) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        var _argIndex: Int = 1
        _stmt.bindLong(_argIndex, episodeIndex.toLong())
        _argIndex = 2
        if (episodeTitle == null) {
          _stmt.bindNull(_argIndex)
        } else {
          _stmt.bindText(_argIndex, episodeTitle)
        }
        _argIndex = 3
        _stmt.bindLong(_argIndex, timestamp)
        _argIndex = 4
        _stmt.bindText(_argIndex, id)
        _stmt.step()
      } finally {
        _stmt.close()
      }
    }
  }

  public companion object {
    public fun getRequiredConverters(): List<KClass<*>> = emptyList()
  }
}
