package com.libretv.android.`data`.database

import androidx.room.EntityDeleteOrUpdateAdapter
import androidx.room.EntityInsertAdapter
import androidx.room.RoomDatabase
import androidx.room.coroutines.createFlow
import androidx.room.util.getColumnIndexOrThrow
import androidx.room.util.performSuspending
import androidx.sqlite.SQLiteStatement
import com.libretv.android.`data`.model.ApiSource
import javax.`annotation`.processing.Generated
import kotlin.Boolean
import kotlin.Int
import kotlin.String
import kotlin.Suppress
import kotlin.Unit
import kotlin.collections.List
import kotlin.collections.MutableList
import kotlin.collections.mutableListOf
import kotlin.reflect.KClass
import kotlinx.coroutines.flow.Flow

@Generated(value = ["androidx.room.RoomProcessor"])
@Suppress(names = ["UNCHECKED_CAST", "DEPRECATION", "REDUNDANT_PROJECTION", "REMOVAL"])
public class ApiSourceDao_Impl(
  __db: RoomDatabase,
) : ApiSourceDao {
  private val __db: RoomDatabase

  private val __insertAdapterOfApiSource: EntityInsertAdapter<ApiSource>

  private val __deleteAdapterOfApiSource: EntityDeleteOrUpdateAdapter<ApiSource>

  private val __updateAdapterOfApiSource: EntityDeleteOrUpdateAdapter<ApiSource>
  init {
    this.__db = __db
    this.__insertAdapterOfApiSource = object : EntityInsertAdapter<ApiSource>() {
      protected override fun createQuery(): String =
          "INSERT OR REPLACE INTO `api_sources` (`code`,`name`,`api`,`detail`,`isAdult`,`isEnabled`,`isCustom`,`order`) VALUES (?,?,?,?,?,?,?,?)"

      protected override fun bind(statement: SQLiteStatement, entity: ApiSource) {
        statement.bindText(1, entity.code)
        statement.bindText(2, entity.name)
        statement.bindText(3, entity.api)
        val _tmpDetail: String? = entity.detail
        if (_tmpDetail == null) {
          statement.bindNull(4)
        } else {
          statement.bindText(4, _tmpDetail)
        }
        val _tmp: Int = if (entity.isAdult) 1 else 0
        statement.bindLong(5, _tmp.toLong())
        val _tmp_1: Int = if (entity.isEnabled) 1 else 0
        statement.bindLong(6, _tmp_1.toLong())
        val _tmp_2: Int = if (entity.isCustom) 1 else 0
        statement.bindLong(7, _tmp_2.toLong())
        statement.bindLong(8, entity.order.toLong())
      }
    }
    this.__deleteAdapterOfApiSource = object : EntityDeleteOrUpdateAdapter<ApiSource>() {
      protected override fun createQuery(): String = "DELETE FROM `api_sources` WHERE `code` = ?"

      protected override fun bind(statement: SQLiteStatement, entity: ApiSource) {
        statement.bindText(1, entity.code)
      }
    }
    this.__updateAdapterOfApiSource = object : EntityDeleteOrUpdateAdapter<ApiSource>() {
      protected override fun createQuery(): String =
          "UPDATE OR ABORT `api_sources` SET `code` = ?,`name` = ?,`api` = ?,`detail` = ?,`isAdult` = ?,`isEnabled` = ?,`isCustom` = ?,`order` = ? WHERE `code` = ?"

      protected override fun bind(statement: SQLiteStatement, entity: ApiSource) {
        statement.bindText(1, entity.code)
        statement.bindText(2, entity.name)
        statement.bindText(3, entity.api)
        val _tmpDetail: String? = entity.detail
        if (_tmpDetail == null) {
          statement.bindNull(4)
        } else {
          statement.bindText(4, _tmpDetail)
        }
        val _tmp: Int = if (entity.isAdult) 1 else 0
        statement.bindLong(5, _tmp.toLong())
        val _tmp_1: Int = if (entity.isEnabled) 1 else 0
        statement.bindLong(6, _tmp_1.toLong())
        val _tmp_2: Int = if (entity.isCustom) 1 else 0
        statement.bindLong(7, _tmp_2.toLong())
        statement.bindLong(8, entity.order.toLong())
        statement.bindText(9, entity.code)
      }
    }
  }

  public override suspend fun insertApiSource(apiSource: ApiSource): Unit = performSuspending(__db,
      false, true) { _connection ->
    __insertAdapterOfApiSource.insert(_connection, apiSource)
  }

  public override suspend fun insertApiSources(apiSources: List<ApiSource>): Unit =
      performSuspending(__db, false, true) { _connection ->
    __insertAdapterOfApiSource.insert(_connection, apiSources)
  }

  public override suspend fun deleteApiSource(apiSource: ApiSource): Unit = performSuspending(__db,
      false, true) { _connection ->
    __deleteAdapterOfApiSource.handle(_connection, apiSource)
  }

  public override suspend fun updateApiSource(apiSource: ApiSource): Unit = performSuspending(__db,
      false, true) { _connection ->
    __updateAdapterOfApiSource.handle(_connection, apiSource)
  }

  public override fun getAllApiSources(): Flow<List<ApiSource>> {
    val _sql: String = "SELECT * FROM api_sources ORDER BY `order` ASC, name ASC"
    return createFlow(__db, false, arrayOf("api_sources")) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        val _columnIndexOfCode: Int = getColumnIndexOrThrow(_stmt, "code")
        val _columnIndexOfName: Int = getColumnIndexOrThrow(_stmt, "name")
        val _columnIndexOfApi: Int = getColumnIndexOrThrow(_stmt, "api")
        val _columnIndexOfDetail: Int = getColumnIndexOrThrow(_stmt, "detail")
        val _columnIndexOfIsAdult: Int = getColumnIndexOrThrow(_stmt, "isAdult")
        val _columnIndexOfIsEnabled: Int = getColumnIndexOrThrow(_stmt, "isEnabled")
        val _columnIndexOfIsCustom: Int = getColumnIndexOrThrow(_stmt, "isCustom")
        val _columnIndexOfOrder: Int = getColumnIndexOrThrow(_stmt, "order")
        val _result: MutableList<ApiSource> = mutableListOf()
        while (_stmt.step()) {
          val _item: ApiSource
          val _tmpCode: String
          _tmpCode = _stmt.getText(_columnIndexOfCode)
          val _tmpName: String
          _tmpName = _stmt.getText(_columnIndexOfName)
          val _tmpApi: String
          _tmpApi = _stmt.getText(_columnIndexOfApi)
          val _tmpDetail: String?
          if (_stmt.isNull(_columnIndexOfDetail)) {
            _tmpDetail = null
          } else {
            _tmpDetail = _stmt.getText(_columnIndexOfDetail)
          }
          val _tmpIsAdult: Boolean
          val _tmp: Int
          _tmp = _stmt.getLong(_columnIndexOfIsAdult).toInt()
          _tmpIsAdult = _tmp != 0
          val _tmpIsEnabled: Boolean
          val _tmp_1: Int
          _tmp_1 = _stmt.getLong(_columnIndexOfIsEnabled).toInt()
          _tmpIsEnabled = _tmp_1 != 0
          val _tmpIsCustom: Boolean
          val _tmp_2: Int
          _tmp_2 = _stmt.getLong(_columnIndexOfIsCustom).toInt()
          _tmpIsCustom = _tmp_2 != 0
          val _tmpOrder: Int
          _tmpOrder = _stmt.getLong(_columnIndexOfOrder).toInt()
          _item =
              ApiSource(_tmpCode,_tmpName,_tmpApi,_tmpDetail,_tmpIsAdult,_tmpIsEnabled,_tmpIsCustom,_tmpOrder)
          _result.add(_item)
        }
        _result
      } finally {
        _stmt.close()
      }
    }
  }

  public override fun getEnabledApiSources(): Flow<List<ApiSource>> {
    val _sql: String =
        "SELECT * FROM api_sources WHERE isEnabled = 1 ORDER BY `order` ASC, name ASC"
    return createFlow(__db, false, arrayOf("api_sources")) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        val _columnIndexOfCode: Int = getColumnIndexOrThrow(_stmt, "code")
        val _columnIndexOfName: Int = getColumnIndexOrThrow(_stmt, "name")
        val _columnIndexOfApi: Int = getColumnIndexOrThrow(_stmt, "api")
        val _columnIndexOfDetail: Int = getColumnIndexOrThrow(_stmt, "detail")
        val _columnIndexOfIsAdult: Int = getColumnIndexOrThrow(_stmt, "isAdult")
        val _columnIndexOfIsEnabled: Int = getColumnIndexOrThrow(_stmt, "isEnabled")
        val _columnIndexOfIsCustom: Int = getColumnIndexOrThrow(_stmt, "isCustom")
        val _columnIndexOfOrder: Int = getColumnIndexOrThrow(_stmt, "order")
        val _result: MutableList<ApiSource> = mutableListOf()
        while (_stmt.step()) {
          val _item: ApiSource
          val _tmpCode: String
          _tmpCode = _stmt.getText(_columnIndexOfCode)
          val _tmpName: String
          _tmpName = _stmt.getText(_columnIndexOfName)
          val _tmpApi: String
          _tmpApi = _stmt.getText(_columnIndexOfApi)
          val _tmpDetail: String?
          if (_stmt.isNull(_columnIndexOfDetail)) {
            _tmpDetail = null
          } else {
            _tmpDetail = _stmt.getText(_columnIndexOfDetail)
          }
          val _tmpIsAdult: Boolean
          val _tmp: Int
          _tmp = _stmt.getLong(_columnIndexOfIsAdult).toInt()
          _tmpIsAdult = _tmp != 0
          val _tmpIsEnabled: Boolean
          val _tmp_1: Int
          _tmp_1 = _stmt.getLong(_columnIndexOfIsEnabled).toInt()
          _tmpIsEnabled = _tmp_1 != 0
          val _tmpIsCustom: Boolean
          val _tmp_2: Int
          _tmp_2 = _stmt.getLong(_columnIndexOfIsCustom).toInt()
          _tmpIsCustom = _tmp_2 != 0
          val _tmpOrder: Int
          _tmpOrder = _stmt.getLong(_columnIndexOfOrder).toInt()
          _item =
              ApiSource(_tmpCode,_tmpName,_tmpApi,_tmpDetail,_tmpIsAdult,_tmpIsEnabled,_tmpIsCustom,_tmpOrder)
          _result.add(_item)
        }
        _result
      } finally {
        _stmt.close()
      }
    }
  }

  public override fun getNormalApiSources(): Flow<List<ApiSource>> {
    val _sql: String =
        "SELECT * FROM api_sources WHERE isAdult = 0 AND isEnabled = 1 ORDER BY `order` ASC, name ASC"
    return createFlow(__db, false, arrayOf("api_sources")) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        val _columnIndexOfCode: Int = getColumnIndexOrThrow(_stmt, "code")
        val _columnIndexOfName: Int = getColumnIndexOrThrow(_stmt, "name")
        val _columnIndexOfApi: Int = getColumnIndexOrThrow(_stmt, "api")
        val _columnIndexOfDetail: Int = getColumnIndexOrThrow(_stmt, "detail")
        val _columnIndexOfIsAdult: Int = getColumnIndexOrThrow(_stmt, "isAdult")
        val _columnIndexOfIsEnabled: Int = getColumnIndexOrThrow(_stmt, "isEnabled")
        val _columnIndexOfIsCustom: Int = getColumnIndexOrThrow(_stmt, "isCustom")
        val _columnIndexOfOrder: Int = getColumnIndexOrThrow(_stmt, "order")
        val _result: MutableList<ApiSource> = mutableListOf()
        while (_stmt.step()) {
          val _item: ApiSource
          val _tmpCode: String
          _tmpCode = _stmt.getText(_columnIndexOfCode)
          val _tmpName: String
          _tmpName = _stmt.getText(_columnIndexOfName)
          val _tmpApi: String
          _tmpApi = _stmt.getText(_columnIndexOfApi)
          val _tmpDetail: String?
          if (_stmt.isNull(_columnIndexOfDetail)) {
            _tmpDetail = null
          } else {
            _tmpDetail = _stmt.getText(_columnIndexOfDetail)
          }
          val _tmpIsAdult: Boolean
          val _tmp: Int
          _tmp = _stmt.getLong(_columnIndexOfIsAdult).toInt()
          _tmpIsAdult = _tmp != 0
          val _tmpIsEnabled: Boolean
          val _tmp_1: Int
          _tmp_1 = _stmt.getLong(_columnIndexOfIsEnabled).toInt()
          _tmpIsEnabled = _tmp_1 != 0
          val _tmpIsCustom: Boolean
          val _tmp_2: Int
          _tmp_2 = _stmt.getLong(_columnIndexOfIsCustom).toInt()
          _tmpIsCustom = _tmp_2 != 0
          val _tmpOrder: Int
          _tmpOrder = _stmt.getLong(_columnIndexOfOrder).toInt()
          _item =
              ApiSource(_tmpCode,_tmpName,_tmpApi,_tmpDetail,_tmpIsAdult,_tmpIsEnabled,_tmpIsCustom,_tmpOrder)
          _result.add(_item)
        }
        _result
      } finally {
        _stmt.close()
      }
    }
  }

  public override fun getAdultApiSources(): Flow<List<ApiSource>> {
    val _sql: String =
        "SELECT * FROM api_sources WHERE isAdult = 1 AND isEnabled = 1 ORDER BY `order` ASC, name ASC"
    return createFlow(__db, false, arrayOf("api_sources")) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        val _columnIndexOfCode: Int = getColumnIndexOrThrow(_stmt, "code")
        val _columnIndexOfName: Int = getColumnIndexOrThrow(_stmt, "name")
        val _columnIndexOfApi: Int = getColumnIndexOrThrow(_stmt, "api")
        val _columnIndexOfDetail: Int = getColumnIndexOrThrow(_stmt, "detail")
        val _columnIndexOfIsAdult: Int = getColumnIndexOrThrow(_stmt, "isAdult")
        val _columnIndexOfIsEnabled: Int = getColumnIndexOrThrow(_stmt, "isEnabled")
        val _columnIndexOfIsCustom: Int = getColumnIndexOrThrow(_stmt, "isCustom")
        val _columnIndexOfOrder: Int = getColumnIndexOrThrow(_stmt, "order")
        val _result: MutableList<ApiSource> = mutableListOf()
        while (_stmt.step()) {
          val _item: ApiSource
          val _tmpCode: String
          _tmpCode = _stmt.getText(_columnIndexOfCode)
          val _tmpName: String
          _tmpName = _stmt.getText(_columnIndexOfName)
          val _tmpApi: String
          _tmpApi = _stmt.getText(_columnIndexOfApi)
          val _tmpDetail: String?
          if (_stmt.isNull(_columnIndexOfDetail)) {
            _tmpDetail = null
          } else {
            _tmpDetail = _stmt.getText(_columnIndexOfDetail)
          }
          val _tmpIsAdult: Boolean
          val _tmp: Int
          _tmp = _stmt.getLong(_columnIndexOfIsAdult).toInt()
          _tmpIsAdult = _tmp != 0
          val _tmpIsEnabled: Boolean
          val _tmp_1: Int
          _tmp_1 = _stmt.getLong(_columnIndexOfIsEnabled).toInt()
          _tmpIsEnabled = _tmp_1 != 0
          val _tmpIsCustom: Boolean
          val _tmp_2: Int
          _tmp_2 = _stmt.getLong(_columnIndexOfIsCustom).toInt()
          _tmpIsCustom = _tmp_2 != 0
          val _tmpOrder: Int
          _tmpOrder = _stmt.getLong(_columnIndexOfOrder).toInt()
          _item =
              ApiSource(_tmpCode,_tmpName,_tmpApi,_tmpDetail,_tmpIsAdult,_tmpIsEnabled,_tmpIsCustom,_tmpOrder)
          _result.add(_item)
        }
        _result
      } finally {
        _stmt.close()
      }
    }
  }

  public override fun getCustomApiSources(): Flow<List<ApiSource>> {
    val _sql: String = "SELECT * FROM api_sources WHERE isCustom = 1 ORDER BY `order` ASC, name ASC"
    return createFlow(__db, false, arrayOf("api_sources")) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        val _columnIndexOfCode: Int = getColumnIndexOrThrow(_stmt, "code")
        val _columnIndexOfName: Int = getColumnIndexOrThrow(_stmt, "name")
        val _columnIndexOfApi: Int = getColumnIndexOrThrow(_stmt, "api")
        val _columnIndexOfDetail: Int = getColumnIndexOrThrow(_stmt, "detail")
        val _columnIndexOfIsAdult: Int = getColumnIndexOrThrow(_stmt, "isAdult")
        val _columnIndexOfIsEnabled: Int = getColumnIndexOrThrow(_stmt, "isEnabled")
        val _columnIndexOfIsCustom: Int = getColumnIndexOrThrow(_stmt, "isCustom")
        val _columnIndexOfOrder: Int = getColumnIndexOrThrow(_stmt, "order")
        val _result: MutableList<ApiSource> = mutableListOf()
        while (_stmt.step()) {
          val _item: ApiSource
          val _tmpCode: String
          _tmpCode = _stmt.getText(_columnIndexOfCode)
          val _tmpName: String
          _tmpName = _stmt.getText(_columnIndexOfName)
          val _tmpApi: String
          _tmpApi = _stmt.getText(_columnIndexOfApi)
          val _tmpDetail: String?
          if (_stmt.isNull(_columnIndexOfDetail)) {
            _tmpDetail = null
          } else {
            _tmpDetail = _stmt.getText(_columnIndexOfDetail)
          }
          val _tmpIsAdult: Boolean
          val _tmp: Int
          _tmp = _stmt.getLong(_columnIndexOfIsAdult).toInt()
          _tmpIsAdult = _tmp != 0
          val _tmpIsEnabled: Boolean
          val _tmp_1: Int
          _tmp_1 = _stmt.getLong(_columnIndexOfIsEnabled).toInt()
          _tmpIsEnabled = _tmp_1 != 0
          val _tmpIsCustom: Boolean
          val _tmp_2: Int
          _tmp_2 = _stmt.getLong(_columnIndexOfIsCustom).toInt()
          _tmpIsCustom = _tmp_2 != 0
          val _tmpOrder: Int
          _tmpOrder = _stmt.getLong(_columnIndexOfOrder).toInt()
          _item =
              ApiSource(_tmpCode,_tmpName,_tmpApi,_tmpDetail,_tmpIsAdult,_tmpIsEnabled,_tmpIsCustom,_tmpOrder)
          _result.add(_item)
        }
        _result
      } finally {
        _stmt.close()
      }
    }
  }

  public override suspend fun getApiSourceByCode(code: String): ApiSource? {
    val _sql: String = "SELECT * FROM api_sources WHERE code = ?"
    return performSuspending(__db, true, false) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        var _argIndex: Int = 1
        _stmt.bindText(_argIndex, code)
        val _columnIndexOfCode: Int = getColumnIndexOrThrow(_stmt, "code")
        val _columnIndexOfName: Int = getColumnIndexOrThrow(_stmt, "name")
        val _columnIndexOfApi: Int = getColumnIndexOrThrow(_stmt, "api")
        val _columnIndexOfDetail: Int = getColumnIndexOrThrow(_stmt, "detail")
        val _columnIndexOfIsAdult: Int = getColumnIndexOrThrow(_stmt, "isAdult")
        val _columnIndexOfIsEnabled: Int = getColumnIndexOrThrow(_stmt, "isEnabled")
        val _columnIndexOfIsCustom: Int = getColumnIndexOrThrow(_stmt, "isCustom")
        val _columnIndexOfOrder: Int = getColumnIndexOrThrow(_stmt, "order")
        val _result: ApiSource?
        if (_stmt.step()) {
          val _tmpCode: String
          _tmpCode = _stmt.getText(_columnIndexOfCode)
          val _tmpName: String
          _tmpName = _stmt.getText(_columnIndexOfName)
          val _tmpApi: String
          _tmpApi = _stmt.getText(_columnIndexOfApi)
          val _tmpDetail: String?
          if (_stmt.isNull(_columnIndexOfDetail)) {
            _tmpDetail = null
          } else {
            _tmpDetail = _stmt.getText(_columnIndexOfDetail)
          }
          val _tmpIsAdult: Boolean
          val _tmp: Int
          _tmp = _stmt.getLong(_columnIndexOfIsAdult).toInt()
          _tmpIsAdult = _tmp != 0
          val _tmpIsEnabled: Boolean
          val _tmp_1: Int
          _tmp_1 = _stmt.getLong(_columnIndexOfIsEnabled).toInt()
          _tmpIsEnabled = _tmp_1 != 0
          val _tmpIsCustom: Boolean
          val _tmp_2: Int
          _tmp_2 = _stmt.getLong(_columnIndexOfIsCustom).toInt()
          _tmpIsCustom = _tmp_2 != 0
          val _tmpOrder: Int
          _tmpOrder = _stmt.getLong(_columnIndexOfOrder).toInt()
          _result =
              ApiSource(_tmpCode,_tmpName,_tmpApi,_tmpDetail,_tmpIsAdult,_tmpIsEnabled,_tmpIsCustom,_tmpOrder)
        } else {
          _result = null
        }
        _result
      } finally {
        _stmt.close()
      }
    }
  }

  public override suspend fun updateEnabledStatus(code: String, isEnabled: Boolean) {
    val _sql: String = "UPDATE api_sources SET isEnabled = ? WHERE code = ?"
    return performSuspending(__db, false, true) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        var _argIndex: Int = 1
        val _tmp: Int = if (isEnabled) 1 else 0
        _stmt.bindLong(_argIndex, _tmp.toLong())
        _argIndex = 2
        _stmt.bindText(_argIndex, code)
        _stmt.step()
      } finally {
        _stmt.close()
      }
    }
  }

  public override suspend fun updateOrder(code: String, order: Int) {
    val _sql: String = "UPDATE api_sources SET `order` = ? WHERE code = ?"
    return performSuspending(__db, false, true) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        var _argIndex: Int = 1
        _stmt.bindLong(_argIndex, order.toLong())
        _argIndex = 2
        _stmt.bindText(_argIndex, code)
        _stmt.step()
      } finally {
        _stmt.close()
      }
    }
  }

  public override suspend fun deleteAllCustomSources() {
    val _sql: String = "DELETE FROM api_sources WHERE isCustom = 1"
    return performSuspending(__db, false, true) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        _stmt.step()
      } finally {
        _stmt.close()
      }
    }
  }

  public companion object {
    public fun getRequiredConverters(): List<KClass<*>> = emptyList()
  }
}
