{"logs": [{"outputFile": "com.libretv.android.app-mergeReleaseResources-2:/values-hr/values-hr.xml", "map": [{"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\26497276d619c831fa389bb2f1588ec8\\transformed\\media3-exoplayer-1.7.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,191,256,329,408,481,566,648", "endColumns": "74,60,64,72,78,72,84,81,72", "endOffsets": "125,186,251,324,403,476,561,643,716"}, "to": {"startLines": "76,77,78,79,80,81,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "5889,5964,6025,6090,6163,6242,6315,6400,6482", "endColumns": "74,60,64,72,78,72,84,81,72", "endOffsets": "5959,6020,6085,6158,6237,6310,6395,6477,6550"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\406d1819cde1682167f3af9259cf68c0\\transformed\\ui-release\\res\\values-hr\\values-hr.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "196,301,388,482,581,671,750,843,938,1023,1104,1190,1263,1352,1429,1508,1585,1664,1734", "endColumns": "104,86,93,98,89,78,92,94,84,80,85,72,88,76,78,76,78,69,117", "endOffsets": "296,383,477,576,666,745,838,933,1018,1099,1185,1258,1347,1424,1503,1580,1659,1729,1847"}, "to": {"startLines": "29,30,31,33,34,102,103,167,168,169,170,171,172,173,174,175,177,178,179", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1730,1835,1922,2091,2190,7681,7760,14662,14757,14842,14923,15009,15082,15171,15248,15327,15505,15584,15654", "endColumns": "104,86,93,98,89,78,92,94,84,80,85,72,88,76,78,76,78,69,117", "endOffsets": "1830,1917,2011,2185,2275,7755,7848,14752,14837,14918,15004,15077,15166,15243,15322,15399,15579,15649,15767"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\6b27351f1a96970e2ba142187de324d4\\transformed\\media3-ui-1.7.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,295,585,864,945,1027,1107,1214,1321,1391,1458,1549,1641,1706,1777,1840,1912,2031,2155,2276,2344,2428,2499,2570,2674,2779,2846,2911,2964,3022,3070,3131,3205,3284,3360,3434,3498,3557,3628,3680,3743,3828,3913,3969", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "17,12,12,80,81,79,106,106,69,66,90,91,64,70,62,71,118,123,120,67,83,70,70,103,104,66,64,52,57,47,60,73,78,75,73,63,58,70,51,62,84,84,55,67", "endOffsets": "290,580,859,940,1022,1102,1209,1316,1386,1453,1544,1636,1701,1772,1835,1907,2026,2150,2271,2339,2423,2494,2565,2669,2774,2841,2906,2959,3017,3065,3126,3200,3279,3355,3429,3493,3552,3623,3675,3738,3823,3908,3964,4032"}, "to": {"startLines": "2,11,16,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,345,635,3842,3923,4005,4085,4192,4299,4369,4436,4527,4619,4684,4755,4818,4890,5009,5133,5254,5322,5406,5477,5548,5652,5757,5824,6555,6608,6666,6714,6775,6849,6928,7004,7078,7142,7201,7272,7324,7387,7472,7557,7613", "endLines": "10,15,20,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101", "endColumns": "17,12,12,80,81,79,106,106,69,66,90,91,64,70,62,71,118,123,120,67,83,70,70,103,104,66,64,52,57,47,60,73,78,75,73,63,58,70,51,62,84,84,55,67", "endOffsets": "340,630,909,3918,4000,4080,4187,4294,4364,4431,4522,4614,4679,4750,4813,4885,5004,5128,5249,5317,5401,5472,5543,5647,5752,5819,5884,6603,6661,6709,6770,6844,6923,6999,7073,7137,7196,7267,7319,7382,7467,7552,7608,7676"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\2425a4573e4fb65053b44e46c3806d87\\transformed\\foundation-release\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,145,228", "endColumns": "89,82,84", "endOffsets": "140,223,308"}, "to": {"startLines": "21,180,181", "startColumns": "4,4,4", "startOffsets": "914,15772,15855", "endColumns": "89,82,84", "endOffsets": "999,15850,15935"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\0b493fc5385f598ec7f47c0a10bc88bb\\transformed\\material3-release\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,296,416,537,637,731,842,983,1102,1247,1332,1432,1527,1625,1744,1870,1975,2111,2246,2380,2548,2674,2798,2926,3050,3146,3244,3374,3508,3605,3707,3816,3957,4104,4213,4313,4398,4491,4586,4699,4793,4879,4988,5076,5159,5256,5357,5450,5547,5635,5743,5840,5942,6080,6170,6270", "endColumns": "119,120,119,120,99,93,110,140,118,144,84,99,94,97,118,125,104,135,134,133,167,125,123,127,123,95,97,129,133,96,101,108,140,146,108,99,84,92,94,112,93,85,108,87,82,96,100,92,96,87,107,96,101,137,89,99,91", "endOffsets": "170,291,411,532,632,726,837,978,1097,1242,1327,1427,1522,1620,1739,1865,1970,2106,2241,2375,2543,2669,2793,2921,3045,3141,3239,3369,3503,3600,3702,3811,3952,4099,4208,4308,4393,4486,4581,4694,4788,4874,4983,5071,5154,5251,5352,5445,5542,5630,5738,5835,5937,6075,6165,6265,6357"}, "to": {"startLines": "104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7853,7973,8094,8214,8335,8435,8529,8640,8781,8900,9045,9130,9230,9325,9423,9542,9668,9773,9909,10044,10178,10346,10472,10596,10724,10848,10944,11042,11172,11306,11403,11505,11614,11755,11902,12011,12111,12196,12289,12384,12497,12591,12677,12786,12874,12957,13054,13155,13248,13345,13433,13541,13638,13740,13878,13968,14068", "endColumns": "119,120,119,120,99,93,110,140,118,144,84,99,94,97,118,125,104,135,134,133,167,125,123,127,123,95,97,129,133,96,101,108,140,146,108,99,84,92,94,112,93,85,108,87,82,96,100,92,96,87,107,96,101,137,89,99,91", "endOffsets": "7968,8089,8209,8330,8430,8524,8635,8776,8895,9040,9125,9225,9320,9418,9537,9663,9768,9904,10039,10173,10341,10467,10591,10719,10843,10939,11037,11167,11301,11398,11500,11609,11750,11897,12006,12106,12191,12284,12379,12492,12586,12672,12781,12869,12952,13049,13150,13243,13340,13428,13536,13633,13735,13873,13963,14063,14155"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\4c56c0fb8a9fdb1c153785f7a46f8077\\transformed\\media3-session-1.7.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,234,308,401,498,606,695,784,885,973,1050,1148,1237,1331,1410,1516,1598,1692,1763,1837,1916,2005,2097", "endColumns": "74,103,73,92,96,107,88,88,100,87,76,97,88,93,78,105,81,93,70,73,78,88,91,96", "endOffsets": "125,229,303,396,493,601,690,779,880,968,1045,1143,1232,1326,1405,1511,1593,1687,1758,1832,1911,2000,2092,2189"}, "to": {"startLines": "32,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,161,162,163,164,165,166", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2016,2280,2384,2458,2551,2648,2756,2845,2934,3035,3123,3200,3298,3387,3481,3560,3666,3748,14160,14231,14305,14384,14473,14565", "endColumns": "74,103,73,92,96,107,88,88,100,87,76,97,88,93,78,105,81,93,70,73,78,88,91,96", "endOffsets": "2086,2379,2453,2546,2643,2751,2840,2929,3030,3118,3195,3293,3382,3476,3555,3661,3743,3837,14226,14300,14379,14468,14560,14657"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\955c05c442e591726e4833ad1e0fc303\\transformed\\core-1.16.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,260,357,456,560,664,781", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "148,255,352,451,555,659,776,877"}, "to": {"startLines": "22,23,24,25,26,27,28,176", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "1004,1102,1209,1306,1405,1509,1613,15404", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "1097,1204,1301,1400,1504,1608,1725,15500"}}]}]}