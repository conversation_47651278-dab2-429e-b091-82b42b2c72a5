{"logs": [{"outputFile": "com.libretv.android.app-mergeDebugResources-2:/values/values.xml", "map": [{"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\6b27351f1a96970e2ba142187de324d4\\transformed\\media3-ui-1.7.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,11,12,13,14,19,20,21,25,26,27,28,29,30,31,32,33,34,38,40,41,42,43,44,45,47,49,50,51,52,57,64,65,66,67,68,69,70,75,76,77,78,79,80,81,82,83,84,85,87,88,89,96,97,98,99,100,101,112,113,114,115,116,117,118,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,384,385,388,392,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,612,617,621,625,629,633,637,641,645,646,652,663,667,671,675,679,683,687,691,695,699,703,707,720,725,730,735,748,756,766,770,774", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,380,430,484,538,701,747,796,922,971,1020,1079,1133,1188,1248,1307,1359,1409,1537,1662,1710,1759,1807,1864,1911,2026,2134,2188,2242,2296,2444,2682,2732,2781,2842,2902,2958,3018,3188,3248,3301,3358,3413,3469,3526,3575,3626,3681,3735,3853,3909,3964,4251,4316,4374,4423,4471,4522,5217,5274,5331,5393,5460,5532,5576,7540,7596,7659,7732,7802,7861,7918,7965,8020,8065,8114,8169,8223,8273,8324,8378,8437,8487,8545,8601,8654,8717,8782,8845,8897,8957,9021,9087,9145,9217,9278,9348,9418,9483,9548,11497,11592,11697,11800,11881,11964,12045,12134,12227,12320,12413,12498,12593,12686,12763,12855,12933,13013,13091,13177,13259,13352,13430,13521,13602,13691,13794,13895,13979,14075,14172,14267,14360,14452,14545,14638,14731,14814,14901,14996,15089,15191,15283,15364,15459,15552,18088,18132,18173,18218,18266,18310,18353,18402,18449,18493,18549,18602,18644,18691,18739,18799,18837,18887,18931,18970,19020,19072,19110,19157,19204,19245,19284,19322,19366,19414,19456,19494,19536,19590,19637,19674,19723,19765,19806,19847,19889,19932,19970,22182,22260,22481,22778,25723,25805,25887,26029,26107,26194,26279,26346,26409,26501,26593,26658,26721,26783,26854,26964,27075,27185,27252,27332,27403,27470,27555,27640,27703,27791,28501,28643,28743,28791,28934,28997,29059,29124,29195,29253,29311,29377,29429,29491,29567,29643,29697,38036,38315,38546,38756,38969,39179,39401,39617,39821,39859,40213,41000,41241,41481,41738,41991,42244,42479,42726,42965,43209,43430,43625,44297,44588,44884,45187,45853,46387,46861,47072,47272", "endLines": "10,11,12,13,18,19,20,24,25,26,27,28,29,30,31,32,33,37,38,40,41,42,43,44,45,47,49,50,51,56,63,64,65,66,67,68,69,74,75,76,77,78,79,80,81,82,83,84,85,87,88,95,96,97,98,99,100,101,112,113,114,115,116,117,118,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,384,385,391,395,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,468,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484,616,620,624,628,632,636,640,644,645,651,662,666,670,674,678,682,686,690,694,698,702,706,719,724,729,734,747,755,765,769,773,777", "endColumns": "17,49,53,53,9,45,48,9,48,48,58,53,54,59,58,51,49,9,64,47,48,47,56,46,54,51,53,53,53,9,9,49,48,60,59,55,59,9,59,52,56,54,55,56,48,50,54,53,58,55,54,9,64,57,48,47,50,45,56,56,61,66,71,43,56,55,62,72,69,58,56,46,54,44,48,54,53,49,50,53,58,49,57,55,52,62,64,62,51,59,63,65,57,71,60,69,69,64,64,70,94,104,102,80,82,80,88,92,92,92,84,94,92,76,91,77,79,77,85,81,92,77,90,80,88,102,100,83,95,96,94,92,91,92,92,92,82,86,94,92,101,91,80,94,92,76,43,40,44,47,43,42,48,46,43,55,52,41,46,47,59,37,49,43,38,49,51,37,46,46,40,38,37,43,47,41,37,41,53,46,36,48,41,40,40,41,42,37,35,77,77,12,12,81,81,141,77,86,84,66,62,91,91,64,62,61,70,109,110,109,66,79,70,66,84,84,62,87,63,141,99,47,142,62,61,64,70,57,57,65,51,61,75,75,53,112,10,10,10,10,10,10,10,10,37,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10", "endOffsets": "375,425,479,533,696,742,791,917,966,1015,1074,1128,1183,1243,1302,1354,1404,1532,1597,1705,1754,1802,1859,1906,1961,2073,2183,2237,2291,2439,2677,2727,2776,2837,2897,2953,3013,3183,3243,3296,3353,3408,3464,3521,3570,3621,3676,3730,3789,3904,3959,4246,4311,4369,4418,4466,4517,4563,5269,5326,5388,5455,5527,5571,5628,7591,7654,7727,7797,7856,7913,7960,8015,8060,8109,8164,8218,8268,8319,8373,8432,8482,8540,8596,8649,8712,8777,8840,8892,8952,9016,9082,9140,9212,9273,9343,9413,9478,9543,9614,11587,11692,11795,11876,11959,12040,12129,12222,12315,12408,12493,12588,12681,12758,12850,12928,13008,13086,13172,13254,13347,13425,13516,13597,13686,13789,13890,13974,14070,14167,14262,14355,14447,14540,14633,14726,14809,14896,14991,15084,15186,15278,15359,15454,15547,15624,18127,18168,18213,18261,18305,18348,18397,18444,18488,18544,18597,18639,18686,18734,18794,18832,18882,18926,18965,19015,19067,19105,19152,19199,19240,19279,19317,19361,19409,19451,19489,19531,19585,19632,19669,19718,19760,19801,19842,19884,19927,19965,20001,22255,22333,22773,23043,25800,25882,26024,26102,26189,26274,26341,26404,26496,26588,26653,26716,26778,26849,26959,27070,27180,27247,27327,27398,27465,27550,27635,27698,27786,27850,28638,28738,28786,28929,28992,29054,29119,29190,29248,29306,29372,29424,29486,29562,29638,29692,29805,38310,38541,38751,38964,39174,39396,39612,39816,39854,40208,40995,41236,41476,41733,41986,42239,42474,42721,42960,43204,43425,43620,44292,44583,44879,45182,45848,46382,46856,47067,47267,47443"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\2425a4573e4fb65053b44e46c3806d87\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "398,587,588", "startColumns": "4,4,4", "startOffsets": "23176,36449,36505", "endColumns": "45,55,54", "endOffsets": "23217,36500,36555"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\ecf6ab9b3b3e13c2912b30b1e461ce38\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "376", "startColumns": "4", "startOffsets": "21730", "endColumns": "42", "endOffsets": "21768"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\fe28f0070187b5cca4efcaab56677fb2\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "348,354", "startColumns": "4,4", "startOffsets": "20235,20540", "endColumns": "53,66", "endOffsets": "20284,20602"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\e8e3d02eb8e4113eee298889264c6369\\transformed\\activity-1.10.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "355,377", "startColumns": "4,4", "startOffsets": "20607,21773", "endColumns": "41,59", "endOffsets": "20644,21828"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\26497276d619c831fa389bb2f1588ec8\\transformed\\media3-exoplayer-1.7.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "459,460,461,462,463,464,465,466,467", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "27855,27925,27987,28052,28116,28193,28258,28348,28432", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "27920,27982,28047,28111,28188,28253,28343,28427,28496"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\fc417f0dcf28f5cd08e849c99534415e\\transformed\\fragment-1.5.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "344,356,380", "startColumns": "4,4,4", "startOffsets": "20006,20649,21937", "endColumns": "56,64,63", "endOffsets": "20058,20709,21996"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\7fcb9610de1d6578fc689f0d62ec1e8a\\transformed\\savedstate-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "378", "startColumns": "4", "startOffsets": "21833", "endColumns": "53", "endOffsets": "21882"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\09378dcba741b68faa4989a5c91d6bd5\\transformed\\recyclerview-1.3.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "48,187,188,189,190,191,192,349", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2078,9619,9678,9726,9782,9857,9933,20289", "endColumns": "55,58,47,55,74,75,71,65", "endOffsets": "2129,9673,9721,9777,9852,9928,10000,20350"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\ae738502719aa702fcbb63f9de86724f\\transformed\\coil-base-2.7.0\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "298", "startColumns": "4", "startOffsets": "17925", "endColumns": "49", "endOffsets": "17970"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\61e78a9aa048918f3a91576673b36af5\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "396", "startColumns": "4", "startOffsets": "23048", "endColumns": "82", "endOffsets": "23126"}}, {"source": "C:\\Users\\<USER>\\StudioProject\\shipin\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "397,406,407,413,415,487,556,557,558,559,562,563,564,565,568,569,570,571,573,574,575,576,577,583", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "23131,23733,23771,24168,24264,29922,34938,34981,35021,35063,35223,35257,35303,35353,35501,35538,35584,35636,35737,35783,35831,35881,35933,36237", "endColumns": "44,37,36,37,36,42,42,39,41,43,33,45,49,43,36,45,51,54,45,47,49,51,50,38", "endOffsets": "23171,23766,23803,24201,24296,29960,34976,35016,35058,35102,35252,35298,35348,35392,35533,35579,35631,35686,35778,35826,35876,35928,35979,36271"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\3bf0e9913a8fb17f961a2eb6334c8d97\\transformed\\material-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "549", "startColumns": "4", "startOffsets": "34406", "endColumns": "57", "endOffsets": "34459"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\abbd8e9524afba48716f5430cec85e10\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "345", "startColumns": "4", "startOffsets": "20063", "endColumns": "65", "endOffsets": "20124"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\406d1819cde1682167f3af9259cf68c0\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "297,299,300,346,347,381,408,409,410,412,414,485,486,560,561,566,567,572,578,579,580,581,584,585,586,596,778,781", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "17851,17975,18033,20129,20180,22001,23808,23873,23927,24067,24206,29810,29862,35107,35169,35397,35447,35691,35984,36038,36084,36126,36276,36323,36359,37056,47448,47559", "endLines": "297,299,300,346,347,381,408,409,410,412,414,485,486,560,561,566,567,572,578,579,580,581,584,585,586,598,780,785", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,53,45,41,39,46,35,89,12,12,12", "endOffsets": "17920,18028,18083,20175,20230,22049,23868,23922,23988,24163,24259,29857,29917,35164,35218,35442,35496,35732,36033,36079,36121,36161,36318,36354,36444,37163,47554,47811"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\955c05c442e591726e4833ad1e0fc303\\transformed\\core-1.16.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "39,106,107,109,110,129,130,145,146,147,148,149,150,151,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,350,351,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,387,399,400,401,402,403,404,405,582,786,787,792,795,800,838,839", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1602,4844,4916,5046,5111,6197,6266,7052,7122,7190,7262,7332,7393,7467,10005,10066,10127,10189,10253,10315,10376,10444,10544,10604,10670,10743,10812,10869,10921,15728,15800,15876,15941,16000,16059,16119,16179,16239,16299,16359,16419,16479,16539,16599,16659,16718,16778,16838,16898,16958,17018,17078,17138,17198,17258,17318,17377,17437,17497,17556,17615,17674,17733,17792,20355,20390,20714,20769,20832,20887,20945,21001,21059,21120,21183,21240,21291,21349,21399,21460,21517,21583,21617,21652,22411,23222,23289,23361,23430,23499,23573,23645,36166,47816,47933,48200,48493,48760,50906,50978", "endLines": "39,106,107,109,110,129,130,145,146,147,148,149,150,151,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,350,351,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,387,399,400,401,402,403,404,405,582,786,790,792,798,800,838,839", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66", "endOffsets": "1657,4911,4999,5106,5172,6261,6324,7117,7185,7257,7327,7388,7462,7535,10061,10122,10184,10248,10310,10371,10439,10539,10599,10665,10738,10807,10864,10916,10978,15795,15871,15936,15995,16054,16114,16174,16234,16294,16354,16414,16474,16534,16594,16654,16713,16773,16833,16893,16953,17013,17073,17133,17193,17253,17313,17372,17432,17492,17551,17610,17669,17728,17787,17846,20385,20420,20764,20827,20882,20940,20996,21054,21115,21178,21235,21286,21344,21394,21455,21512,21578,21612,21647,21682,22476,23284,23356,23425,23494,23568,23640,23728,36232,47928,48129,48305,48689,48884,50973,51040"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\4c56c0fb8a9fdb1c153785f7a46f8077\\transformed\\media3-session-1.7.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "131,135,139,261,352,382,411,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,550,551,552,553,554,555,791,793,794,799,801", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6329,6551,6763,15629,20425,22054,23993,24301,24390,24463,24559,24654,24737,24821,24902,24987,25067,25131,25225,25304,25398,25472,25563,25635,34464,34532,34598,34674,34756,34843,48134,48310,48432,48694,48889", "endColumns": "88,70,72,98,61,59,73,88,72,95,94,82,83,80,84,79,63,93,78,93,73,90,71,87,67,65,75,81,86,94,65,121,60,65,66", "endOffsets": "6413,6617,6831,15723,20482,22109,24062,24385,24458,24554,24649,24732,24816,24897,24982,25062,25126,25220,25299,25393,25467,25558,25630,25718,34527,34593,34669,34751,34838,34933,48195,48427,48488,48755,48951"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\80630b05ea7fb0c577cb3dfcc6eeb05c\\transformed\\core-splashscreen-1.0.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "46,86,102,103,104,105,208,209,210,211,212,213,214,383,589,590,591,592,594,812,821,834", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1966,3794,4568,4637,4709,4772,10983,11057,11133,11209,11286,11357,11426,22114,36560,36641,36733,36826,36935,49398,49858,50633", "endLines": "46,86,102,103,104,105,208,209,210,211,212,213,214,383,589,590,591,593,595,820,833,837", "endColumns": "59,58,68,71,62,71,73,75,75,76,70,68,70,67,80,91,92,12,12,12,12,12", "endOffsets": "2021,3848,4632,4704,4767,4839,11052,11128,11204,11281,11352,11421,11492,22177,36636,36728,36821,36930,37051,49853,50628,50901"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\44c4165ba6d998610d7f987a40811eff\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "379", "startColumns": "4", "startOffsets": "21887", "endColumns": "49", "endOffsets": "21932"}}, {"source": "C:\\Users\\<USER>\\StudioProject\\shipin\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3,9", "startColumns": "4,4", "startOffsets": "78,289", "endLines": "6,14", "endColumns": "12,12", "endOffsets": "264,561"}, "to": {"startLines": "802,806", "startColumns": "4,4", "startOffsets": "48956,49135", "endLines": "805,811", "endColumns": "12,12", "endOffsets": "49130,49393"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\0b493fc5385f598ec7f47c0a10bc88bb\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "386,488,489,490,491,492,493,494,495,496,497,500,501,502,503,504,505,506,507,508,509,510,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,599,609", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "22338,29965,30053,30139,30220,30304,30373,30438,30521,30627,30713,30833,30887,30956,31017,31086,31175,31270,31344,31441,31534,31632,31781,31872,31960,32056,32154,32218,32286,32373,32467,32534,32606,32678,32779,32888,32964,33033,33081,33147,33211,33285,33342,33399,33471,33521,33575,33646,33717,33787,33856,33914,33990,34061,34135,34221,34271,34341,37168,37883", "endLines": "386,488,489,490,491,492,493,494,495,496,499,500,501,502,503,504,505,506,507,508,509,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,608,611", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "22406,30048,30134,30215,30299,30368,30433,30516,30622,30708,30828,30882,30951,31012,31081,31170,31265,31339,31436,31529,31627,31776,31867,31955,32051,32149,32213,32281,32368,32462,32529,32601,32673,32774,32883,32959,33028,33076,33142,33206,33280,33337,33394,33466,33516,33570,33641,33712,33782,33851,33909,33985,34056,34130,34216,34266,34336,34401,37878,38031"}}, {"source": "C:\\Users\\<USER>\\StudioProject\\shipin\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "108,111,119,120,121,122,123,124,125,126,127,128,132,133,134,136,137,138,140,141,142,143,144", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5004,5177,5633,5686,5742,5805,5870,5923,5973,6033,6085,6147,6418,6461,6506,6622,6669,6716,6836,6878,6923,6968,7010", "endColumns": "41,39,52,55,62,64,52,49,59,51,61,49,42,44,44,46,46,46,41,44,44,41,41", "endOffsets": "5041,5212,5681,5737,5800,5865,5918,5968,6028,6080,6142,6192,6456,6501,6546,6664,6711,6758,6873,6918,6963,7005,7047"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\1fa1c77ec5ce78b692b7b2587ffef552\\transformed\\core-viewtree-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "375", "startColumns": "4", "startOffsets": "21687", "endColumns": "42", "endOffsets": "21725"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\01dc5809f9f3fc5db6925fb2fcbfb1ca\\transformed\\navigation-runtime-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "353", "startColumns": "4", "startOffsets": "20487", "endColumns": "52", "endOffsets": "20535"}}]}, {"outputFile": "D:\\SDK\\.gradle\\daemon\\8.11.1\\com.libretv.android.app-mergeDebugResources-2:\\values\\values.xml", "map": [{"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\2425a4573e4fb65053b44e46c3806d87\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,101,157", "endColumns": "45,55,54", "endOffsets": "96,152,207"}, "to": {"startLines": "382,548,549", "startColumns": "4,4,4", "startOffsets": "22354,34615,34671", "endColumns": "45,55,54", "endOffsets": "22395,34666,34721"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\ecf6ab9b3b3e13c2912b30b1e461ce38\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "360", "startColumns": "4", "startOffsets": "20909", "endColumns": "42", "endOffsets": "20947"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\e8e3d02eb8e4113eee298889264c6369\\transformed\\activity-1.10.1\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "339,361", "startColumns": "4,4", "startOffsets": "19786,20952", "endColumns": "41,59", "endOffsets": "19823,21007"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\09378dcba741b68faa4989a5c91d6bd5\\transformed\\recyclerview-1.3.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,111,170,218,274,349,425,497,563", "endLines": "2,3,4,5,6,7,8,9,30", "endColumns": "55,58,47,55,74,75,71,65,24", "endOffsets": "106,165,213,269,344,420,492,558,1398"}, "to": {"startLines": "48,171,172,173,174,175,176,333,1137", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "2078,8798,8857,8905,8961,9036,9112,19468,61042", "endLines": "48,171,172,173,174,175,176,333,1157", "endColumns": "55,58,47,55,74,75,71,65,24", "endOffsets": "2129,8852,8900,8956,9031,9107,9179,19529,61877"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\abbd8e9524afba48716f5430cec85e10\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}, "to": {"startLines": "329", "startColumns": "4", "startOffsets": "19242", "endColumns": "65", "endOffsets": "19303"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\406d1819cde1682167f3af9259cf68c0\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "34,35,36,37,38,39,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,63,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2060,2134,2192,2247,2298,2353,2452,2517,2571,2637,2738,2796,2848,2908,2970,3024,3074,3128,3174,3228,3274,3316,3356,3403,3439,3529,3641,3752", "endLines": "34,35,36,37,38,39,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,62,65,70", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,53,45,41,39,46,35,89,12,12,12", "endOffsets": "2129,2187,2242,2293,2348,2401,2512,2566,2632,2733,2791,2843,2903,2965,3019,3069,3123,3169,3223,3269,3311,3351,3398,3434,3524,3636,3747,4004"}, "to": {"startLines": "281,283,284,330,331,365,390,391,392,394,395,465,466,535,536,537,538,539,540,541,542,543,545,546,547,557,739,742", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "17030,17154,17212,19308,19359,21180,22911,22976,23030,23170,23271,28838,28890,33923,33985,34039,34089,34143,34189,34243,34289,34331,34442,34489,34525,35222,45614,45725", "endLines": "281,283,284,330,331,365,390,391,392,394,395,465,466,535,536,537,538,539,540,541,542,543,545,546,547,559,741,746", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,53,45,41,39,46,35,89,12,12,12", "endOffsets": "17099,17207,17262,19354,19409,21228,22971,23025,23091,23266,23324,28885,28945,33980,34034,34084,34138,34184,34238,34284,34326,34366,34484,34520,34610,35329,45720,45977"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\955c05c442e591726e4833ad1e0fc303\\transformed\\core-1.16.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,100,101,105,106,107,108,114,124,159,180,213", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4484,4542,4603,4666,4723,4774,4832,4882,4943,5000,5066,5100,5135,5170,5240,5307,5379,5448,5517,5591,5663,5751,5822,5939,6140,6250,6451,6580,6652,6719,6922,7223,9029,9710,10392", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,99,100,104,105,106,107,113,123,158,179,212,218", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4479,4537,4598,4661,4718,4769,4827,4877,4938,4995,5061,5095,5130,5165,5235,5302,5374,5443,5512,5586,5658,5746,5817,5934,6135,6245,6446,6575,6647,6714,6917,7218,9024,9705,10387,10554"}, "to": {"startLines": "39,106,107,109,110,118,119,129,130,131,132,133,134,135,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,334,335,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,371,383,384,385,386,387,388,389,544,747,748,753,756,761,790,791,834,840,866,901,931,964", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1602,4844,4916,5046,5111,5593,5662,6231,6301,6369,6441,6511,6572,6646,9184,9245,9306,9368,9432,9494,9555,9623,9723,9783,9849,9922,9991,10048,10100,14907,14979,15055,15120,15179,15238,15298,15358,15418,15478,15538,15598,15658,15718,15778,15838,15897,15957,16017,16077,16137,16197,16257,16317,16377,16437,16497,16556,16616,16676,16735,16794,16853,16912,16971,19534,19569,19893,19948,20011,20066,20124,20180,20238,20299,20362,20419,20470,20528,20578,20639,20696,20762,20796,20831,21590,22400,22467,22539,22608,22677,22751,22823,34371,45982,46099,46366,46659,46926,48713,48785,50151,50354,51231,53037,54037,54719", "endLines": "39,106,107,109,110,118,119,129,130,131,132,133,134,135,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,334,335,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,371,383,384,385,386,387,388,389,544,747,751,753,759,761,790,791,839,849,900,921,963,969", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "1657,4911,4999,5106,5172,5657,5720,6296,6364,6436,6506,6567,6641,6714,9240,9301,9363,9427,9489,9550,9618,9718,9778,9844,9917,9986,10043,10095,10157,14974,15050,15115,15174,15233,15293,15353,15413,15473,15533,15593,15653,15713,15773,15833,15892,15952,16012,16072,16132,16192,16252,16312,16372,16432,16492,16551,16611,16671,16730,16789,16848,16907,16966,17025,19564,19599,19943,20006,20061,20119,20175,20233,20294,20357,20414,20465,20523,20573,20634,20691,20757,20791,20826,20861,21655,22462,22534,22603,22672,22746,22818,22906,34437,46094,46295,46471,46855,47050,48780,48847,50349,50650,53032,53713,54714,54881"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\44c4165ba6d998610d7f987a40811eff\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "363", "startColumns": "4", "startOffsets": "21066", "endColumns": "49", "endOffsets": "21111"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\b019d1ca88646cc0c0d931e66d340927\\transformed\\appcompat-resources-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "799,815,821,1158,1174", "startColumns": "4,4,4,4,4", "startOffsets": "49156,49581,49759,61882,62293", "endLines": "814,820,830,1173,1177", "endColumns": "24,24,24,24,24", "endOffsets": "49576,49754,50038,62288,62415"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\0b493fc5385f598ec7f47c0a10bc88bb\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,15,16,17,18,19,20,21,22,23,24,25,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,173,261,347,428,512,581,646,729,835,921,1041,1095,1164,1225,1294,1383,1478,1552,1649,1742,1840,1989,2080,2168,2264,2362,2426,2494,2581,2675,2742,2814,2886,2987,3096,3172,3241,3289,3355,3419,3493,3550,3607,3679,3729,3783,3854,3925,3995,4064,4122,4198,4269,4343,4429,4479,4549,4614,5329", "endLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,73,76", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "168,256,342,423,507,576,641,724,830,916,1036,1090,1159,1220,1289,1378,1473,1547,1644,1737,1835,1984,2075,2163,2259,2357,2421,2489,2576,2670,2737,2809,2881,2982,3091,3167,3236,3284,3350,3414,3488,3545,3602,3674,3724,3778,3849,3920,3990,4059,4117,4193,4264,4338,4424,4474,4544,4609,5324,5477"}, "to": {"startLines": "370,467,468,469,470,471,472,473,474,475,476,479,480,481,482,483,484,485,486,487,488,489,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,560,570", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "21517,28950,29038,29124,29205,29289,29358,29423,29506,29612,29698,29818,29872,29941,30002,30071,30160,30255,30329,30426,30519,30617,30766,30857,30945,31041,31139,31203,31271,31358,31452,31519,31591,31663,31764,31873,31949,32018,32066,32132,32196,32270,32327,32384,32456,32506,32560,32631,32702,32772,32841,32899,32975,33046,33120,33206,33256,33326,35334,36049", "endLines": "370,467,468,469,470,471,472,473,474,475,478,479,480,481,482,483,484,485,486,487,488,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,569,572", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "21585,29033,29119,29200,29284,29353,29418,29501,29607,29693,29813,29867,29936,29997,30066,30155,30250,30324,30421,30514,30612,30761,30852,30940,31036,31134,31198,31266,31353,31447,31514,31586,31658,31759,31868,31944,32013,32061,32127,32191,32265,32322,32379,32451,32501,32555,32626,32697,32767,32836,32894,32970,33041,33115,33201,33251,33321,33386,36044,36197"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\dab4f5d0853d53eccf431f943f79567f\\transformed\\navigation-common-release\\res\\values\\values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "996,1009,1015,1021,1030", "startColumns": "4,4,4,4,4", "startOffsets": "55831,56470,56714,56961,57324", "endLines": "1008,1014,1020,1023,1034", "endColumns": "24,24,24,24,24", "endOffsets": "56465,56709,56956,57089,57501"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\6b27351f1a96970e2ba142187de324d4\\transformed\\media3-ui-1.7.1\\res\\values\\values.xml", "from": {"startLines": "2,11,12,13,14,19,20,21,25,26,27,28,29,30,31,32,33,34,38,39,40,41,42,43,44,45,46,47,48,49,54,61,62,63,64,65,66,67,72,73,74,75,76,77,78,79,80,81,82,83,84,85,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,235,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,287,291,295,299,303,307,311,315,316,322,333,337,341,345,349,353,357,361,365,369,373,377,390,395,400,405,418,426,436,440,444,448,451,467,493,538", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,385,439,493,656,702,751,877,926,975,1034,1088,1143,1203,1262,1314,1364,1492,1557,1605,1654,1702,1759,1806,1861,1913,1967,2021,2075,2223,2461,2511,2560,2621,2681,2737,2797,2967,3027,3080,3137,3192,3248,3305,3354,3405,3460,3514,3573,3629,3684,3971,4036,4094,4143,4191,4242,4288,4345,4402,4464,4531,4603,4647,4704,4760,4823,4896,4966,5025,5082,5129,5184,5229,5278,5333,5387,5437,5488,5542,5601,5651,5709,5765,5818,5881,5946,6009,6061,6121,6185,6251,6309,6381,6442,6512,6582,6647,6712,6783,6878,6983,7086,7167,7250,7331,7420,7513,7606,7699,7784,7879,7972,8049,8141,8219,8299,8377,8463,8545,8638,8716,8807,8888,8977,9080,9181,9265,9361,9458,9553,9646,9738,9831,9924,10017,10100,10187,10282,10375,10477,10569,10650,10745,10838,10915,10959,11000,11045,11093,11137,11180,11229,11276,11320,11376,11429,11471,11518,11566,11626,11664,11714,11758,11797,11847,11899,11937,11984,12031,12072,12111,12149,12193,12241,12283,12321,12363,12417,12464,12501,12550,12592,12633,12674,12716,12759,12797,12833,12911,12989,13286,13556,13638,13720,13862,13940,14027,14112,14179,14242,14334,14426,14491,14554,14616,14687,14797,14908,15018,15085,15165,15236,15303,15388,15473,15536,15624,15688,15830,15930,15978,16121,16184,16246,16311,16382,16440,16498,16564,16616,16678,16754,16830,16884,16997,17276,17507,17717,17930,18140,18362,18578,18782,18820,19174,19961,20202,20442,20699,20952,21205,21440,21687,21926,22170,22391,22586,23258,23549,23845,24148,24814,25348,25822,26033,26233,26409,26517,27093,28038,29633", "endLines": "10,11,12,13,18,19,20,24,25,26,27,28,29,30,31,32,33,37,38,39,40,41,42,43,44,45,46,47,48,53,60,61,62,63,64,65,66,71,72,73,74,75,76,77,78,79,80,81,82,83,84,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,234,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,286,290,294,298,302,306,310,314,315,321,332,336,340,344,348,352,356,360,364,368,372,376,389,394,399,404,417,425,435,439,443,447,450,466,492,537,594", "endColumns": "17,49,53,53,9,45,48,9,48,48,58,53,54,59,58,51,49,9,64,47,48,47,56,46,54,51,53,53,53,9,9,49,48,60,59,55,59,9,59,52,56,54,55,56,48,50,54,53,58,55,54,9,64,57,48,47,50,45,56,56,61,66,71,43,56,55,62,72,69,58,56,46,54,44,48,54,53,49,50,53,58,49,57,55,52,62,64,62,51,59,63,65,57,71,60,69,69,64,64,70,94,104,102,80,82,80,88,92,92,92,84,94,92,76,91,77,79,77,85,81,92,77,90,80,88,102,100,83,95,96,94,92,91,92,92,92,82,86,94,92,101,91,80,94,92,76,43,40,44,47,43,42,48,46,43,55,52,41,46,47,59,37,49,43,38,49,51,37,46,46,40,38,37,43,47,41,37,41,53,46,36,48,41,40,40,41,42,37,35,77,77,12,12,81,81,141,77,86,84,66,62,91,91,64,62,61,70,109,110,109,66,79,70,66,84,84,62,87,63,141,99,47,142,62,61,64,70,57,57,65,51,61,75,75,53,112,10,10,10,10,10,10,10,10,37,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,22,22,22,22,22", "endOffsets": "330,380,434,488,651,697,746,872,921,970,1029,1083,1138,1198,1257,1309,1359,1487,1552,1600,1649,1697,1754,1801,1856,1908,1962,2016,2070,2218,2456,2506,2555,2616,2676,2732,2792,2962,3022,3075,3132,3187,3243,3300,3349,3400,3455,3509,3568,3624,3679,3966,4031,4089,4138,4186,4237,4283,4340,4397,4459,4526,4598,4642,4699,4755,4818,4891,4961,5020,5077,5124,5179,5224,5273,5328,5382,5432,5483,5537,5596,5646,5704,5760,5813,5876,5941,6004,6056,6116,6180,6246,6304,6376,6437,6507,6577,6642,6707,6778,6873,6978,7081,7162,7245,7326,7415,7508,7601,7694,7779,7874,7967,8044,8136,8214,8294,8372,8458,8540,8633,8711,8802,8883,8972,9075,9176,9260,9356,9453,9548,9641,9733,9826,9919,10012,10095,10182,10277,10370,10472,10564,10645,10740,10833,10910,10954,10995,11040,11088,11132,11175,11224,11271,11315,11371,11424,11466,11513,11561,11621,11659,11709,11753,11792,11842,11894,11932,11979,12026,12067,12106,12144,12188,12236,12278,12316,12358,12412,12459,12496,12545,12587,12628,12669,12711,12754,12792,12828,12906,12984,13281,13551,13633,13715,13857,13935,14022,14107,14174,14237,14329,14421,14486,14549,14611,14682,14792,14903,15013,15080,15160,15231,15298,15383,15468,15531,15619,15683,15825,15925,15973,16116,16179,16241,16306,16377,16435,16493,16559,16611,16673,16749,16825,16879,16992,17271,17502,17712,17925,18135,18357,18573,18777,18815,19169,19956,20197,20437,20694,20947,21200,21435,21682,21921,22165,22386,22581,23253,23544,23840,24143,24809,25343,25817,26028,26228,26404,26512,27088,28033,29628,31569"}, "to": {"startLines": "2,11,12,13,14,19,20,21,25,26,27,28,29,30,31,32,33,34,38,40,41,42,43,44,45,47,49,50,51,52,57,64,65,66,67,68,69,70,75,76,77,78,79,80,81,82,83,84,85,87,88,89,96,97,98,99,100,101,111,112,113,114,115,116,117,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,368,369,372,376,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,573,578,582,586,590,594,598,602,606,607,613,624,628,632,636,640,644,648,652,656,660,664,668,681,686,691,696,709,717,727,731,735,831,850,970,1035,1080", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,380,430,484,538,701,747,796,922,971,1020,1079,1133,1188,1248,1307,1359,1409,1537,1662,1710,1759,1807,1864,1911,2026,2134,2188,2242,2296,2444,2682,2732,2781,2842,2902,2958,3018,3188,3248,3301,3358,3413,3469,3526,3575,3626,3681,3735,3853,3909,3964,4251,4316,4374,4423,4471,4522,5177,5234,5291,5353,5420,5492,5536,6719,6775,6838,6911,6981,7040,7097,7144,7199,7244,7293,7348,7402,7452,7503,7557,7616,7666,7724,7780,7833,7896,7961,8024,8076,8136,8200,8266,8324,8396,8457,8527,8597,8662,8727,10676,10771,10876,10979,11060,11143,11224,11313,11406,11499,11592,11677,11772,11865,11942,12034,12112,12192,12270,12356,12438,12531,12609,12700,12781,12870,12973,13074,13158,13254,13351,13446,13539,13631,13724,13817,13910,13993,14080,14175,14268,14370,14462,14543,14638,14731,17267,17311,17352,17397,17445,17489,17532,17581,17628,17672,17728,17781,17823,17870,17918,17978,18016,18066,18110,18149,18199,18251,18289,18336,18383,18424,18463,18501,18545,18593,18635,18673,18715,18769,18816,18853,18902,18944,18985,19026,19068,19111,19149,21361,21439,21660,21957,24751,24833,24915,25057,25135,25222,25307,25374,25437,25529,25621,25686,25749,25811,25882,25992,26103,26213,26280,26360,26431,26498,26583,26668,26731,26819,27529,27671,27771,27819,27962,28025,28087,28152,28223,28281,28339,28405,28457,28519,28595,28671,28725,36202,36481,36712,36922,37135,37345,37567,37783,37987,38025,38379,39166,39407,39647,39904,40157,40410,40645,40892,41131,41375,41596,41791,42463,42754,43050,43353,44019,44553,45027,45238,45438,50043,50655,54886,57506,59101", "endLines": "10,11,12,13,18,19,20,24,25,26,27,28,29,30,31,32,33,37,38,40,41,42,43,44,45,47,49,50,51,56,63,64,65,66,67,68,69,74,75,76,77,78,79,80,81,82,83,84,85,87,88,95,96,97,98,99,100,101,111,112,113,114,115,116,117,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,368,369,375,379,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,463,464,577,581,585,589,593,597,601,605,606,612,623,627,631,635,639,643,647,651,655,659,663,667,680,685,690,695,708,716,726,730,734,738,833,865,995,1079,1136", "endColumns": "17,49,53,53,9,45,48,9,48,48,58,53,54,59,58,51,49,9,64,47,48,47,56,46,54,51,53,53,53,9,9,49,48,60,59,55,59,9,59,52,56,54,55,56,48,50,54,53,58,55,54,9,64,57,48,47,50,45,56,56,61,66,71,43,56,55,62,72,69,58,56,46,54,44,48,54,53,49,50,53,58,49,57,55,52,62,64,62,51,59,63,65,57,71,60,69,69,64,64,70,94,104,102,80,82,80,88,92,92,92,84,94,92,76,91,77,79,77,85,81,92,77,90,80,88,102,100,83,95,96,94,92,91,92,92,92,82,86,94,92,101,91,80,94,92,76,43,40,44,47,43,42,48,46,43,55,52,41,46,47,59,37,49,43,38,49,51,37,46,46,40,38,37,43,47,41,37,41,53,46,36,48,41,40,40,41,42,37,35,77,77,12,12,81,81,141,77,86,84,66,62,91,91,64,62,61,70,109,110,109,66,79,70,66,84,84,62,87,63,141,99,47,142,62,61,64,70,57,57,65,51,61,75,75,53,112,10,10,10,10,10,10,10,10,37,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,22,22,22,22,22", "endOffsets": "375,425,479,533,696,742,791,917,966,1015,1074,1128,1183,1243,1302,1354,1404,1532,1597,1705,1754,1802,1859,1906,1961,2073,2183,2237,2291,2439,2677,2727,2776,2837,2897,2953,3013,3183,3243,3296,3353,3408,3464,3521,3570,3621,3676,3730,3789,3904,3959,4246,4311,4369,4418,4466,4517,4563,5229,5286,5348,5415,5487,5531,5588,6770,6833,6906,6976,7035,7092,7139,7194,7239,7288,7343,7397,7447,7498,7552,7611,7661,7719,7775,7828,7891,7956,8019,8071,8131,8195,8261,8319,8391,8452,8522,8592,8657,8722,8793,10766,10871,10974,11055,11138,11219,11308,11401,11494,11587,11672,11767,11860,11937,12029,12107,12187,12265,12351,12433,12526,12604,12695,12776,12865,12968,13069,13153,13249,13346,13441,13534,13626,13719,13812,13905,13988,14075,14170,14263,14365,14457,14538,14633,14726,14803,17306,17347,17392,17440,17484,17527,17576,17623,17667,17723,17776,17818,17865,17913,17973,18011,18061,18105,18144,18194,18246,18284,18331,18378,18419,18458,18496,18540,18588,18630,18668,18710,18764,18811,18848,18897,18939,18980,19021,19063,19106,19144,19180,21434,21512,21952,22222,24828,24910,25052,25130,25217,25302,25369,25432,25524,25616,25681,25744,25806,25877,25987,26098,26208,26275,26355,26426,26493,26578,26663,26726,26814,26878,27666,27766,27814,27957,28020,28082,28147,28218,28276,28334,28400,28452,28514,28590,28666,28720,28833,36476,36707,36917,37130,37340,37562,37778,37982,38020,38374,39161,39402,39642,39899,40152,40405,40640,40887,41126,41370,41591,41786,42458,42749,43045,43348,44014,44548,45022,45233,45433,45609,50146,51226,55826,59096,61037"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\fe28f0070187b5cca4efcaab56677fb2\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "332,338", "startColumns": "4,4", "startOffsets": "19414,19719", "endColumns": "53,66", "endOffsets": "19463,19781"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\26497276d619c831fa389bb2f1588ec8\\transformed\\media3-exoplayer-1.7.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,187,252,316,393,458,548,632", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "120,182,247,311,388,453,543,627,696"}, "to": {"startLines": "439,440,441,442,443,444,445,446,447", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "26883,26953,27015,27080,27144,27221,27286,27376,27460", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "26948,27010,27075,27139,27216,27281,27371,27455,27524"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\fc417f0dcf28f5cd08e849c99534415e\\transformed\\fragment-1.5.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "328,340,364,922,927", "startColumns": "4,4,4,4,4", "startOffsets": "19185,19828,21116,53718,53888", "endLines": "328,340,364,926,930", "endColumns": "56,64,63,24,24", "endOffsets": "19237,19888,21175,53883,54032"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\7fcb9610de1d6578fc689f0d62ec1e8a\\transformed\\savedstate-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "362", "startColumns": "4", "startOffsets": "21012", "endColumns": "53", "endOffsets": "21061"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\ae738502719aa702fcbb63f9de86724f\\transformed\\coil-base-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "282", "startColumns": "4", "startOffsets": "17104", "endColumns": "49", "endOffsets": "17149"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\61e78a9aa048918f3a91576673b36af5\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "380", "startColumns": "4", "startOffsets": "22227", "endColumns": "82", "endOffsets": "22305"}}, {"source": "C:\\Users\\<USER>\\StudioProject\\shipin\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "1", "startColumns": "4", "startOffsets": "16", "endColumns": "43", "endOffsets": "55"}, "to": {"startLines": "381", "startColumns": "4", "startOffsets": "22310", "endColumns": "43", "endOffsets": "22349"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\3bf0e9913a8fb17f961a2eb6334c8d97\\transformed\\material-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "57", "endOffsets": "108"}, "to": {"startLines": "528", "startColumns": "4", "startOffsets": "33391", "endColumns": "57", "endOffsets": "33444"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\4c56c0fb8a9fdb1c153785f7a46f8077\\transformed\\media3-session-1.7.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,144,215,288,387,449,509,583,672,745,841,936,1019,1103,1184,1269,1349,1413,1507,1586,1680,1754,1845,1917,2005,2073,2139,2215,2297,2384,2479,2545,2667,2728,2794", "endColumns": "88,70,72,98,61,59,73,88,72,95,94,82,83,80,84,79,63,93,78,93,73,90,71,87,67,65,75,81,86,94,65,121,60,65,66", "endOffsets": "139,210,283,382,444,504,578,667,740,836,931,1014,1098,1179,1264,1344,1408,1502,1581,1675,1749,1840,1912,2000,2068,2134,2210,2292,2379,2474,2540,2662,2723,2789,2856"}, "to": {"startLines": "120,121,125,245,336,366,393,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,529,530,531,532,533,534,752,754,755,760,762", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5725,5814,6026,14808,19604,21233,23096,23329,23418,23491,23587,23682,23765,23849,23930,24015,24095,24159,24253,24332,24426,24500,24591,24663,33449,33517,33583,33659,33741,33828,46300,46476,46598,46860,47055", "endColumns": "88,70,72,98,61,59,73,88,72,95,94,82,83,80,84,79,63,93,78,93,73,90,71,87,67,65,75,81,86,94,65,121,60,65,66", "endOffsets": "5809,5880,6094,14902,19661,21288,23165,23413,23486,23582,23677,23760,23844,23925,24010,24090,24154,24248,24327,24421,24495,24586,24658,24746,33512,33578,33654,33736,33823,33918,46361,46593,46654,46921,47117"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\80630b05ea7fb0c577cb3dfcc6eeb05c\\transformed\\core-splashscreen-1.0.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,21,23,32,45", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,174,243,315,378,450,524,600,676,753,824,893,964,1032,1113,1205,1298,1407,1528,1988,2763", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,20,22,31,44,48", "endColumns": "59,58,68,71,62,71,73,75,75,76,70,68,70,67,80,91,92,12,12,12,12,12", "endOffsets": "110,169,238,310,373,445,519,595,671,748,819,888,959,1027,1108,1200,1293,1402,1523,1983,2758,3031"}, "to": {"startLines": "46,86,102,103,104,105,192,193,194,195,196,197,198,367,550,551,552,553,555,764,773,786", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1966,3794,4568,4637,4709,4772,10162,10236,10312,10388,10465,10536,10605,21293,34726,34807,34899,34992,35101,47205,47665,48440", "endLines": "46,86,102,103,104,105,192,193,194,195,196,197,198,367,550,551,552,554,556,772,785,789", "endColumns": "59,58,68,71,62,71,73,75,75,76,70,68,70,67,80,91,92,12,12,12,12,12", "endOffsets": "2021,3848,4632,4704,4767,4839,10231,10307,10383,10460,10531,10600,10671,21356,34802,34894,34987,35096,35217,47660,48435,48708"}}, {"source": "C:\\Users\\<USER>\\StudioProject\\shipin\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "56", "endColumns": "83", "endOffsets": "135"}, "to": {"startLines": "763", "startColumns": "4", "startOffsets": "47122", "endColumns": "82", "endOffsets": "47200"}}, {"source": "C:\\Users\\<USER>\\StudioProject\\shipin\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "7,2,3,4,5,6,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "286,55,102,149,196,241,328", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "323,97,144,191,236,281,365"}, "to": {"startLines": "108,122,123,124,126,127,128", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5004,5885,5932,5979,6099,6144,6189", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "5041,5927,5974,6021,6139,6184,6226"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\1fa1c77ec5ce78b692b7b2587ffef552\\transformed\\core-viewtree-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "359", "startColumns": "4", "startOffsets": "20866", "endColumns": "42", "endOffsets": "20904"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\01dc5809f9f3fc5db6925fb2fcbfb1ca\\transformed\\navigation-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "337,792,1024,1027", "startColumns": "4,4,4,4", "startOffsets": "19666,48852,57094,57209", "endLines": "337,798,1026,1029", "endColumns": "52,24,24,24", "endOffsets": "19714,49151,57204,57319"}}]}]}