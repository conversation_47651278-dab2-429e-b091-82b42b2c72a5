package com.libretv.android.`data`.database

import androidx.room.InvalidationTracker
import androidx.room.RoomOpenDelegate
import androidx.room.migration.AutoMigrationSpec
import androidx.room.migration.Migration
import androidx.room.util.TableInfo
import androidx.room.util.TableInfo.Companion.read
import androidx.room.util.dropFtsSyncTriggers
import androidx.sqlite.SQLiteConnection
import androidx.sqlite.execSQL
import javax.`annotation`.processing.Generated
import kotlin.Lazy
import kotlin.String
import kotlin.Suppress
import kotlin.collections.List
import kotlin.collections.Map
import kotlin.collections.MutableList
import kotlin.collections.MutableMap
import kotlin.collections.MutableSet
import kotlin.collections.Set
import kotlin.collections.mutableListOf
import kotlin.collections.mutableMapOf
import kotlin.collections.mutableSetOf
import kotlin.reflect.KClass

@Generated(value = ["androidx.room.RoomProcessor"])
@Suppress(names = ["UNCHECKED_CAST", "DEPRECATION", "REDUNDANT_PROJECTION", "REMOVAL"])
public class LibreTVDatabase_Impl : LibreTVDatabase() {
  private val _videoDao: Lazy<VideoDao> = lazy {
    VideoDao_Impl(this)
  }

  private val _searchHistoryDao: Lazy<SearchHistoryDao> = lazy {
    SearchHistoryDao_Impl(this)
  }

  private val _watchHistoryDao: Lazy<WatchHistoryDao> = lazy {
    WatchHistoryDao_Impl(this)
  }

  private val _apiSourceDao: Lazy<ApiSourceDao> = lazy {
    ApiSourceDao_Impl(this)
  }

  protected override fun createOpenDelegate(): RoomOpenDelegate {
    val _openDelegate: RoomOpenDelegate = object : RoomOpenDelegate(1,
        "0bf9903719435483e519fe64c8f07aa3", "86ff8be9982b78e954d62ff3dbb0a302") {
      public override fun createAllTables(connection: SQLiteConnection) {
        connection.execSQL("CREATE TABLE IF NOT EXISTS `videos` (`vodId` TEXT NOT NULL, `vodName` TEXT NOT NULL, `vodPic` TEXT, `vodRemarks` TEXT, `vodYear` TEXT, `vodArea` TEXT, `vodDirector` TEXT, `vodActor` TEXT, `vodContent` TEXT, `vodPlayUrl` TEXT, `typeName` TEXT, `sourceName` TEXT, `sourceCode` TEXT, `apiUrl` TEXT, `lastWatchTime` INTEGER NOT NULL, `watchProgress` INTEGER NOT NULL, `isFavorite` INTEGER NOT NULL, PRIMARY KEY(`vodId`))")
        connection.execSQL("CREATE TABLE IF NOT EXISTS `search_history` (`query` TEXT NOT NULL, `timestamp` INTEGER NOT NULL, PRIMARY KEY(`query`))")
        connection.execSQL("CREATE TABLE IF NOT EXISTS `watch_history` (`id` TEXT NOT NULL, `videoId` TEXT NOT NULL, `videoTitle` TEXT NOT NULL, `videoCover` TEXT, `sourceCode` TEXT NOT NULL, `sourceName` TEXT NOT NULL, `episodeIndex` INTEGER NOT NULL, `episodeTitle` TEXT, `watchProgress` INTEGER NOT NULL, `totalDuration` INTEGER NOT NULL, `lastWatchTime` INTEGER NOT NULL, `episodes` TEXT NOT NULL, PRIMARY KEY(`id`))")
        connection.execSQL("CREATE TABLE IF NOT EXISTS `api_sources` (`code` TEXT NOT NULL, `name` TEXT NOT NULL, `api` TEXT NOT NULL, `detail` TEXT, `isAdult` INTEGER NOT NULL, `isEnabled` INTEGER NOT NULL, `isCustom` INTEGER NOT NULL, `order` INTEGER NOT NULL, PRIMARY KEY(`code`))")
        connection.execSQL("CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)")
        connection.execSQL("INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '0bf9903719435483e519fe64c8f07aa3')")
      }

      public override fun dropAllTables(connection: SQLiteConnection) {
        connection.execSQL("DROP TABLE IF EXISTS `videos`")
        connection.execSQL("DROP TABLE IF EXISTS `search_history`")
        connection.execSQL("DROP TABLE IF EXISTS `watch_history`")
        connection.execSQL("DROP TABLE IF EXISTS `api_sources`")
      }

      public override fun onCreate(connection: SQLiteConnection) {
      }

      public override fun onOpen(connection: SQLiteConnection) {
        internalInitInvalidationTracker(connection)
      }

      public override fun onPreMigrate(connection: SQLiteConnection) {
        dropFtsSyncTriggers(connection)
      }

      public override fun onPostMigrate(connection: SQLiteConnection) {
      }

      public override fun onValidateSchema(connection: SQLiteConnection):
          RoomOpenDelegate.ValidationResult {
        val _columnsVideos: MutableMap<String, TableInfo.Column> = mutableMapOf()
        _columnsVideos.put("vodId", TableInfo.Column("vodId", "TEXT", true, 1, null,
            TableInfo.CREATED_FROM_ENTITY))
        _columnsVideos.put("vodName", TableInfo.Column("vodName", "TEXT", true, 0, null,
            TableInfo.CREATED_FROM_ENTITY))
        _columnsVideos.put("vodPic", TableInfo.Column("vodPic", "TEXT", false, 0, null,
            TableInfo.CREATED_FROM_ENTITY))
        _columnsVideos.put("vodRemarks", TableInfo.Column("vodRemarks", "TEXT", false, 0, null,
            TableInfo.CREATED_FROM_ENTITY))
        _columnsVideos.put("vodYear", TableInfo.Column("vodYear", "TEXT", false, 0, null,
            TableInfo.CREATED_FROM_ENTITY))
        _columnsVideos.put("vodArea", TableInfo.Column("vodArea", "TEXT", false, 0, null,
            TableInfo.CREATED_FROM_ENTITY))
        _columnsVideos.put("vodDirector", TableInfo.Column("vodDirector", "TEXT", false, 0, null,
            TableInfo.CREATED_FROM_ENTITY))
        _columnsVideos.put("vodActor", TableInfo.Column("vodActor", "TEXT", false, 0, null,
            TableInfo.CREATED_FROM_ENTITY))
        _columnsVideos.put("vodContent", TableInfo.Column("vodContent", "TEXT", false, 0, null,
            TableInfo.CREATED_FROM_ENTITY))
        _columnsVideos.put("vodPlayUrl", TableInfo.Column("vodPlayUrl", "TEXT", false, 0, null,
            TableInfo.CREATED_FROM_ENTITY))
        _columnsVideos.put("typeName", TableInfo.Column("typeName", "TEXT", false, 0, null,
            TableInfo.CREATED_FROM_ENTITY))
        _columnsVideos.put("sourceName", TableInfo.Column("sourceName", "TEXT", false, 0, null,
            TableInfo.CREATED_FROM_ENTITY))
        _columnsVideos.put("sourceCode", TableInfo.Column("sourceCode", "TEXT", false, 0, null,
            TableInfo.CREATED_FROM_ENTITY))
        _columnsVideos.put("apiUrl", TableInfo.Column("apiUrl", "TEXT", false, 0, null,
            TableInfo.CREATED_FROM_ENTITY))
        _columnsVideos.put("lastWatchTime", TableInfo.Column("lastWatchTime", "INTEGER", true, 0,
            null, TableInfo.CREATED_FROM_ENTITY))
        _columnsVideos.put("watchProgress", TableInfo.Column("watchProgress", "INTEGER", true, 0,
            null, TableInfo.CREATED_FROM_ENTITY))
        _columnsVideos.put("isFavorite", TableInfo.Column("isFavorite", "INTEGER", true, 0, null,
            TableInfo.CREATED_FROM_ENTITY))
        val _foreignKeysVideos: MutableSet<TableInfo.ForeignKey> = mutableSetOf()
        val _indicesVideos: MutableSet<TableInfo.Index> = mutableSetOf()
        val _infoVideos: TableInfo = TableInfo("videos", _columnsVideos, _foreignKeysVideos,
            _indicesVideos)
        val _existingVideos: TableInfo = read(connection, "videos")
        if (!_infoVideos.equals(_existingVideos)) {
          return RoomOpenDelegate.ValidationResult(false, """
              |videos(com.libretv.android.data.model.VideoInfo).
              | Expected:
              |""".trimMargin() + _infoVideos + """
              |
              | Found:
              |""".trimMargin() + _existingVideos)
        }
        val _columnsSearchHistory: MutableMap<String, TableInfo.Column> = mutableMapOf()
        _columnsSearchHistory.put("query", TableInfo.Column("query", "TEXT", true, 1, null,
            TableInfo.CREATED_FROM_ENTITY))
        _columnsSearchHistory.put("timestamp", TableInfo.Column("timestamp", "INTEGER", true, 0,
            null, TableInfo.CREATED_FROM_ENTITY))
        val _foreignKeysSearchHistory: MutableSet<TableInfo.ForeignKey> = mutableSetOf()
        val _indicesSearchHistory: MutableSet<TableInfo.Index> = mutableSetOf()
        val _infoSearchHistory: TableInfo = TableInfo("search_history", _columnsSearchHistory,
            _foreignKeysSearchHistory, _indicesSearchHistory)
        val _existingSearchHistory: TableInfo = read(connection, "search_history")
        if (!_infoSearchHistory.equals(_existingSearchHistory)) {
          return RoomOpenDelegate.ValidationResult(false, """
              |search_history(com.libretv.android.data.model.SearchHistory).
              | Expected:
              |""".trimMargin() + _infoSearchHistory + """
              |
              | Found:
              |""".trimMargin() + _existingSearchHistory)
        }
        val _columnsWatchHistory: MutableMap<String, TableInfo.Column> = mutableMapOf()
        _columnsWatchHistory.put("id", TableInfo.Column("id", "TEXT", true, 1, null,
            TableInfo.CREATED_FROM_ENTITY))
        _columnsWatchHistory.put("videoId", TableInfo.Column("videoId", "TEXT", true, 0, null,
            TableInfo.CREATED_FROM_ENTITY))
        _columnsWatchHistory.put("videoTitle", TableInfo.Column("videoTitle", "TEXT", true, 0, null,
            TableInfo.CREATED_FROM_ENTITY))
        _columnsWatchHistory.put("videoCover", TableInfo.Column("videoCover", "TEXT", false, 0,
            null, TableInfo.CREATED_FROM_ENTITY))
        _columnsWatchHistory.put("sourceCode", TableInfo.Column("sourceCode", "TEXT", true, 0, null,
            TableInfo.CREATED_FROM_ENTITY))
        _columnsWatchHistory.put("sourceName", TableInfo.Column("sourceName", "TEXT", true, 0, null,
            TableInfo.CREATED_FROM_ENTITY))
        _columnsWatchHistory.put("episodeIndex", TableInfo.Column("episodeIndex", "INTEGER", true,
            0, null, TableInfo.CREATED_FROM_ENTITY))
        _columnsWatchHistory.put("episodeTitle", TableInfo.Column("episodeTitle", "TEXT", false, 0,
            null, TableInfo.CREATED_FROM_ENTITY))
        _columnsWatchHistory.put("watchProgress", TableInfo.Column("watchProgress", "INTEGER", true,
            0, null, TableInfo.CREATED_FROM_ENTITY))
        _columnsWatchHistory.put("totalDuration", TableInfo.Column("totalDuration", "INTEGER", true,
            0, null, TableInfo.CREATED_FROM_ENTITY))
        _columnsWatchHistory.put("lastWatchTime", TableInfo.Column("lastWatchTime", "INTEGER", true,
            0, null, TableInfo.CREATED_FROM_ENTITY))
        _columnsWatchHistory.put("episodes", TableInfo.Column("episodes", "TEXT", true, 0, null,
            TableInfo.CREATED_FROM_ENTITY))
        val _foreignKeysWatchHistory: MutableSet<TableInfo.ForeignKey> = mutableSetOf()
        val _indicesWatchHistory: MutableSet<TableInfo.Index> = mutableSetOf()
        val _infoWatchHistory: TableInfo = TableInfo("watch_history", _columnsWatchHistory,
            _foreignKeysWatchHistory, _indicesWatchHistory)
        val _existingWatchHistory: TableInfo = read(connection, "watch_history")
        if (!_infoWatchHistory.equals(_existingWatchHistory)) {
          return RoomOpenDelegate.ValidationResult(false, """
              |watch_history(com.libretv.android.data.model.WatchHistory).
              | Expected:
              |""".trimMargin() + _infoWatchHistory + """
              |
              | Found:
              |""".trimMargin() + _existingWatchHistory)
        }
        val _columnsApiSources: MutableMap<String, TableInfo.Column> = mutableMapOf()
        _columnsApiSources.put("code", TableInfo.Column("code", "TEXT", true, 1, null,
            TableInfo.CREATED_FROM_ENTITY))
        _columnsApiSources.put("name", TableInfo.Column("name", "TEXT", true, 0, null,
            TableInfo.CREATED_FROM_ENTITY))
        _columnsApiSources.put("api", TableInfo.Column("api", "TEXT", true, 0, null,
            TableInfo.CREATED_FROM_ENTITY))
        _columnsApiSources.put("detail", TableInfo.Column("detail", "TEXT", false, 0, null,
            TableInfo.CREATED_FROM_ENTITY))
        _columnsApiSources.put("isAdult", TableInfo.Column("isAdult", "INTEGER", true, 0, null,
            TableInfo.CREATED_FROM_ENTITY))
        _columnsApiSources.put("isEnabled", TableInfo.Column("isEnabled", "INTEGER", true, 0, null,
            TableInfo.CREATED_FROM_ENTITY))
        _columnsApiSources.put("isCustom", TableInfo.Column("isCustom", "INTEGER", true, 0, null,
            TableInfo.CREATED_FROM_ENTITY))
        _columnsApiSources.put("order", TableInfo.Column("order", "INTEGER", true, 0, null,
            TableInfo.CREATED_FROM_ENTITY))
        val _foreignKeysApiSources: MutableSet<TableInfo.ForeignKey> = mutableSetOf()
        val _indicesApiSources: MutableSet<TableInfo.Index> = mutableSetOf()
        val _infoApiSources: TableInfo = TableInfo("api_sources", _columnsApiSources,
            _foreignKeysApiSources, _indicesApiSources)
        val _existingApiSources: TableInfo = read(connection, "api_sources")
        if (!_infoApiSources.equals(_existingApiSources)) {
          return RoomOpenDelegate.ValidationResult(false, """
              |api_sources(com.libretv.android.data.model.ApiSource).
              | Expected:
              |""".trimMargin() + _infoApiSources + """
              |
              | Found:
              |""".trimMargin() + _existingApiSources)
        }
        return RoomOpenDelegate.ValidationResult(true, null)
      }
    }
    return _openDelegate
  }

  protected override fun createInvalidationTracker(): InvalidationTracker {
    val _shadowTablesMap: MutableMap<String, String> = mutableMapOf()
    val _viewTables: MutableMap<String, Set<String>> = mutableMapOf()
    return InvalidationTracker(this, _shadowTablesMap, _viewTables, "videos", "search_history",
        "watch_history", "api_sources")
  }

  public override fun clearAllTables() {
    super.performClear(false, "videos", "search_history", "watch_history", "api_sources")
  }

  protected override fun getRequiredTypeConverterClasses(): Map<KClass<*>, List<KClass<*>>> {
    val _typeConvertersMap: MutableMap<KClass<*>, List<KClass<*>>> = mutableMapOf()
    _typeConvertersMap.put(VideoDao::class, VideoDao_Impl.getRequiredConverters())
    _typeConvertersMap.put(SearchHistoryDao::class, SearchHistoryDao_Impl.getRequiredConverters())
    _typeConvertersMap.put(WatchHistoryDao::class, WatchHistoryDao_Impl.getRequiredConverters())
    _typeConvertersMap.put(ApiSourceDao::class, ApiSourceDao_Impl.getRequiredConverters())
    return _typeConvertersMap
  }

  public override fun getRequiredAutoMigrationSpecClasses(): Set<KClass<out AutoMigrationSpec>> {
    val _autoMigrationSpecsSet: MutableSet<KClass<out AutoMigrationSpec>> = mutableSetOf()
    return _autoMigrationSpecsSet
  }

  public override
      fun createAutoMigrations(autoMigrationSpecs: Map<KClass<out AutoMigrationSpec>, AutoMigrationSpec>):
      List<Migration> {
    val _autoMigrations: MutableList<Migration> = mutableListOf()
    return _autoMigrations
  }

  public override fun videoDao(): VideoDao = _videoDao.value

  public override fun searchHistoryDao(): SearchHistoryDao = _searchHistoryDao.value

  public override fun watchHistoryDao(): WatchHistoryDao = _watchHistoryDao.value

  public override fun apiSourceDao(): ApiSourceDao = _apiSourceDao.value
}
