/ Header Record For PersistentHashMapValueStorage/ .com.libretv.android.data.database.ApiSourceDao2 1com.libretv.android.data.database.LibreTVDatabase3 2com.libretv.android.data.database.SearchHistoryDao+ *com.libretv.android.data.database.VideoDao2 1com.libretv.android.data.database.WatchHistoryDao$ #androidx.activity.ComponentActivity android.app.Application androidx.room.RoomDatabase3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer okhttp3.Interceptor okhttp3.Interceptor kotlin.Annotation kotlin.Annotation kotlin.Annotation3 2com.libretv.android.presentation.navigation.Screen3 2com.libretv.android.presentation.navigation.Screen3 2com.libretv.android.presentation.navigation.Screen3 2com.libretv.android.presentation.navigation.Screen androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel