package com.libretv.android.presentation.theme

import android.app.Activity
import android.os.Build
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.dynamicDarkColorScheme
import androidx.compose.material3.dynamicLightColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.SideEffect
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalView
import androidx.core.view.WindowCompat

// LibreTV品牌色彩
private val LibreTVPrimary = Color(0xFF6366F1) // Indigo-500
private val LibreTVPrimaryVariant = Color(0xFF4F46E5) // Indigo-600
private val LibreTVSecondary = Color(0xFF8B5CF6) // Violet-500
private val LibreTVSecondaryVariant = Color(0xFF7C3AED) // Violet-600

private val DarkColorScheme = darkColorScheme(
    primary = LibreTVPrimary,
    secondary = LibreTVSecondary,
    tertiary = Color(0xFF06B6D4), // Cyan-500
    background = Color(0xFF0F172A), // Slate-900
    surface = Color(0xFF1E293B), // Slate-800
    onPrimary = Color.White,
    onSecondary = Color.White,
    onTertiary = Color.White,
    onBackground = Color(0xFFF1F5F9), // Slate-100
    onSurface = Color(0xFFF1F5F9), // Slate-100
)

private val LightColorScheme = lightColorScheme(
    primary = LibreTVPrimary,
    secondary = LibreTVSecondary,
    tertiary = Color(0xFF06B6D4), // Cyan-500
    background = Color(0xFFFFFBFF),
    surface = Color(0xFFFFFBFF),
    onPrimary = Color.White,
    onSecondary = Color.White,
    onTertiary = Color.White,
    onBackground = Color(0xFF1C1B1F),
    onSurface = Color(0xFF1C1B1F),
)

@Composable
fun LibreTVTheme(
    darkTheme: Boolean = isSystemInDarkTheme(),
    // Dynamic color is available on Android 12+
    dynamicColor: Boolean = true,
    content: @Composable () -> Unit
) {
    val colorScheme = when {
        dynamicColor && Build.VERSION.SDK_INT >= Build.VERSION_CODES.S -> {
            val context = LocalContext.current
            if (darkTheme) dynamicDarkColorScheme(context) else dynamicLightColorScheme(context)
        }

        darkTheme -> DarkColorScheme
        else -> LightColorScheme
    }
    val view = LocalView.current
    if (!view.isInEditMode) {
        SideEffect {
            val window = (view.context as Activity).window
            window.statusBarColor = colorScheme.primary.toArgb()
            WindowCompat.getInsetsController(window, view).isAppearanceLightStatusBars = darkTheme
        }
    }

    MaterialTheme(
        colorScheme = colorScheme,
        typography = Typography,
        content = content
    )
}
