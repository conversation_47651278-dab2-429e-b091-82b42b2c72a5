package com.libretv.android.presentation.screens.home;

import com.libretv.android.data.network.DoubanApiService;
import com.libretv.android.data.repository.VideoRepository;
import com.libretv.android.data.repository.WatchHistoryRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class HomeViewModel_Factory implements Factory<HomeViewModel> {
  private final Provider<VideoRepository> videoRepositoryProvider;

  private final Provider<WatchHistoryRepository> watchHistoryRepositoryProvider;

  private final Provider<DoubanApiService> doubanApiServiceProvider;

  public HomeViewModel_Factory(Provider<VideoRepository> videoRepositoryProvider,
      Provider<WatchHistoryRepository> watchHistoryRepositoryProvider,
      Provider<DoubanApiService> doubanApiServiceProvider) {
    this.videoRepositoryProvider = videoRepositoryProvider;
    this.watchHistoryRepositoryProvider = watchHistoryRepositoryProvider;
    this.doubanApiServiceProvider = doubanApiServiceProvider;
  }

  @Override
  public HomeViewModel get() {
    return newInstance(videoRepositoryProvider.get(), watchHistoryRepositoryProvider.get(), doubanApiServiceProvider.get());
  }

  public static HomeViewModel_Factory create(Provider<VideoRepository> videoRepositoryProvider,
      Provider<WatchHistoryRepository> watchHistoryRepositoryProvider,
      Provider<DoubanApiService> doubanApiServiceProvider) {
    return new HomeViewModel_Factory(videoRepositoryProvider, watchHistoryRepositoryProvider, doubanApiServiceProvider);
  }

  public static HomeViewModel newInstance(VideoRepository videoRepository,
      WatchHistoryRepository watchHistoryRepository, DoubanApiService doubanApiService) {
    return new HomeViewModel(videoRepository, watchHistoryRepository, doubanApiService);
  }
}
