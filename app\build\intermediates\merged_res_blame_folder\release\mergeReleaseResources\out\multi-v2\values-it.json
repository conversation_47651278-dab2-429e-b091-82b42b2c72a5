{"logs": [{"outputFile": "com.libretv.android.app-mergeReleaseResources-2:/values-it/values-it.xml", "map": [{"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\26497276d619c831fa389bb2f1588ec8\\transformed\\media3-exoplayer-1.7.1\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,187,259,329,405,471,558,643", "endColumns": "70,60,71,69,75,65,86,84,73", "endOffsets": "121,182,254,324,400,466,553,638,712"}, "to": {"startLines": "74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "5742,5813,5874,5946,6016,6092,6158,6245,6330", "endColumns": "70,60,71,69,75,65,86,84,73", "endOffsets": "5808,5869,5941,6011,6087,6153,6240,6325,6399"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\0b493fc5385f598ec7f47c0a10bc88bb\\transformed\\material3-release\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,182,306,428,552,657,753,866,1009,1128,1286,1370,1482,1576,1676,1795,1917,2034,2176,2316,2459,2635,2770,2890,3013,3143,3238,3335,3462,3600,3700,3810,3916,4059,4207,4317,4418,4507,4603,4696,4811,4897,4983,5086,5166,5249,5348,5454,5554,5655,5743,5853,5953,6058,6176,6256,6370", "endColumns": "126,123,121,123,104,95,112,142,118,157,83,111,93,99,118,121,116,141,139,142,175,134,119,122,129,94,96,126,137,99,109,105,142,147,109,100,88,95,92,114,85,85,102,79,82,98,105,99,100,87,109,99,104,117,79,113,106", "endOffsets": "177,301,423,547,652,748,861,1004,1123,1281,1365,1477,1571,1671,1790,1912,2029,2171,2311,2454,2630,2765,2885,3008,3138,3233,3330,3457,3595,3695,3805,3911,4054,4202,4312,4413,4502,4598,4691,4806,4892,4978,5081,5161,5244,5343,5449,5549,5650,5738,5848,5948,6053,6171,6251,6365,6472"}, "to": {"startLines": "102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7677,7804,7928,8050,8174,8279,8375,8488,8631,8750,8908,8992,9104,9198,9298,9417,9539,9656,9798,9938,10081,10257,10392,10512,10635,10765,10860,10957,11084,11222,11322,11432,11538,11681,11829,11939,12040,12129,12225,12318,12433,12519,12605,12708,12788,12871,12970,13076,13176,13277,13365,13475,13575,13680,13798,13878,13992", "endColumns": "126,123,121,123,104,95,112,142,118,157,83,111,93,99,118,121,116,141,139,142,175,134,119,122,129,94,96,126,137,99,109,105,142,147,109,100,88,95,92,114,85,85,102,79,82,98,105,99,100,87,109,99,104,117,79,113,106", "endOffsets": "7799,7923,8045,8169,8274,8370,8483,8626,8745,8903,8987,9099,9193,9293,9412,9534,9651,9793,9933,10076,10252,10387,10507,10630,10760,10855,10952,11079,11217,11317,11427,11533,11676,11824,11934,12035,12124,12220,12313,12428,12514,12600,12703,12783,12866,12965,13071,13171,13272,13360,13470,13570,13675,13793,13873,13987,14094"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\2425a4573e4fb65053b44e46c3806d87\\transformed\\foundation-release\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,145,243", "endColumns": "89,97,98", "endOffsets": "140,238,337"}, "to": {"startLines": "19,178,179", "startColumns": "4,4,4", "startOffsets": "713,15723,15821", "endColumns": "89,97,98", "endOffsets": "798,15816,15915"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\6b27351f1a96970e2ba142187de324d4\\transformed\\media3-ui-1.7.1\\res\\values-it\\values-it.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,475,663,749,837,916,1008,1100,1178,1243,1343,1441,1506,1574,1639,1710,1838,1972,2098,2168,2261,2336,2412,2508,2606,2675,2743,2796,2854,2902,2963,3037,3108,3171,3252,3310,3371,3437,3489,3551,3627,3703,3761", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,85,87,78,91,91,77,64,99,97,64,67,64,70,127,133,125,69,92,74,75,95,97,68,67,52,57,47,60,73,70,62,80,57,60,65,51,61,75,75,57,69", "endOffsets": "281,470,658,744,832,911,1003,1095,1173,1238,1338,1436,1501,1569,1634,1705,1833,1967,2093,2163,2256,2331,2407,2503,2601,2670,2738,2791,2849,2897,2958,3032,3103,3166,3247,3305,3366,3432,3484,3546,3622,3698,3756,3826"}, "to": {"startLines": "2,11,15,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,336,525,3662,3748,3836,3915,4007,4099,4177,4242,4342,4440,4505,4573,4638,4709,4837,4971,5097,5167,5260,5335,5411,5507,5605,5674,6404,6457,6515,6563,6624,6698,6769,6832,6913,6971,7032,7098,7150,7212,7288,7364,7422", "endLines": "10,14,18,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99", "endColumns": "17,12,12,85,87,78,91,91,77,64,99,97,64,67,64,70,127,133,125,69,92,74,75,95,97,68,67,52,57,47,60,73,70,62,80,57,60,65,51,61,75,75,57,69", "endOffsets": "331,520,708,3743,3831,3910,4002,4094,4172,4237,4337,4435,4500,4568,4633,4704,4832,4966,5092,5162,5255,5330,5406,5502,5600,5669,5737,6452,6510,6558,6619,6693,6764,6827,6908,6966,7027,7093,7145,7207,7283,7359,7417,7487"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\406d1819cde1682167f3af9259cf68c0\\transformed\\ui-release\\res\\values-it\\values-it.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "196,296,383,481,581,668,747,853,946,1041,1125,1213,1298,1383,1459,1531,1601,1679,1748", "endColumns": "99,86,97,99,86,78,105,92,94,83,87,84,84,75,71,69,77,68,120", "endOffsets": "291,378,476,576,663,742,848,941,1036,1120,1208,1293,1378,1454,1526,1596,1674,1743,1864"}, "to": {"startLines": "27,28,29,31,32,100,101,165,166,167,168,169,170,171,172,173,175,176,177", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1550,1650,1737,1917,2017,7492,7571,14606,14699,14794,14878,14966,15051,15136,15212,15284,15455,15533,15602", "endColumns": "99,86,97,99,86,78,105,92,94,83,87,84,84,75,71,69,77,68,120", "endOffsets": "1645,1732,1830,2012,2099,7566,7672,14694,14789,14873,14961,15046,15131,15207,15279,15349,15528,15597,15718"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\4c56c0fb8a9fdb1c153785f7a46f8077\\transformed\\media3-session-1.7.1\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,137,230,309,412,508,598,677,774,864,970,1038,1151,1251,1340,1415,1514,1601,1695,1772,1843,1922,2002,2100", "endColumns": "81,92,78,102,95,89,78,96,89,105,67,112,99,88,74,98,86,93,76,70,78,79,97,101", "endOffsets": "132,225,304,407,503,593,672,769,859,965,1033,1146,1246,1335,1410,1509,1596,1690,1767,1838,1917,1997,2095,2197"}, "to": {"startLines": "30,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,159,160,161,162,163,164", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1835,2104,2197,2276,2379,2475,2565,2644,2741,2831,2937,3005,3118,3218,3307,3382,3481,3568,14099,14176,14247,14326,14406,14504", "endColumns": "81,92,78,102,95,89,78,96,89,105,67,112,99,88,74,98,86,93,76,70,78,79,97,101", "endOffsets": "1912,2192,2271,2374,2470,2560,2639,2736,2826,2932,3000,3113,3213,3302,3377,3476,3563,3657,14171,14242,14321,14401,14499,14601"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\955c05c442e591726e4833ad1e0fc303\\transformed\\core-1.16.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,565,672,802", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "148,250,349,451,560,667,797,898"}, "to": {"startLines": "20,21,22,23,24,25,26,174", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "803,901,1003,1102,1204,1313,1420,15354", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "896,998,1097,1199,1308,1415,1545,15450"}}]}]}