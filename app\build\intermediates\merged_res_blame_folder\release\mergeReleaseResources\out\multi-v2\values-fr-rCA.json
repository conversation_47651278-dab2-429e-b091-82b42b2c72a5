{"logs": [{"outputFile": "com.libretv.android.app-mergeReleaseResources-2:/values-fr-rCA/values-fr-rCA.xml", "map": [{"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\0b493fc5385f598ec7f47c0a10bc88bb\\transformed\\material3-release\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,202,347,469,617,743,837,949,1091,1210,1369,1453,1554,1655,1756,1877,2012,2118,2268,2414,2550,2752,2881,2999,3122,3255,3357,3462,3586,3714,3816,3928,4033,4178,4330,4439,4548,4626,4719,4814,4932,5022,5108,5215,5295,5380,5477,5588,5681,5785,5873,5989,6090,6199,6321,6401,6511", "endColumns": "146,144,121,147,125,93,111,141,118,158,83,100,100,100,120,134,105,149,145,135,201,128,117,122,132,101,104,123,127,101,111,104,144,151,108,108,77,92,94,117,89,85,106,79,84,96,110,92,103,87,115,100,108,121,79,109,96", "endOffsets": "197,342,464,612,738,832,944,1086,1205,1364,1448,1549,1650,1751,1872,2007,2113,2263,2409,2545,2747,2876,2994,3117,3250,3352,3457,3581,3709,3811,3923,4028,4173,4325,4434,4543,4621,4714,4809,4927,5017,5103,5210,5290,5375,5472,5583,5676,5780,5868,5984,6085,6194,6316,6396,6506,6603"}, "to": {"startLines": "102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7782,7929,8074,8196,8344,8470,8564,8676,8818,8937,9096,9180,9281,9382,9483,9604,9739,9845,9995,10141,10277,10479,10608,10726,10849,10982,11084,11189,11313,11441,11543,11655,11760,11905,12057,12166,12275,12353,12446,12541,12659,12749,12835,12942,13022,13107,13204,13315,13408,13512,13600,13716,13817,13926,14048,14128,14238", "endColumns": "146,144,121,147,125,93,111,141,118,158,83,100,100,100,120,134,105,149,145,135,201,128,117,122,132,101,104,123,127,101,111,104,144,151,108,108,77,92,94,117,89,85,106,79,84,96,110,92,103,87,115,100,108,121,79,109,96", "endOffsets": "7924,8069,8191,8339,8465,8559,8671,8813,8932,9091,9175,9276,9377,9478,9599,9734,9840,9990,10136,10272,10474,10603,10721,10844,10977,11079,11184,11308,11436,11538,11650,11755,11900,12052,12161,12270,12348,12441,12536,12654,12744,12830,12937,13017,13102,13199,13310,13403,13507,13595,13711,13812,13921,14043,14123,14233,14330"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\6b27351f1a96970e2ba142187de324d4\\transformed\\media3-ui-1.7.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,499,686,775,866,945,1043,1140,1219,1285,1382,1479,1544,1607,1671,1743,1864,1990,2115,2190,2278,2351,2431,2530,2631,2697,2761,2814,2872,2920,2981,3048,3125,3192,3264,3322,3381,3447,3499,3564,3643,3722,3776", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,88,90,78,97,96,78,65,96,96,64,62,63,71,120,125,124,74,87,72,79,98,100,65,63,52,57,47,60,66,76,66,71,57,58,65,51,64,78,78,53,65", "endOffsets": "280,494,681,770,861,940,1038,1135,1214,1280,1377,1474,1539,1602,1666,1738,1859,1985,2110,2185,2273,2346,2426,2525,2626,2692,2756,2809,2867,2915,2976,3043,3120,3187,3259,3317,3376,3442,3494,3559,3638,3717,3771,3837"}, "to": {"startLines": "2,11,15,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,549,3722,3811,3902,3981,4079,4176,4255,4321,4418,4515,4580,4643,4707,4779,4900,5026,5151,5226,5314,5387,5467,5566,5667,5733,6527,6580,6638,6686,6747,6814,6891,6958,7030,7088,7147,7213,7265,7330,7409,7488,7542", "endLines": "10,14,18,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99", "endColumns": "17,12,12,88,90,78,97,96,78,65,96,96,64,62,63,71,120,125,124,74,87,72,79,98,100,65,63,52,57,47,60,66,76,66,71,57,58,65,51,64,78,78,53,65", "endOffsets": "330,544,731,3806,3897,3976,4074,4171,4250,4316,4413,4510,4575,4638,4702,4774,4895,5021,5146,5221,5309,5382,5462,5561,5662,5728,5792,6575,6633,6681,6742,6809,6886,6953,7025,7083,7142,7208,7260,7325,7404,7483,7537,7603"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\4c56c0fb8a9fdb1c153785f7a46f8077\\transformed\\media3-session-1.7.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,137,243,325,429,535,624,727,827,929,1037,1109,1212,1315,1407,1480,1568,1647,1750,1818,1884,1977,2071,2175", "endColumns": "81,105,81,103,105,88,102,99,101,107,71,102,102,91,72,87,78,102,67,65,92,93,103,109", "endOffsets": "132,238,320,424,530,619,722,822,924,1032,1104,1207,1310,1402,1475,1563,1642,1745,1813,1879,1972,2066,2170,2280"}, "to": {"startLines": "30,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,159,160,161,162,163,164", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1834,2109,2215,2297,2401,2507,2596,2699,2799,2901,3009,3081,3184,3287,3379,3452,3540,3619,14335,14403,14469,14562,14656,14760", "endColumns": "81,105,81,103,105,88,102,99,101,107,71,102,102,91,72,87,78,102,67,65,92,93,103,109", "endOffsets": "1911,2210,2292,2396,2502,2591,2694,2794,2896,3004,3076,3179,3282,3374,3447,3535,3614,3717,14398,14464,14557,14651,14755,14865"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\2425a4573e4fb65053b44e46c3806d87\\transformed\\foundation-release\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,145,233", "endColumns": "89,87,94", "endOffsets": "140,228,323"}, "to": {"startLines": "19,178,179", "startColumns": "4,4,4", "startOffsets": "736,15974,16062", "endColumns": "89,87,94", "endOffsets": "821,16057,16152"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\955c05c442e591726e4833ad1e0fc303\\transformed\\core-1.16.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,778", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "148,250,349,451,555,659,773,874"}, "to": {"startLines": "20,21,22,23,24,25,26,174", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "826,924,1026,1125,1227,1331,1435,15603", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "919,1021,1120,1222,1326,1430,1544,15699"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\26497276d619c831fa389bb2f1588ec8\\transformed\\media3-exoplayer-1.7.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,194,272,344,427,503,600,693", "endColumns": "73,64,77,71,82,75,96,92,91", "endOffsets": "124,189,267,339,422,498,595,688,780"}, "to": {"startLines": "74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "5797,5871,5936,6014,6086,6169,6245,6342,6435", "endColumns": "73,64,77,71,82,75,96,92,91", "endOffsets": "5866,5931,6009,6081,6164,6240,6337,6430,6522"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\406d1819cde1682167f3af9259cf68c0\\transformed\\ui-release\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "196,295,383,481,587,674,754,848,940,1027,1108,1193,1269,1354,1429,1507,1581,1660,1729", "endColumns": "98,87,97,105,86,79,93,91,86,80,84,75,84,74,77,73,78,68,121", "endOffsets": "290,378,476,582,669,749,843,935,1022,1103,1188,1264,1349,1424,1502,1576,1655,1724,1846"}, "to": {"startLines": "27,28,29,31,32,100,101,165,166,167,168,169,170,171,172,173,175,176,177", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1549,1648,1736,1916,2022,7608,7688,14870,14962,15049,15130,15215,15291,15376,15451,15529,15704,15783,15852", "endColumns": "98,87,97,105,86,79,93,91,86,80,84,75,84,74,77,73,78,68,121", "endOffsets": "1643,1731,1829,2017,2104,7683,7777,14957,15044,15125,15210,15286,15371,15446,15524,15598,15778,15847,15969"}}]}]}