package com.libretv.android.data.repository

import com.libretv.android.data.database.ApiSourceDao
import com.libretv.android.data.database.VideoDao
import com.libretv.android.data.model.ApiResponse
import com.libretv.android.data.model.ApiSource
import com.libretv.android.data.model.VideoDetailInfo
import com.libretv.android.data.model.VideoInfo
import com.libretv.android.data.network.ApiService
import com.libretv.android.data.network.InternalApiService
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 视频数据仓库
 * 实现LibreTV的视频搜索和详情获取功能
 */
@Singleton
class VideoRepository @Inject constructor(
    private val apiService: ApiService,
    private val internalApiService: InternalApiService,
    private val videoDao: VideoDao,
    private val apiSourceDao: ApiSourceDao
) {
    
    /**
     * 搜索视频 - 聚合搜索
     */
    suspend fun searchVideos(
        keyword: String,
        page: Int = 1,
        useAggregated: Boolean = true
    ): Result<List<VideoInfo>> {
        return try {
            if (useAggregated) {
                // 使用聚合搜索
                val response = internalApiService.aggregatedSearch(keyword)
                if (response.isSuccessful) {
                    val videos = response.body()?.list ?: emptyList()
                    // 缓存搜索结果
                    videoDao.insertVideos(videos)
                    Result.success(videos)
                } else {
                    Result.failure(Exception("搜索失败: ${response.code()}"))
                }
            } else {
                // 使用单个源搜索
                searchFromEnabledSources(keyword, page)
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 从启用的API源搜索
     */
    private suspend fun searchFromEnabledSources(
        keyword: String,
        page: Int
    ): Result<List<VideoInfo>> {
        val enabledSources = apiSourceDao.getEnabledApiSources().first()
        val allResults = mutableListOf<VideoInfo>()
        
        for (source in enabledSources) {
            try {
                val response = apiService.searchVideos(
                    baseUrl = source.api,
                    keyword = keyword,
                    page = page
                )
                
                if (response.isSuccessful) {
                    val videos = response.body()?.list ?: emptyList()
                    // 添加源信息
                    val videosWithSource = videos.map { video ->
                        video.copy(
                            sourceName = source.name,
                            sourceCode = source.code
                        )
                    }
                    allResults.addAll(videosWithSource)
                }
            } catch (e: Exception) {
                // 单个源失败不影响其他源
                android.util.Log.w("VideoRepository", "Source ${source.name} failed: ${e.message}")
            }
        }
        
        // 缓存搜索结果
        if (allResults.isNotEmpty()) {
            videoDao.insertVideos(allResults)
        }
        
        return Result.success(allResults)
    }
    
    /**
     * 获取视频详情
     */
    suspend fun getVideoDetail(
        videoId: String,
        sourceCode: String,
        customApi: String? = null
    ): Result<VideoDetailInfo> {
        return try {
            val response = internalApiService.getDetail(
                id = videoId,
                source = sourceCode,
                customApi = customApi
            )
            
            if (response.isSuccessful) {
                val detail = response.body()?.videoInfo
                if (detail != null) {
                    Result.success(detail)
                } else {
                    Result.failure(Exception("视频详情为空"))
                }
            } else {
                Result.failure(Exception("获取详情失败: ${response.code()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 获取视频播放链接
     */
    suspend fun getVideoEpisodes(
        videoId: String,
        sourceCode: String,
        customApi: String? = null
    ): Result<List<String>> {
        return try {
            val response = internalApiService.getDetail(
                id = videoId,
                source = sourceCode,
                customApi = customApi
            )
            
            if (response.isSuccessful) {
                val episodes = response.body()?.episodes ?: emptyList()
                Result.success(episodes)
            } else {
                Result.failure(Exception("获取播放链接失败: ${response.code()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 获取收藏的视频
     */
    fun getFavoriteVideos(): Flow<List<VideoInfo>> {
        return videoDao.getFavoriteVideos()
    }
    
    /**
     * 更新收藏状态
     */
    suspend fun updateFavoriteStatus(videoId: String, isFavorite: Boolean) {
        videoDao.updateFavoriteStatus(videoId, isFavorite)
    }
    
    /**
     * 更新观看进度
     */
    suspend fun updateWatchProgress(videoId: String, progress: Long) {
        videoDao.updateWatchProgress(videoId, progress, System.currentTimeMillis())
    }
    
    /**
     * 本地搜索缓存的视频
     */
    suspend fun searchLocalVideos(query: String): List<VideoInfo> {
        return videoDao.searchVideos(query)
    }
    
    /**
     * 清理旧的缓存数据
     */
    suspend fun cleanOldCache(daysAgo: Int = 7) {
        val timestamp = System.currentTimeMillis() - (daysAgo * 24 * 60 * 60 * 1000L)
        videoDao.deleteOldVideos(timestamp)
    }
}
