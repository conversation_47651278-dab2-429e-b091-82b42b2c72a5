{"logs": [{"outputFile": "com.libretv.android.app-mergeDebugResources-2:/values-uk/values-uk.xml", "map": [{"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\2425a4573e4fb65053b44e46c3806d87\\transformed\\foundation-release\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,136,233", "endColumns": "80,96,99", "endOffsets": "131,228,328"}, "to": {"startLines": "23,183,184", "startColumns": "4,4,4", "startOffsets": "1006,15986,16083", "endColumns": "80,96,99", "endOffsets": "1082,16078,16178"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\6b27351f1a96970e2ba142187de324d4\\transformed\\media3-ui-1.7.1\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,626,956,1040,1122,1205,1305,1404,1489,1552,1650,1749,1820,1889,1955,2023,2149,2274,2411,2488,2570,2645,2733,2828,2921,2989,3074,3127,3187,3235,3296,3363,3431,3495,3562,3627,3687,3753,3805,3866,3951,4036,4091", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63", "endColumns": "17,12,12,83,81,82,99,98,84,62,97,98,70,68,65,67,125,124,136,76,81,74,87,94,92,67,84,52,59,47,60,66,67,63,66,64,59,65,51,60,84,84,54,66", "endOffsets": "282,621,951,1035,1117,1200,1300,1399,1484,1547,1645,1744,1815,1884,1950,2018,2144,2269,2406,2483,2565,2640,2728,2823,2916,2984,3069,3122,3182,3230,3291,3358,3426,3490,3557,3622,3682,3748,3800,3861,3946,4031,4086,4153"}, "to": {"startLines": "2,11,17,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,337,676,3904,3988,4070,4153,4253,4352,4437,4500,4598,4697,4768,4837,4903,4971,5097,5222,5359,5436,5518,5593,5681,5776,5869,5937,6710,6763,6823,6871,6932,6999,7067,7131,7198,7263,7323,7389,7441,7502,7587,7672,7727", "endLines": "10,16,22,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103", "endColumns": "17,12,12,83,81,82,99,98,84,62,97,98,70,68,65,67,125,124,136,76,81,74,87,94,92,67,84,52,59,47,60,66,67,63,66,64,59,65,51,60,84,84,54,66", "endOffsets": "332,671,1001,3983,4065,4148,4248,4347,4432,4495,4593,4692,4763,4832,4898,4966,5092,5217,5354,5431,5513,5588,5676,5771,5864,5932,6017,6758,6818,6866,6927,6994,7062,7126,7193,7258,7318,7384,7436,7497,7582,7667,7722,7789"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\955c05c442e591726e4833ad1e0fc303\\transformed\\core-1.16.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,358,459,564,669,782", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "150,252,353,454,559,664,777,878"}, "to": {"startLines": "24,25,26,27,28,29,30,179", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "1087,1187,1289,1390,1491,1596,1701,15612", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "1182,1284,1385,1486,1591,1696,1809,15708"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\0b493fc5385f598ec7f47c0a10bc88bb\\transformed\\material3-release\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,289,407,525,624,719,831,969,1085,1232,1316,1416,1509,1605,1721,1845,1950,2091,2228,2363,2552,2679,2803,2932,3053,3147,3248,3374,3504,3602,3707,3816,3961,4112,4220,4320,4395,4490,4586,4705,4791,4878,4977,5057,5143,5242,5346,5441,5541,5630,5737,5833,5936,6054,6134,6249", "endColumns": "117,115,117,117,98,94,111,137,115,146,83,99,92,95,115,123,104,140,136,134,188,126,123,128,120,93,100,125,129,97,104,108,144,150,107,99,74,94,95,118,85,86,98,79,85,98,103,94,99,88,106,95,102,117,79,114,105", "endOffsets": "168,284,402,520,619,714,826,964,1080,1227,1311,1411,1504,1600,1716,1840,1945,2086,2223,2358,2547,2674,2798,2927,3048,3142,3243,3369,3499,3597,3702,3811,3956,4107,4215,4315,4390,4485,4581,4700,4786,4873,4972,5052,5138,5237,5341,5436,5536,5625,5732,5828,5931,6049,6129,6244,6350"}, "to": {"startLines": "106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7965,8083,8199,8317,8435,8534,8629,8741,8879,8995,9142,9226,9326,9419,9515,9631,9755,9860,10001,10138,10273,10462,10589,10713,10842,10963,11057,11158,11284,11414,11512,11617,11726,11871,12022,12130,12230,12305,12400,12496,12615,12701,12788,12887,12967,13053,13152,13256,13351,13451,13540,13647,13743,13846,13964,14044,14159", "endColumns": "117,115,117,117,98,94,111,137,115,146,83,99,92,95,115,123,104,140,136,134,188,126,123,128,120,93,100,125,129,97,104,108,144,150,107,99,74,94,95,118,85,86,98,79,85,98,103,94,99,88,106,95,102,117,79,114,105", "endOffsets": "8078,8194,8312,8430,8529,8624,8736,8874,8990,9137,9221,9321,9414,9510,9626,9750,9855,9996,10133,10268,10457,10584,10708,10837,10958,11052,11153,11279,11409,11507,11612,11721,11866,12017,12125,12225,12300,12395,12491,12610,12696,12783,12882,12962,13048,13147,13251,13346,13446,13535,13642,13738,13841,13959,14039,14154,14260"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\26497276d619c831fa389bb2f1588ec8\\transformed\\media3-exoplayer-1.7.1\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,194,262,333,413,486,579,668", "endColumns": "73,64,67,70,79,72,92,88,74", "endOffsets": "124,189,257,328,408,481,574,663,738"}, "to": {"startLines": "78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6022,6096,6161,6229,6300,6380,6453,6546,6635", "endColumns": "73,64,67,70,79,72,92,88,74", "endOffsets": "6091,6156,6224,6295,6375,6448,6541,6630,6705"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\4c56c0fb8a9fdb1c153785f7a46f8077\\transformed\\media3-session-1.7.1\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,137,244,320,441,537,631,703,797,894,974,1040,1147,1231,1325,1403,1503,1582,1681,1755,1827,1910,1997,2096", "endColumns": "81,106,75,120,95,93,71,93,96,79,65,106,83,93,77,99,78,98,73,71,82,86,98,104", "endOffsets": "132,239,315,436,532,626,698,792,889,969,1035,1142,1226,1320,1398,1498,1577,1676,1750,1822,1905,1992,2091,2196"}, "to": {"startLines": "34,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,164,165,166,167,168,169", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2093,2360,2467,2543,2664,2760,2854,2926,3020,3117,3197,3263,3370,3454,3548,3626,3726,3805,14358,14432,14504,14587,14674,14773", "endColumns": "81,106,75,120,95,93,71,93,96,79,65,106,83,93,77,99,78,98,73,71,82,86,98,104", "endOffsets": "2170,2462,2538,2659,2755,2849,2921,3015,3112,3192,3258,3365,3449,3543,3621,3721,3800,3899,14427,14499,14582,14669,14768,14873"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\3bf0e9913a8fb17f961a2eb6334c8d97\\transformed\\material-release\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "92", "endOffsets": "143"}, "to": {"startLines": "163", "startColumns": "4", "startOffsets": "14265", "endColumns": "92", "endOffsets": "14353"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\406d1819cde1682167f3af9259cf68c0\\transformed\\ui-release\\res\\values-uk\\values-uk.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "187,280,364,466,567,651,733,822,910,992,1077,1165,1237,1326,1402,1479,1556,1636,1706", "endColumns": "92,83,101,100,83,81,88,87,81,84,87,71,88,75,76,76,79,69,122", "endOffsets": "275,359,461,562,646,728,817,905,987,1072,1160,1232,1321,1397,1474,1551,1631,1701,1824"}, "to": {"startLines": "31,32,33,35,36,104,105,170,171,172,173,174,175,176,177,178,180,181,182", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1814,1907,1991,2175,2276,7794,7876,14878,14966,15048,15133,15221,15293,15382,15458,15535,15713,15793,15863", "endColumns": "92,83,101,100,83,81,88,87,81,84,87,71,88,75,76,76,79,69,122", "endOffsets": "1902,1986,2088,2271,2355,7871,7960,14961,15043,15128,15216,15288,15377,15453,15530,15607,15788,15858,15981"}}]}]}