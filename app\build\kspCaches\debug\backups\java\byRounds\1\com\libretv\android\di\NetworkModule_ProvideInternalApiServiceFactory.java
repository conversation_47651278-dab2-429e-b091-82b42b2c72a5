package com.libretv.android.di;

import com.libretv.android.data.network.InternalApiService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;
import retrofit2.Retrofit;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("com.libretv.android.di.InternalRetrofit")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class NetworkModule_ProvideInternalApiServiceFactory implements Factory<InternalApiService> {
  private final Provider<Retrofit> retrofitProvider;

  public NetworkModule_ProvideInternalApiServiceFactory(Provider<Retrofit> retrofitProvider) {
    this.retrofitProvider = retrofitProvider;
  }

  @Override
  public InternalApiService get() {
    return provideInternalApiService(retrofitProvider.get());
  }

  public static NetworkModule_ProvideInternalApiServiceFactory create(
      Provider<Retrofit> retrofitProvider) {
    return new NetworkModule_ProvideInternalApiServiceFactory(retrofitProvider);
  }

  public static InternalApiService provideInternalApiService(Retrofit retrofit) {
    return Preconditions.checkNotNullFromProvides(NetworkModule.INSTANCE.provideInternalApiService(retrofit));
  }
}
