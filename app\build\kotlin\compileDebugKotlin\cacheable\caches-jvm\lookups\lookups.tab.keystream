  Activity android.app  Application android.app  
LibreTVApp android.app.Activity  enableEdgeToEdge android.app.Activity  installSplashScreen android.app.Activity  onCreate android.app.Activity  
setContent android.app.Activity  window android.app.Activity  onCreate android.app.Application  Context android.content  
LibreTVApp android.content.Context  applicationContext android.content.Context  	dataStore android.content.Context  enableEdgeToEdge android.content.Context  installSplashScreen android.content.Context  
setContent android.content.Context  
LibreTVApp android.content.ContextWrapper  enableEdgeToEdge android.content.ContextWrapper  installSplashScreen android.content.ContextWrapper  
setContent android.content.ContextWrapper  Build 
android.os  Bundle 
android.os  SDK_INT android.os.Build.VERSION  S android.os.Build.VERSION_CODES  d android.util.Log  e android.util.Log  w android.util.Log  View android.view  
LibreTVApp  android.view.ContextThemeWrapper  enableEdgeToEdge  android.view.ContextThemeWrapper  installSplashScreen  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  context android.view.View  isInEditMode android.view.View  statusBarColor android.view.Window  ComponentActivity androidx.activity  enableEdgeToEdge androidx.activity  Bundle #androidx.activity.ComponentActivity  	Companion #androidx.activity.ComponentActivity  
LibreTVApp #androidx.activity.ComponentActivity  enableEdgeToEdge #androidx.activity.ComponentActivity  installSplashScreen #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  
LibreTVApp -androidx.activity.ComponentActivity.Companion  enableEdgeToEdge -androidx.activity.ComponentActivity.Companion  installSplashScreen -androidx.activity.ComponentActivity.Companion  
setContent -androidx.activity.ComponentActivity.Companion  
setContent androidx.activity.compose  AnimatedContentScope androidx.compose.animation  
HistoryScreen /androidx.compose.animation.AnimatedContentScope  
HomeScreen /androidx.compose.animation.AnimatedContentScope  Screen /androidx.compose.animation.AnimatedContentScope  SearchScreen /androidx.compose.animation.AnimatedContentScope  SettingsScreen /androidx.compose.animation.AnimatedContentScope  isSystemInDarkTheme androidx.compose.foundation  AlertDialog "androidx.compose.foundation.layout  	Alignment "androidx.compose.foundation.layout  Arrangement "androidx.compose.foundation.layout  
AsyncImage "androidx.compose.foundation.layout  Boolean "androidx.compose.foundation.layout  Box "androidx.compose.foundation.layout  BoxScope "androidx.compose.foundation.layout  Button "androidx.compose.foundation.layout  Card "androidx.compose.foundation.layout  CardDefaults "androidx.compose.foundation.layout  CategoryGrid "androidx.compose.foundation.layout  ChevronRight "androidx.compose.foundation.layout  CircularProgressIndicator "androidx.compose.foundation.layout  ClickableSettingItem "androidx.compose.foundation.layout  Code "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  ContentScale "androidx.compose.foundation.layout  Date "androidx.compose.foundation.layout  Delete "androidx.compose.foundation.layout  DoubanMovieCard "androidx.compose.foundation.layout  
DoubanSubject "androidx.compose.foundation.layout  ExperimentalMaterial3Api "androidx.compose.foundation.layout  Folder "androidx.compose.foundation.layout  
FontWeight "androidx.compose.foundation.layout  
Fullscreen "androidx.compose.foundation.layout  	GridCells "androidx.compose.foundation.layout  GridView "androidx.compose.foundation.layout  History "androidx.compose.foundation.layout  HistoryItem "androidx.compose.foundation.layout  HistoryViewModel "androidx.compose.foundation.layout  
HomeViewModel "androidx.compose.foundation.layout  Icon "androidx.compose.foundation.layout  
IconButton "androidx.compose.foundation.layout  Icons "androidx.compose.foundation.layout  ImageVector "androidx.compose.foundation.layout  	ImeAction "androidx.compose.foundation.layout  Info "androidx.compose.foundation.layout  
LazyColumn "androidx.compose.foundation.layout  LazyRow "androidx.compose.foundation.layout  LazyVerticalGrid "androidx.compose.foundation.layout  LinearProgressIndicator "androidx.compose.foundation.layout  List "androidx.compose.foundation.layout  Locale "androidx.compose.foundation.layout  
MaterialTheme "androidx.compose.foundation.layout  Modifier "androidx.compose.foundation.layout  OptIn "androidx.compose.foundation.layout  OutlinedTextField "androidx.compose.foundation.layout  
PaddingValues "androidx.compose.foundation.layout  Pair "androidx.compose.foundation.layout  Palette "androidx.compose.foundation.layout  	PlayArrow "androidx.compose.foundation.layout  RoundedCornerShape "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  	SearchBar "androidx.compose.foundation.layout  SearchViewModel "androidx.compose.foundation.layout  SectionTitle "androidx.compose.foundation.layout  SettingsSection "androidx.compose.foundation.layout  SettingsViewModel "androidx.compose.foundation.layout  SimpleSearchField "androidx.compose.foundation.layout  Source "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  Storage "androidx.compose.foundation.layout  String "androidx.compose.foundation.layout  SuggestionChip "androidx.compose.foundation.layout  Switch "androidx.compose.foundation.layout  SwitchSettingItem "androidx.compose.foundation.layout  Text "androidx.compose.foundation.layout  
TextButton "androidx.compose.foundation.layout  TextOverflow "androidx.compose.foundation.layout  Unit "androidx.compose.foundation.layout  	VideoCard "androidx.compose.foundation.layout  	VideoInfo "androidx.compose.foundation.layout  Warning "androidx.compose.foundation.layout  WatchHistory "androidx.compose.foundation.layout  aspectRatio "androidx.compose.foundation.layout  
cardColors "androidx.compose.foundation.layout  
cardElevation "androidx.compose.foundation.layout  chunked "androidx.compose.foundation.layout  clip "androidx.compose.foundation.layout  coerceIn "androidx.compose.foundation.layout  collectAsState "androidx.compose.foundation.layout  com "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  forEach "androidx.compose.foundation.layout  getValue "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  isEmpty "androidx.compose.foundation.layout  
isNotBlank "androidx.compose.foundation.layout  
isNotEmpty "androidx.compose.foundation.layout  
isNullOrBlank "androidx.compose.foundation.layout  
isNullOrEmpty "androidx.compose.foundation.layout  let "androidx.compose.foundation.layout  listOf "androidx.compose.foundation.layout  mutableStateOf "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  provideDelegate "androidx.compose.foundation.layout  remember "androidx.compose.foundation.layout  repeat "androidx.compose.foundation.layout  setValue "androidx.compose.foundation.layout  size "androidx.compose.foundation.layout  spacedBy "androidx.compose.foundation.layout  to "androidx.compose.foundation.layout  weight "androidx.compose.foundation.layout  width "androidx.compose.foundation.layout  Center .androidx.compose.foundation.layout.Arrangement  
Horizontal .androidx.compose.foundation.layout.Arrangement  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  SpaceBetween .androidx.compose.foundation.layout.Arrangement  Vertical .androidx.compose.foundation.layout.Arrangement  spacedBy .androidx.compose.foundation.layout.Arrangement  	Alignment +androidx.compose.foundation.layout.BoxScope  
AsyncImage +androidx.compose.foundation.layout.BoxScope  CircularProgressIndicator +androidx.compose.foundation.layout.BoxScope  Column +androidx.compose.foundation.layout.BoxScope  ContentScale +androidx.compose.foundation.layout.BoxScope  LinearProgressIndicator +androidx.compose.foundation.layout.BoxScope  
MaterialTheme +androidx.compose.foundation.layout.BoxScope  Modifier +androidx.compose.foundation.layout.BoxScope  RoundedCornerShape +androidx.compose.foundation.layout.BoxScope  Text +androidx.compose.foundation.layout.BoxScope  align +androidx.compose.foundation.layout.BoxScope  aspectRatio +androidx.compose.foundation.layout.BoxScope  clip +androidx.compose.foundation.layout.BoxScope  coerceIn +androidx.compose.foundation.layout.BoxScope  dp +androidx.compose.foundation.layout.BoxScope  fillMaxWidth +androidx.compose.foundation.layout.BoxScope  padding +androidx.compose.foundation.layout.BoxScope  	Alignment .androidx.compose.foundation.layout.ColumnScope  Arrangement .androidx.compose.foundation.layout.ColumnScope  	ArrowBack .androidx.compose.foundation.layout.ColumnScope  
AsyncImage .androidx.compose.foundation.layout.ColumnScope  Box .androidx.compose.foundation.layout.ColumnScope  Button .androidx.compose.foundation.layout.ColumnScope  Card .androidx.compose.foundation.layout.ColumnScope  CardDefaults .androidx.compose.foundation.layout.ColumnScope  ChevronRight .androidx.compose.foundation.layout.ColumnScope  CircularProgressIndicator .androidx.compose.foundation.layout.ColumnScope  ClickableSettingItem .androidx.compose.foundation.layout.ColumnScope  Code .androidx.compose.foundation.layout.ColumnScope  Column .androidx.compose.foundation.layout.ColumnScope  ContentScale .androidx.compose.foundation.layout.ColumnScope  Date .androidx.compose.foundation.layout.ColumnScope  Delete .androidx.compose.foundation.layout.ColumnScope  Folder .androidx.compose.foundation.layout.ColumnScope  
FontWeight .androidx.compose.foundation.layout.ColumnScope  
Fullscreen .androidx.compose.foundation.layout.ColumnScope  	GridCells .androidx.compose.foundation.layout.ColumnScope  GridView .androidx.compose.foundation.layout.ColumnScope  History .androidx.compose.foundation.layout.ColumnScope  HistoryItem .androidx.compose.foundation.layout.ColumnScope  Icon .androidx.compose.foundation.layout.ColumnScope  
IconButton .androidx.compose.foundation.layout.ColumnScope  Icons .androidx.compose.foundation.layout.ColumnScope  Info .androidx.compose.foundation.layout.ColumnScope  
LazyColumn .androidx.compose.foundation.layout.ColumnScope  LazyRow .androidx.compose.foundation.layout.ColumnScope  LazyVerticalGrid .androidx.compose.foundation.layout.ColumnScope  LinearProgressIndicator .androidx.compose.foundation.layout.ColumnScope  
MaterialTheme .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  Palette .androidx.compose.foundation.layout.ColumnScope  	PlayArrow .androidx.compose.foundation.layout.ColumnScope  RoundedCornerShape .androidx.compose.foundation.layout.ColumnScope  Row .androidx.compose.foundation.layout.ColumnScope  SimpleSearchField .androidx.compose.foundation.layout.ColumnScope  Source .androidx.compose.foundation.layout.ColumnScope  Spacer .androidx.compose.foundation.layout.ColumnScope  Storage .androidx.compose.foundation.layout.ColumnScope  SuggestionChip .androidx.compose.foundation.layout.ColumnScope  SwitchSettingItem .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  TextOverflow .androidx.compose.foundation.layout.ColumnScope  	VideoCard .androidx.compose.foundation.layout.ColumnScope  Warning .androidx.compose.foundation.layout.ColumnScope  aspectRatio .androidx.compose.foundation.layout.ColumnScope  
cardColors .androidx.compose.foundation.layout.ColumnScope  chunked .androidx.compose.foundation.layout.ColumnScope  clip .androidx.compose.foundation.layout.ColumnScope  coerceIn .androidx.compose.foundation.layout.ColumnScope  content .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  fillMaxSize .androidx.compose.foundation.layout.ColumnScope  fillMaxWidth .androidx.compose.foundation.layout.ColumnScope  height .androidx.compose.foundation.layout.ColumnScope  invoke .androidx.compose.foundation.layout.ColumnScope  isEmpty .androidx.compose.foundation.layout.ColumnScope  
isNotBlank .androidx.compose.foundation.layout.ColumnScope  
isNotEmpty .androidx.compose.foundation.layout.ColumnScope  
isNullOrBlank .androidx.compose.foundation.layout.ColumnScope  
isNullOrEmpty .androidx.compose.foundation.layout.ColumnScope  items .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  repeat .androidx.compose.foundation.layout.ColumnScope  size .androidx.compose.foundation.layout.ColumnScope  spacedBy .androidx.compose.foundation.layout.ColumnScope  weight .androidx.compose.foundation.layout.ColumnScope  width .androidx.compose.foundation.layout.ColumnScope  	Alignment +androidx.compose.foundation.layout.RowScope  Arrangement +androidx.compose.foundation.layout.RowScope  	ArrowBack +androidx.compose.foundation.layout.RowScope  Card +androidx.compose.foundation.layout.RowScope  ChevronRight +androidx.compose.foundation.layout.RowScope  Column +androidx.compose.foundation.layout.RowScope  Date +androidx.compose.foundation.layout.RowScope  Delete +androidx.compose.foundation.layout.RowScope  
FontWeight +androidx.compose.foundation.layout.RowScope  Icon +androidx.compose.foundation.layout.RowScope  
IconButton +androidx.compose.foundation.layout.RowScope  Icons +androidx.compose.foundation.layout.RowScope  
MaterialTheme +androidx.compose.foundation.layout.RowScope  Modifier +androidx.compose.foundation.layout.RowScope  NavigationBarItem +androidx.compose.foundation.layout.RowScope  SimpleSearchField +androidx.compose.foundation.layout.RowScope  Spacer +androidx.compose.foundation.layout.RowScope  Switch +androidx.compose.foundation.layout.RowScope  Text +androidx.compose.foundation.layout.RowScope  	VideoCard +androidx.compose.foundation.layout.RowScope  any +androidx.compose.foundation.layout.RowScope  bottomNavItems +androidx.compose.foundation.layout.RowScope  currentBackStackEntryAsState +androidx.compose.foundation.layout.RowScope  dp +androidx.compose.foundation.layout.RowScope  fillMaxSize +androidx.compose.foundation.layout.RowScope  findStartDestination +androidx.compose.foundation.layout.RowScope  getValue +androidx.compose.foundation.layout.RowScope  height +androidx.compose.foundation.layout.RowScope  	hierarchy +androidx.compose.foundation.layout.RowScope  
isNotEmpty +androidx.compose.foundation.layout.RowScope  
isNullOrBlank +androidx.compose.foundation.layout.RowScope  padding +androidx.compose.foundation.layout.RowScope  provideDelegate +androidx.compose.foundation.layout.RowScope  repeat +androidx.compose.foundation.layout.RowScope  size +androidx.compose.foundation.layout.RowScope  weight +androidx.compose.foundation.layout.RowScope  width +androidx.compose.foundation.layout.RowScope  libretv &androidx.compose.foundation.layout.com  android .androidx.compose.foundation.layout.com.libretv  data 6androidx.compose.foundation.layout.com.libretv.android  model ;androidx.compose.foundation.layout.com.libretv.android.data  WatchHistory Aandroidx.compose.foundation.layout.com.libretv.android.data.model  
LazyColumn  androidx.compose.foundation.lazy  
LazyItemScope  androidx.compose.foundation.lazy  
LazyListScope  androidx.compose.foundation.lazy  LazyRow  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  	Alignment .androidx.compose.foundation.lazy.LazyItemScope  Arrangement .androidx.compose.foundation.lazy.LazyItemScope  Box .androidx.compose.foundation.lazy.LazyItemScope  Card .androidx.compose.foundation.lazy.LazyItemScope  CardDefaults .androidx.compose.foundation.lazy.LazyItemScope  CategoryGrid .androidx.compose.foundation.lazy.LazyItemScope  CircularProgressIndicator .androidx.compose.foundation.lazy.LazyItemScope  ClickableSettingItem .androidx.compose.foundation.lazy.LazyItemScope  Code .androidx.compose.foundation.lazy.LazyItemScope  Delete .androidx.compose.foundation.lazy.LazyItemScope  DoubanMovieCard .androidx.compose.foundation.lazy.LazyItemScope  Folder .androidx.compose.foundation.lazy.LazyItemScope  
FontWeight .androidx.compose.foundation.lazy.LazyItemScope  
Fullscreen .androidx.compose.foundation.lazy.LazyItemScope  GridView .androidx.compose.foundation.lazy.LazyItemScope  History .androidx.compose.foundation.lazy.LazyItemScope  HistoryItem .androidx.compose.foundation.lazy.LazyItemScope  Icons .androidx.compose.foundation.lazy.LazyItemScope  Info .androidx.compose.foundation.lazy.LazyItemScope  LazyRow .androidx.compose.foundation.lazy.LazyItemScope  
MaterialTheme .androidx.compose.foundation.lazy.LazyItemScope  Modifier .androidx.compose.foundation.lazy.LazyItemScope  
PaddingValues .androidx.compose.foundation.lazy.LazyItemScope  Palette .androidx.compose.foundation.lazy.LazyItemScope  	PlayArrow .androidx.compose.foundation.lazy.LazyItemScope  	SearchBar .androidx.compose.foundation.lazy.LazyItemScope  SectionTitle .androidx.compose.foundation.lazy.LazyItemScope  SettingsSection .androidx.compose.foundation.lazy.LazyItemScope  Source .androidx.compose.foundation.lazy.LazyItemScope  Storage .androidx.compose.foundation.lazy.LazyItemScope  SuggestionChip .androidx.compose.foundation.lazy.LazyItemScope  SwitchSettingItem .androidx.compose.foundation.lazy.LazyItemScope  Text .androidx.compose.foundation.lazy.LazyItemScope  	VideoCard .androidx.compose.foundation.lazy.LazyItemScope  Warning .androidx.compose.foundation.lazy.LazyItemScope  
cardColors .androidx.compose.foundation.lazy.LazyItemScope  dp .androidx.compose.foundation.lazy.LazyItemScope  fillMaxWidth .androidx.compose.foundation.lazy.LazyItemScope  items .androidx.compose.foundation.lazy.LazyItemScope  listOf .androidx.compose.foundation.lazy.LazyItemScope  padding .androidx.compose.foundation.lazy.LazyItemScope  spacedBy .androidx.compose.foundation.lazy.LazyItemScope  to .androidx.compose.foundation.lazy.LazyItemScope  width .androidx.compose.foundation.lazy.LazyItemScope  	Alignment .androidx.compose.foundation.lazy.LazyListScope  Arrangement .androidx.compose.foundation.lazy.LazyListScope  Box .androidx.compose.foundation.lazy.LazyListScope  Card .androidx.compose.foundation.lazy.LazyListScope  CardDefaults .androidx.compose.foundation.lazy.LazyListScope  CategoryGrid .androidx.compose.foundation.lazy.LazyListScope  CircularProgressIndicator .androidx.compose.foundation.lazy.LazyListScope  ClickableSettingItem .androidx.compose.foundation.lazy.LazyListScope  Code .androidx.compose.foundation.lazy.LazyListScope  Delete .androidx.compose.foundation.lazy.LazyListScope  DoubanMovieCard .androidx.compose.foundation.lazy.LazyListScope  Folder .androidx.compose.foundation.lazy.LazyListScope  
FontWeight .androidx.compose.foundation.lazy.LazyListScope  
Fullscreen .androidx.compose.foundation.lazy.LazyListScope  GridView .androidx.compose.foundation.lazy.LazyListScope  History .androidx.compose.foundation.lazy.LazyListScope  HistoryItem .androidx.compose.foundation.lazy.LazyListScope  Icons .androidx.compose.foundation.lazy.LazyListScope  Info .androidx.compose.foundation.lazy.LazyListScope  LazyRow .androidx.compose.foundation.lazy.LazyListScope  
MaterialTheme .androidx.compose.foundation.lazy.LazyListScope  Modifier .androidx.compose.foundation.lazy.LazyListScope  
PaddingValues .androidx.compose.foundation.lazy.LazyListScope  Palette .androidx.compose.foundation.lazy.LazyListScope  	PlayArrow .androidx.compose.foundation.lazy.LazyListScope  	SearchBar .androidx.compose.foundation.lazy.LazyListScope  SectionTitle .androidx.compose.foundation.lazy.LazyListScope  SettingsSection .androidx.compose.foundation.lazy.LazyListScope  Source .androidx.compose.foundation.lazy.LazyListScope  Storage .androidx.compose.foundation.lazy.LazyListScope  SuggestionChip .androidx.compose.foundation.lazy.LazyListScope  SwitchSettingItem .androidx.compose.foundation.lazy.LazyListScope  Text .androidx.compose.foundation.lazy.LazyListScope  	VideoCard .androidx.compose.foundation.lazy.LazyListScope  Warning .androidx.compose.foundation.lazy.LazyListScope  
cardColors .androidx.compose.foundation.lazy.LazyListScope  dp .androidx.compose.foundation.lazy.LazyListScope  fillMaxWidth .androidx.compose.foundation.lazy.LazyListScope  
isNotEmpty .androidx.compose.foundation.lazy.LazyListScope  item .androidx.compose.foundation.lazy.LazyListScope  items .androidx.compose.foundation.lazy.LazyListScope  let .androidx.compose.foundation.lazy.LazyListScope  listOf .androidx.compose.foundation.lazy.LazyListScope  padding .androidx.compose.foundation.lazy.LazyListScope  spacedBy .androidx.compose.foundation.lazy.LazyListScope  to .androidx.compose.foundation.lazy.LazyListScope  width .androidx.compose.foundation.lazy.LazyListScope  	GridCells %androidx.compose.foundation.lazy.grid  LazyGridItemScope %androidx.compose.foundation.lazy.grid  
LazyGridScope %androidx.compose.foundation.lazy.grid  LazyVerticalGrid %androidx.compose.foundation.lazy.grid  items %androidx.compose.foundation.lazy.grid  Fixed /androidx.compose.foundation.lazy.grid.GridCells  	VideoCard 7androidx.compose.foundation.lazy.grid.LazyGridItemScope  	VideoCard 3androidx.compose.foundation.lazy.grid.LazyGridScope  items 3androidx.compose.foundation.lazy.grid.LazyGridScope  RoundedCornerShape !androidx.compose.foundation.shape  KeyboardActionScope  androidx.compose.foundation.text  KeyboardActions  androidx.compose.foundation.text  KeyboardOptions  androidx.compose.foundation.text  Icons androidx.compose.material.icons  Default %androidx.compose.material.icons.Icons  Filled %androidx.compose.material.icons.Icons  	ArrowBack ,androidx.compose.material.icons.Icons.Filled  ChevronRight ,androidx.compose.material.icons.Icons.Filled  Clear ,androidx.compose.material.icons.Icons.Filled  Code ,androidx.compose.material.icons.Icons.Filled  Delete ,androidx.compose.material.icons.Icons.Filled  Folder ,androidx.compose.material.icons.Icons.Filled  
Fullscreen ,androidx.compose.material.icons.Icons.Filled  GridView ,androidx.compose.material.icons.Icons.Filled  History ,androidx.compose.material.icons.Icons.Filled  Home ,androidx.compose.material.icons.Icons.Filled  Info ,androidx.compose.material.icons.Icons.Filled  Palette ,androidx.compose.material.icons.Icons.Filled  	PlayArrow ,androidx.compose.material.icons.Icons.Filled  Search ,androidx.compose.material.icons.Icons.Filled  Settings ,androidx.compose.material.icons.Icons.Filled  Source ,androidx.compose.material.icons.Icons.Filled  Storage ,androidx.compose.material.icons.Icons.Filled  Warning ,androidx.compose.material.icons.Icons.Filled  	Alignment &androidx.compose.material.icons.filled  Arrangement &androidx.compose.material.icons.filled  	ArrowBack &androidx.compose.material.icons.filled  Boolean &androidx.compose.material.icons.filled  Card &androidx.compose.material.icons.filled  CardDefaults &androidx.compose.material.icons.filled  ChevronRight &androidx.compose.material.icons.filled  Clear &androidx.compose.material.icons.filled  ClickableSettingItem &androidx.compose.material.icons.filled  Code &androidx.compose.material.icons.filled  Column &androidx.compose.material.icons.filled  ColumnScope &androidx.compose.material.icons.filled  
Composable &androidx.compose.material.icons.filled  Delete &androidx.compose.material.icons.filled  ExperimentalMaterial3Api &androidx.compose.material.icons.filled  Folder &androidx.compose.material.icons.filled  
FontWeight &androidx.compose.material.icons.filled  
Fullscreen &androidx.compose.material.icons.filled  GridView &androidx.compose.material.icons.filled  History &androidx.compose.material.icons.filled  Home &androidx.compose.material.icons.filled  Icon &androidx.compose.material.icons.filled  Icons &androidx.compose.material.icons.filled  ImageVector &androidx.compose.material.icons.filled  Info &androidx.compose.material.icons.filled  
MaterialTheme &androidx.compose.material.icons.filled  Modifier &androidx.compose.material.icons.filled  OptIn &androidx.compose.material.icons.filled  Palette &androidx.compose.material.icons.filled  	PlayArrow &androidx.compose.material.icons.filled  Row &androidx.compose.material.icons.filled  Search &androidx.compose.material.icons.filled  Settings &androidx.compose.material.icons.filled  SettingsSection &androidx.compose.material.icons.filled  SettingsViewModel &androidx.compose.material.icons.filled  Source &androidx.compose.material.icons.filled  Storage &androidx.compose.material.icons.filled  String &androidx.compose.material.icons.filled  Switch &androidx.compose.material.icons.filled  SwitchSettingItem &androidx.compose.material.icons.filled  Text &androidx.compose.material.icons.filled  Unit &androidx.compose.material.icons.filled  Warning &androidx.compose.material.icons.filled  
cardColors &androidx.compose.material.icons.filled  collectAsState &androidx.compose.material.icons.filled  fillMaxSize &androidx.compose.material.icons.filled  fillMaxWidth &androidx.compose.material.icons.filled  getValue &androidx.compose.material.icons.filled  padding &androidx.compose.material.icons.filled  provideDelegate &androidx.compose.material.icons.filled  size &androidx.compose.material.icons.filled  spacedBy &androidx.compose.material.icons.filled  weight &androidx.compose.material.icons.filled  AlertDialog androidx.compose.material3  	Alignment androidx.compose.material3  Arrangement androidx.compose.material3  
AsyncImage androidx.compose.material3  Boolean androidx.compose.material3  Box androidx.compose.material3  Button androidx.compose.material3  Card androidx.compose.material3  
CardColors androidx.compose.material3  CardDefaults androidx.compose.material3  
CardElevation androidx.compose.material3  CategoryGrid androidx.compose.material3  ChevronRight androidx.compose.material3  CircularProgressIndicator androidx.compose.material3  ClickableSettingItem androidx.compose.material3  Code androidx.compose.material3  ColorScheme androidx.compose.material3  Column androidx.compose.material3  ColumnScope androidx.compose.material3  
Composable androidx.compose.material3  ContentScale androidx.compose.material3  Date androidx.compose.material3  Delete androidx.compose.material3  DoubanMovieCard androidx.compose.material3  
DoubanSubject androidx.compose.material3  ExperimentalMaterial3Api androidx.compose.material3  Folder androidx.compose.material3  
FontWeight androidx.compose.material3  
Fullscreen androidx.compose.material3  	GridCells androidx.compose.material3  GridView androidx.compose.material3  History androidx.compose.material3  HistoryItem androidx.compose.material3  
HistoryScreen androidx.compose.material3  HistoryViewModel androidx.compose.material3  
HomeScreen androidx.compose.material3  
HomeViewModel androidx.compose.material3  Icon androidx.compose.material3  
IconButton androidx.compose.material3  Icons androidx.compose.material3  ImageVector androidx.compose.material3  	ImeAction androidx.compose.material3  Info androidx.compose.material3  
LazyColumn androidx.compose.material3  LazyRow androidx.compose.material3  LazyVerticalGrid androidx.compose.material3  LinearProgressIndicator androidx.compose.material3  List androidx.compose.material3  Locale androidx.compose.material3  
MaterialTheme androidx.compose.material3  Modifier androidx.compose.material3  
NavigationBar androidx.compose.material3  NavigationBarItem androidx.compose.material3  OptIn androidx.compose.material3  OutlinedTextField androidx.compose.material3  
PaddingValues androidx.compose.material3  Pair androidx.compose.material3  Palette androidx.compose.material3  	PlayArrow androidx.compose.material3  RoundedCornerShape androidx.compose.material3  Row androidx.compose.material3  Scaffold androidx.compose.material3  Screen androidx.compose.material3  	SearchBar androidx.compose.material3  SearchScreen androidx.compose.material3  SearchViewModel androidx.compose.material3  SectionTitle androidx.compose.material3  SettingsScreen androidx.compose.material3  SettingsSection androidx.compose.material3  SettingsViewModel androidx.compose.material3  SimpleSearchField androidx.compose.material3  Source androidx.compose.material3  Spacer androidx.compose.material3  Storage androidx.compose.material3  String androidx.compose.material3  SuggestionChip androidx.compose.material3  Surface androidx.compose.material3  Switch androidx.compose.material3  SwitchSettingItem androidx.compose.material3  Text androidx.compose.material3  
TextButton androidx.compose.material3  TextOverflow androidx.compose.material3  
Typography androidx.compose.material3  Unit androidx.compose.material3  	VideoCard androidx.compose.material3  	VideoInfo androidx.compose.material3  Warning androidx.compose.material3  WatchHistory androidx.compose.material3  any androidx.compose.material3  aspectRatio androidx.compose.material3  bottomNavItems androidx.compose.material3  
cardColors androidx.compose.material3  
cardElevation androidx.compose.material3  chunked androidx.compose.material3  clip androidx.compose.material3  coerceIn androidx.compose.material3  collectAsState androidx.compose.material3  com androidx.compose.material3  currentBackStackEntryAsState androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  fillMaxSize androidx.compose.material3  fillMaxWidth androidx.compose.material3  findStartDestination androidx.compose.material3  forEach androidx.compose.material3  getValue androidx.compose.material3  height androidx.compose.material3  isEmpty androidx.compose.material3  
isNotBlank androidx.compose.material3  
isNotEmpty androidx.compose.material3  
isNullOrBlank androidx.compose.material3  
isNullOrEmpty androidx.compose.material3  let androidx.compose.material3  lightColorScheme androidx.compose.material3  listOf androidx.compose.material3  mutableStateOf androidx.compose.material3  padding androidx.compose.material3  provideDelegate androidx.compose.material3  remember androidx.compose.material3  repeat androidx.compose.material3  setValue androidx.compose.material3  size androidx.compose.material3  spacedBy androidx.compose.material3  to androidx.compose.material3  weight androidx.compose.material3  width androidx.compose.material3  
cardColors 'androidx.compose.material3.CardDefaults  
cardElevation 'androidx.compose.material3.CardDefaults  
background &androidx.compose.material3.ColorScheme  errorContainer &androidx.compose.material3.ColorScheme  onErrorContainer &androidx.compose.material3.ColorScheme  onSurfaceVariant &androidx.compose.material3.ColorScheme  primary &androidx.compose.material3.ColorScheme  surface &androidx.compose.material3.ColorScheme  colorScheme (androidx.compose.material3.MaterialTheme  
typography (androidx.compose.material3.MaterialTheme  	bodyLarge %androidx.compose.material3.Typography  
bodyMedium %androidx.compose.material3.Typography  	bodySmall %androidx.compose.material3.Typography  headlineMedium %androidx.compose.material3.Typography  labelMedium %androidx.compose.material3.Typography  
labelSmall %androidx.compose.material3.Typography  
titleLarge %androidx.compose.material3.Typography  titleMedium %androidx.compose.material3.Typography  
titleSmall %androidx.compose.material3.Typography  libretv androidx.compose.material3.com  android &androidx.compose.material3.com.libretv  data .androidx.compose.material3.com.libretv.android  model 3androidx.compose.material3.com.libretv.android.data  WatchHistory 9androidx.compose.material3.com.libretv.android.data.model  AlertDialog androidx.compose.runtime  	Alignment androidx.compose.runtime  Arrangement androidx.compose.runtime  Boolean androidx.compose.runtime  Box androidx.compose.runtime  Button androidx.compose.runtime  Card androidx.compose.runtime  CardDefaults androidx.compose.runtime  CategoryGrid androidx.compose.runtime  ChevronRight androidx.compose.runtime  CircularProgressIndicator androidx.compose.runtime  ClickableSettingItem androidx.compose.runtime  Code androidx.compose.runtime  Column androidx.compose.runtime  ColumnScope androidx.compose.runtime  
Composable androidx.compose.runtime  Date androidx.compose.runtime  Delete androidx.compose.runtime  DoubanMovieCard androidx.compose.runtime  ExperimentalMaterial3Api androidx.compose.runtime  Folder androidx.compose.runtime  
FontWeight androidx.compose.runtime  
Fullscreen androidx.compose.runtime  	GridCells androidx.compose.runtime  GridView androidx.compose.runtime  History androidx.compose.runtime  HistoryItem androidx.compose.runtime  
HistoryScreen androidx.compose.runtime  HistoryViewModel androidx.compose.runtime  
HomeScreen androidx.compose.runtime  
HomeViewModel androidx.compose.runtime  Icon androidx.compose.runtime  
IconButton androidx.compose.runtime  Icons androidx.compose.runtime  ImageVector androidx.compose.runtime  	ImeAction androidx.compose.runtime  Info androidx.compose.runtime  
LazyColumn androidx.compose.runtime  LazyRow androidx.compose.runtime  LazyVerticalGrid androidx.compose.runtime  List androidx.compose.runtime  Locale androidx.compose.runtime  
MaterialTheme androidx.compose.runtime  Modifier androidx.compose.runtime  MutableState androidx.compose.runtime  
NavigationBar androidx.compose.runtime  NavigationBarItem androidx.compose.runtime  OptIn androidx.compose.runtime  OutlinedTextField androidx.compose.runtime  
PaddingValues androidx.compose.runtime  Pair androidx.compose.runtime  Palette androidx.compose.runtime  	PlayArrow androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  Row androidx.compose.runtime  Scaffold androidx.compose.runtime  Screen androidx.compose.runtime  	SearchBar androidx.compose.runtime  SearchScreen androidx.compose.runtime  SearchViewModel androidx.compose.runtime  SectionTitle androidx.compose.runtime  SettingsScreen androidx.compose.runtime  SettingsSection androidx.compose.runtime  SettingsViewModel androidx.compose.runtime  
SideEffect androidx.compose.runtime  SimpleSearchField androidx.compose.runtime  Source androidx.compose.runtime  Spacer androidx.compose.runtime  State androidx.compose.runtime  Storage androidx.compose.runtime  String androidx.compose.runtime  SuggestionChip androidx.compose.runtime  Switch androidx.compose.runtime  SwitchSettingItem androidx.compose.runtime  Text androidx.compose.runtime  
TextButton androidx.compose.runtime  Unit androidx.compose.runtime  	VideoCard androidx.compose.runtime  Warning androidx.compose.runtime  any androidx.compose.runtime  bottomNavItems androidx.compose.runtime  
cardColors androidx.compose.runtime  chunked androidx.compose.runtime  collectAsState androidx.compose.runtime  com androidx.compose.runtime  currentBackStackEntryAsState androidx.compose.runtime  fillMaxSize androidx.compose.runtime  fillMaxWidth androidx.compose.runtime  findStartDestination androidx.compose.runtime  forEach androidx.compose.runtime  getValue androidx.compose.runtime  height androidx.compose.runtime  isEmpty androidx.compose.runtime  
isNotEmpty androidx.compose.runtime  
isNullOrBlank androidx.compose.runtime  
isNullOrEmpty androidx.compose.runtime  let androidx.compose.runtime  listOf androidx.compose.runtime  mutableStateOf androidx.compose.runtime  padding androidx.compose.runtime  provideDelegate androidx.compose.runtime  remember androidx.compose.runtime  repeat androidx.compose.runtime  setValue androidx.compose.runtime  size androidx.compose.runtime  spacedBy androidx.compose.runtime  to androidx.compose.runtime  weight androidx.compose.runtime  width androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  setValue %androidx.compose.runtime.MutableState  current 3androidx.compose.runtime.ProvidableCompositionLocal  getValue androidx.compose.runtime.State  provideDelegate androidx.compose.runtime.State  libretv androidx.compose.runtime.com  android $androidx.compose.runtime.com.libretv  data ,androidx.compose.runtime.com.libretv.android  model 1androidx.compose.runtime.com.libretv.android.data  WatchHistory 7androidx.compose.runtime.com.libretv.android.data.model  ComposableFunction0 !androidx.compose.runtime.internal  ComposableFunction1 !androidx.compose.runtime.internal  ComposableFunction2 !androidx.compose.runtime.internal  invoke 5androidx.compose.runtime.internal.ComposableFunction1  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  BottomCenter androidx.compose.ui.Alignment  Center androidx.compose.ui.Alignment  CenterHorizontally androidx.compose.ui.Alignment  CenterVertically androidx.compose.ui.Alignment  	Companion androidx.compose.ui.Alignment  
Horizontal androidx.compose.ui.Alignment  Vertical androidx.compose.ui.Alignment  BottomCenter 'androidx.compose.ui.Alignment.Companion  Center 'androidx.compose.ui.Alignment.Companion  CenterHorizontally 'androidx.compose.ui.Alignment.Companion  CenterVertically 'androidx.compose.ui.Alignment.Companion  	Companion androidx.compose.ui.Modifier  align androidx.compose.ui.Modifier  aspectRatio androidx.compose.ui.Modifier  clip androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  height androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  size androidx.compose.ui.Modifier  weight androidx.compose.ui.Modifier  width androidx.compose.ui.Modifier  fillMaxSize &androidx.compose.ui.Modifier.Companion  fillMaxWidth &androidx.compose.ui.Modifier.Companion  height &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  size &androidx.compose.ui.Modifier.Companion  weight &androidx.compose.ui.Modifier.Companion  width &androidx.compose.ui.Modifier.Companion  clip androidx.compose.ui.draw  FocusRequester androidx.compose.ui.focus  focusRequester androidx.compose.ui.focus  Color androidx.compose.ui.graphics  toArgb androidx.compose.ui.graphics  	Companion "androidx.compose.ui.graphics.Color  White "androidx.compose.ui.graphics.Color  toArgb "androidx.compose.ui.graphics.Color  White ,androidx.compose.ui.graphics.Color.Companion  ImageVector #androidx.compose.ui.graphics.vector  ContentScale androidx.compose.ui.layout  	Companion 'androidx.compose.ui.layout.ContentScale  Crop 'androidx.compose.ui.layout.ContentScale  Crop 1androidx.compose.ui.layout.ContentScale.Companion  LocalContext androidx.compose.ui.platform  LocalSoftwareKeyboardController androidx.compose.ui.platform  	LocalView androidx.compose.ui.platform  SoftwareKeyboardController androidx.compose.ui.platform  hide 7androidx.compose.ui.platform.SoftwareKeyboardController  	TextStyle androidx.compose.ui.text  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  	Companion (androidx.compose.ui.text.font.FontFamily  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  Bold (androidx.compose.ui.text.font.FontWeight  	Companion (androidx.compose.ui.text.font.FontWeight  Medium (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  Medium 2androidx.compose.ui.text.font.FontWeight.Companion  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  	ImeAction androidx.compose.ui.text.input  	Companion (androidx.compose.ui.text.input.ImeAction  Search (androidx.compose.ui.text.input.ImeAction  Search 2androidx.compose.ui.text.input.ImeAction.Companion  TextOverflow androidx.compose.ui.text.style  	Companion +androidx.compose.ui.text.style.TextOverflow  Ellipsis +androidx.compose.ui.text.style.TextOverflow  Ellipsis 5androidx.compose.ui.text.style.TextOverflow.Companion  Dp androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  Bundle #androidx.core.app.ComponentActivity  
LibreTVApp #androidx.core.app.ComponentActivity  enableEdgeToEdge #androidx.core.app.ComponentActivity  installSplashScreen #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  SplashScreen androidx.core.splashscreen  installSplashScreen 1androidx.core.splashscreen.SplashScreen.Companion  WindowCompat androidx.core.view  getInsetsController androidx.core.view.WindowCompat  isAppearanceLightStatusBars /androidx.core.view.WindowInsetsControllerCompat  	DataStore androidx.datastore.core  data !androidx.datastore.core.DataStore  edit !androidx.datastore.core.DataStore  preferencesDataStore androidx.datastore.preferences  ADULT_CONTENT_ENABLED #androidx.datastore.preferences.core  AUTO_CLEAN_CACHE #androidx.datastore.preferences.core  AUTO_PLAY_NEXT #androidx.datastore.preferences.core  ApplicationContext #androidx.datastore.preferences.core  Boolean #androidx.datastore.preferences.core  
CACHE_ENABLED #androidx.datastore.preferences.core  
CACHE_SIZE_MB #androidx.datastore.preferences.core  Context #androidx.datastore.preferences.core  	DataStore #androidx.datastore.preferences.core  FIRST_LAUNCH #androidx.datastore.preferences.core  Float #androidx.datastore.preferences.core  Flow #androidx.datastore.preferences.core  GRID_COLUMNS #androidx.datastore.preferences.core  Inject #androidx.datastore.preferences.core  Int #androidx.datastore.preferences.core  LAST_VERSION_CODE #androidx.datastore.preferences.core  MutablePreferences #androidx.datastore.preferences.core  PASSWORD_ENABLED #androidx.datastore.preferences.core  
PASSWORD_HASH #androidx.datastore.preferences.core  PLAYER_AUTO_FULLSCREEN #androidx.datastore.preferences.core  PLAYER_BRIGHTNESS #androidx.datastore.preferences.core  PLAYER_SPEED #androidx.datastore.preferences.core  
PLAYER_VOLUME #androidx.datastore.preferences.core  	PROXY_URL #androidx.datastore.preferences.core  Preferences #androidx.datastore.preferences.core  REMEMBER_PROGRESS #androidx.datastore.preferences.core  SHOW_ADULT_WARNING #androidx.datastore.preferences.core  	Singleton #androidx.datastore.preferences.core  String #androidx.datastore.preferences.core  
THEME_MODE #androidx.datastore.preferences.core  TIMEOUT_SECONDS #androidx.datastore.preferences.core  	USE_PROXY #androidx.datastore.preferences.core  booleanPreferencesKey #androidx.datastore.preferences.core  edit #androidx.datastore.preferences.core  floatPreferencesKey #androidx.datastore.preferences.core  intPreferencesKey #androidx.datastore.preferences.core  map #androidx.datastore.preferences.core  preferencesDataStore #androidx.datastore.preferences.core  provideDelegate #androidx.datastore.preferences.core  stringPreferencesKey #androidx.datastore.preferences.core  clear 6androidx.datastore.preferences.core.MutablePreferences  set 6androidx.datastore.preferences.core.MutablePreferences  Key /androidx.datastore.preferences.core.Preferences  get /androidx.datastore.preferences.core.Preferences  
hiltViewModel  androidx.hilt.navigation.compose  	ViewModel androidx.lifecycle  viewModelScope androidx.lifecycle  NavBackStackEntry androidx.navigation  NavDestination androidx.navigation  NavGraph androidx.navigation  NavGraphBuilder androidx.navigation  NavHostController androidx.navigation  NavOptionsBuilder androidx.navigation  PopUpToBuilder androidx.navigation  destination %androidx.navigation.NavBackStackEntry  graph !androidx.navigation.NavController  navigate !androidx.navigation.NavController  popBackStack !androidx.navigation.NavController  	hierarchy "androidx.navigation.NavDestination  id "androidx.navigation.NavDestination  route "androidx.navigation.NavDestination  	hierarchy ,androidx.navigation.NavDestination.Companion  findStartDestination androidx.navigation.NavGraph  findStartDestination &androidx.navigation.NavGraph.Companion  
HistoryScreen #androidx.navigation.NavGraphBuilder  
HomeScreen #androidx.navigation.NavGraphBuilder  Screen #androidx.navigation.NavGraphBuilder  SearchScreen #androidx.navigation.NavGraphBuilder  SettingsScreen #androidx.navigation.NavGraphBuilder  
composable #androidx.navigation.NavGraphBuilder  currentBackStackEntryAsState %androidx.navigation.NavHostController  graph %androidx.navigation.NavHostController  navigate %androidx.navigation.NavHostController  popBackStack %androidx.navigation.NavHostController  findStartDestination %androidx.navigation.NavOptionsBuilder  launchSingleTop %androidx.navigation.NavOptionsBuilder  popUpTo %androidx.navigation.NavOptionsBuilder  restoreState %androidx.navigation.NavOptionsBuilder  	saveState "androidx.navigation.PopUpToBuilder  NavHost androidx.navigation.compose  
composable androidx.navigation.compose  currentBackStackEntryAsState androidx.navigation.compose  rememberNavController androidx.navigation.compose  	ApiSource 
androidx.room  Boolean 
androidx.room  Dao 
androidx.room  Database 
androidx.room  Delete 
androidx.room  Entity 
androidx.room  EntityDeleteOrUpdateAdapter 
androidx.room  EntityInsertAdapter 
androidx.room  Flow 
androidx.room  Insert 
androidx.room  Int 
androidx.room  InvalidationTracker 
androidx.room  List 
androidx.room  Long 
androidx.room  OnConflictStrategy 
androidx.room  
PrimaryKey 
androidx.room  Query 
androidx.room  Room 
androidx.room  RoomDatabase 
androidx.room  RoomOpenDelegate 
androidx.room  
SearchHistory 
androidx.room  String 
androidx.room  
TypeConverter 
androidx.room  TypeConverters 
androidx.room  Update 
androidx.room  	VideoInfo 
androidx.room  WatchHistory 
androidx.room  handle )androidx.room.EntityDeleteOrUpdateAdapter  insert !androidx.room.EntityInsertAdapter  	Companion  androidx.room.OnConflictStrategy  REPLACE  androidx.room.OnConflictStrategy  REPLACE *androidx.room.OnConflictStrategy.Companion  databaseBuilder androidx.room.Room  ApiSourceDao androidx.room.RoomDatabase  ApiSourceDao_Impl androidx.room.RoomDatabase  AutoMigrationSpec androidx.room.RoomDatabase  Builder androidx.room.RoomDatabase  	Companion androidx.room.RoomDatabase  Context androidx.room.RoomDatabase  InvalidationTracker androidx.room.RoomDatabase  KClass androidx.room.RoomDatabase  Lazy androidx.room.RoomDatabase  LibreTVDatabase androidx.room.RoomDatabase  List androidx.room.RoomDatabase  Map androidx.room.RoomDatabase  	Migration androidx.room.RoomDatabase  MutableList androidx.room.RoomDatabase  
MutableMap androidx.room.RoomDatabase  
MutableSet androidx.room.RoomDatabase  Room androidx.room.RoomDatabase  RoomOpenDelegate androidx.room.RoomDatabase  SQLiteConnection androidx.room.RoomDatabase  SearchHistoryDao androidx.room.RoomDatabase  SearchHistoryDao_Impl androidx.room.RoomDatabase  Set androidx.room.RoomDatabase  String androidx.room.RoomDatabase  	TableInfo androidx.room.RoomDatabase  VideoDao androidx.room.RoomDatabase  
VideoDao_Impl androidx.room.RoomDatabase  WatchHistoryDao androidx.room.RoomDatabase  WatchHistoryDao_Impl androidx.room.RoomDatabase  databaseBuilder androidx.room.RoomDatabase  dropFtsSyncTriggers androidx.room.RoomDatabase  execSQL androidx.room.RoomDatabase  getRequiredConverters androidx.room.RoomDatabase  internalInitInvalidationTracker androidx.room.RoomDatabase  java androidx.room.RoomDatabase  lazy androidx.room.RoomDatabase  
mutableListOf androidx.room.RoomDatabase  mutableMapOf androidx.room.RoomDatabase  mutableSetOf androidx.room.RoomDatabase  performClear androidx.room.RoomDatabase  read androidx.room.RoomDatabase  
trimMargin androidx.room.RoomDatabase  build "androidx.room.RoomDatabase.Builder  fallbackToDestructiveMigration "androidx.room.RoomDatabase.Builder  ApiSourceDao $androidx.room.RoomDatabase.Companion  ApiSourceDao_Impl $androidx.room.RoomDatabase.Companion  InvalidationTracker $androidx.room.RoomDatabase.Companion  LibreTVDatabase $androidx.room.RoomDatabase.Companion  Room $androidx.room.RoomDatabase.Companion  RoomOpenDelegate $androidx.room.RoomDatabase.Companion  SearchHistoryDao $androidx.room.RoomDatabase.Companion  SearchHistoryDao_Impl $androidx.room.RoomDatabase.Companion  	TableInfo $androidx.room.RoomDatabase.Companion  VideoDao $androidx.room.RoomDatabase.Companion  
VideoDao_Impl $androidx.room.RoomDatabase.Companion  WatchHistoryDao $androidx.room.RoomDatabase.Companion  WatchHistoryDao_Impl $androidx.room.RoomDatabase.Companion  databaseBuilder $androidx.room.RoomDatabase.Companion  dropFtsSyncTriggers $androidx.room.RoomDatabase.Companion  execSQL $androidx.room.RoomDatabase.Companion  getRequiredConverters $androidx.room.RoomDatabase.Companion  internalInitInvalidationTracker $androidx.room.RoomDatabase.Companion  java $androidx.room.RoomDatabase.Companion  lazy $androidx.room.RoomDatabase.Companion  
mutableListOf $androidx.room.RoomDatabase.Companion  mutableMapOf $androidx.room.RoomDatabase.Companion  mutableSetOf $androidx.room.RoomDatabase.Companion  read $androidx.room.RoomDatabase.Companion  
trimMargin $androidx.room.RoomDatabase.Companion  ValidationResult +androidx.room.RoomDatabase.RoomOpenDelegate  Column $androidx.room.RoomDatabase.TableInfo  
ForeignKey $androidx.room.RoomDatabase.TableInfo  Index $androidx.room.RoomDatabase.TableInfo  
MutableMap androidx.room.RoomOpenDelegate  
MutableSet androidx.room.RoomOpenDelegate  RoomOpenDelegate androidx.room.RoomOpenDelegate  SQLiteConnection androidx.room.RoomOpenDelegate  String androidx.room.RoomOpenDelegate  	TableInfo androidx.room.RoomOpenDelegate  ValidationResult androidx.room.RoomOpenDelegate  dropFtsSyncTriggers androidx.room.RoomOpenDelegate  execSQL androidx.room.RoomOpenDelegate  internalInitInvalidationTracker androidx.room.RoomOpenDelegate  mutableMapOf androidx.room.RoomOpenDelegate  mutableSetOf androidx.room.RoomOpenDelegate  read androidx.room.RoomOpenDelegate  
trimMargin androidx.room.RoomOpenDelegate  ValidationResult /androidx.room.RoomOpenDelegate.RoomOpenDelegate  Column (androidx.room.RoomOpenDelegate.TableInfo  
ForeignKey (androidx.room.RoomOpenDelegate.TableInfo  Index (androidx.room.RoomOpenDelegate.TableInfo  
createFlow androidx.room.coroutines  AutoMigrationSpec androidx.room.migration  	Migration androidx.room.migration  	TableInfo androidx.room.util  dropFtsSyncTriggers androidx.room.util  getColumnIndexOrThrow androidx.room.util  performSuspending androidx.room.util  CREATED_FROM_ENTITY androidx.room.util.TableInfo  Column androidx.room.util.TableInfo  	Companion androidx.room.util.TableInfo  
ForeignKey androidx.room.util.TableInfo  Index androidx.room.util.TableInfo  equals androidx.room.util.TableInfo  CREATED_FROM_ENTITY &androidx.room.util.TableInfo.Companion  read &androidx.room.util.TableInfo.Companion  SQLiteConnection androidx.sqlite  SQLiteStatement androidx.sqlite  execSQL androidx.sqlite  execSQL  androidx.sqlite.SQLiteConnection  prepare  androidx.sqlite.SQLiteConnection  bindLong androidx.sqlite.SQLiteStatement  bindNull androidx.sqlite.SQLiteStatement  bindText androidx.sqlite.SQLiteStatement  close androidx.sqlite.SQLiteStatement  getLong androidx.sqlite.SQLiteStatement  getText androidx.sqlite.SQLiteStatement  isNull androidx.sqlite.SQLiteStatement  step androidx.sqlite.SQLiteStatement  
AsyncImage coil.compose  Boolean com.example.shipin.ui.theme  Build com.example.shipin.ui.theme  
Composable com.example.shipin.ui.theme  DarkColorScheme com.example.shipin.ui.theme  
FontFamily com.example.shipin.ui.theme  
FontWeight com.example.shipin.ui.theme  LightColorScheme com.example.shipin.ui.theme  Pink40 com.example.shipin.ui.theme  Pink80 com.example.shipin.ui.theme  Purple40 com.example.shipin.ui.theme  Purple80 com.example.shipin.ui.theme  PurpleGrey40 com.example.shipin.ui.theme  PurpleGrey80 com.example.shipin.ui.theme  ShipinTheme com.example.shipin.ui.theme  
Typography com.example.shipin.ui.theme  Unit com.example.shipin.ui.theme  Gson com.google.gson  GsonBuilder com.google.gson  fromJson com.google.gson.Gson  toJson com.google.gson.Gson  create com.google.gson.GsonBuilder  
setLenient com.google.gson.GsonBuilder  	TypeToken com.google.gson.reflect  type !com.google.gson.reflect.TypeToken  Application com.libretv.android  HiltAndroidApp com.libretv.android  LibreTVApplication com.libretv.android  
initializeApp &com.libretv.android.LibreTVApplication  	ApiSource !com.libretv.android.data.database  ApiSourceDao !com.libretv.android.data.database  ApiSourceDao_Impl !com.libretv.android.data.database  ApplicationContext !com.libretv.android.data.database  AutoMigrationSpec !com.libretv.android.data.database  Boolean !com.libretv.android.data.database  Context !com.libretv.android.data.database  
Converters !com.libretv.android.data.database  Dao !com.libretv.android.data.database  Database !com.libretv.android.data.database  Delete !com.libretv.android.data.database  EntityDeleteOrUpdateAdapter !com.libretv.android.data.database  EntityInsertAdapter !com.libretv.android.data.database  Flow !com.libretv.android.data.database  	Generated !com.libretv.android.data.database  Gson !com.libretv.android.data.database  Insert !com.libretv.android.data.database  	InstallIn !com.libretv.android.data.database  Int !com.libretv.android.data.database  InvalidationTracker !com.libretv.android.data.database  KClass !com.libretv.android.data.database  Lazy !com.libretv.android.data.database  LibreTVDatabase !com.libretv.android.data.database  LibreTVDatabase_Impl !com.libretv.android.data.database  List !com.libretv.android.data.database  Long !com.libretv.android.data.database  Map !com.libretv.android.data.database  	Migration !com.libretv.android.data.database  Module !com.libretv.android.data.database  MutableList !com.libretv.android.data.database  
MutableMap !com.libretv.android.data.database  
MutableSet !com.libretv.android.data.database  OnConflictStrategy !com.libretv.android.data.database  Provides !com.libretv.android.data.database  Query !com.libretv.android.data.database  Room !com.libretv.android.data.database  RoomDatabase !com.libretv.android.data.database  RoomOpenDelegate !com.libretv.android.data.database  SQLiteConnection !com.libretv.android.data.database  SQLiteStatement !com.libretv.android.data.database  
SearchHistory !com.libretv.android.data.database  SearchHistoryDao !com.libretv.android.data.database  SearchHistoryDao_Impl !com.libretv.android.data.database  Set !com.libretv.android.data.database  	Singleton !com.libretv.android.data.database  SingletonComponent !com.libretv.android.data.database  String !com.libretv.android.data.database  Suppress !com.libretv.android.data.database  	TableInfo !com.libretv.android.data.database  
TypeConverter !com.libretv.android.data.database  TypeConverters !com.libretv.android.data.database  	TypeToken !com.libretv.android.data.database  Unit !com.libretv.android.data.database  Update !com.libretv.android.data.database  VideoDao !com.libretv.android.data.database  
VideoDao_Impl !com.libretv.android.data.database  	VideoInfo !com.libretv.android.data.database  WatchHistory !com.libretv.android.data.database  WatchHistoryDao !com.libretv.android.data.database  WatchHistoryDao_Impl !com.libretv.android.data.database  __converters !com.libretv.android.data.database  arrayOf !com.libretv.android.data.database  create !com.libretv.android.data.database  
createFlow !com.libretv.android.data.database  databaseBuilder !com.libretv.android.data.database  dropFtsSyncTriggers !com.libretv.android.data.database  	emptyList !com.libretv.android.data.database  execSQL !com.libretv.android.data.database  getColumnIndexOrThrow !com.libretv.android.data.database  getRequiredConverters !com.libretv.android.data.database  internalInitInvalidationTracker !com.libretv.android.data.database  java !com.libretv.android.data.database  lazy !com.libretv.android.data.database  
mutableListOf !com.libretv.android.data.database  mutableMapOf !com.libretv.android.data.database  mutableSetOf !com.libretv.android.data.database  performSuspending !com.libretv.android.data.database  read !com.libretv.android.data.database  
trimMargin !com.libretv.android.data.database  OnConflictStrategy .com.libretv.android.data.database.ApiSourceDao  deleteAllCustomSources .com.libretv.android.data.database.ApiSourceDao  deleteApiSource .com.libretv.android.data.database.ApiSourceDao  getAdultApiSources .com.libretv.android.data.database.ApiSourceDao  getAllApiSources .com.libretv.android.data.database.ApiSourceDao  getApiSourceByCode .com.libretv.android.data.database.ApiSourceDao  getCustomApiSources .com.libretv.android.data.database.ApiSourceDao  getEnabledApiSources .com.libretv.android.data.database.ApiSourceDao  getNormalApiSources .com.libretv.android.data.database.ApiSourceDao  insertApiSource .com.libretv.android.data.database.ApiSourceDao  insertApiSources .com.libretv.android.data.database.ApiSourceDao  updateApiSource .com.libretv.android.data.database.ApiSourceDao  updateEnabledStatus .com.libretv.android.data.database.ApiSourceDao  updateOrder .com.libretv.android.data.database.ApiSourceDao  	ApiSource 3com.libretv.android.data.database.ApiSourceDao_Impl  Boolean 3com.libretv.android.data.database.ApiSourceDao_Impl  	Companion 3com.libretv.android.data.database.ApiSourceDao_Impl  EntityDeleteOrUpdateAdapter 3com.libretv.android.data.database.ApiSourceDao_Impl  EntityInsertAdapter 3com.libretv.android.data.database.ApiSourceDao_Impl  Flow 3com.libretv.android.data.database.ApiSourceDao_Impl  Int 3com.libretv.android.data.database.ApiSourceDao_Impl  KClass 3com.libretv.android.data.database.ApiSourceDao_Impl  List 3com.libretv.android.data.database.ApiSourceDao_Impl  MutableList 3com.libretv.android.data.database.ApiSourceDao_Impl  RoomDatabase 3com.libretv.android.data.database.ApiSourceDao_Impl  SQLiteStatement 3com.libretv.android.data.database.ApiSourceDao_Impl  String 3com.libretv.android.data.database.ApiSourceDao_Impl  Unit 3com.libretv.android.data.database.ApiSourceDao_Impl  __db 3com.libretv.android.data.database.ApiSourceDao_Impl  __deleteAdapterOfApiSource 3com.libretv.android.data.database.ApiSourceDao_Impl  __insertAdapterOfApiSource 3com.libretv.android.data.database.ApiSourceDao_Impl  __updateAdapterOfApiSource 3com.libretv.android.data.database.ApiSourceDao_Impl  arrayOf 3com.libretv.android.data.database.ApiSourceDao_Impl  
createFlow 3com.libretv.android.data.database.ApiSourceDao_Impl  	emptyList 3com.libretv.android.data.database.ApiSourceDao_Impl  getColumnIndexOrThrow 3com.libretv.android.data.database.ApiSourceDao_Impl  getRequiredConverters 3com.libretv.android.data.database.ApiSourceDao_Impl  
mutableListOf 3com.libretv.android.data.database.ApiSourceDao_Impl  performSuspending 3com.libretv.android.data.database.ApiSourceDao_Impl  	ApiSource =com.libretv.android.data.database.ApiSourceDao_Impl.Companion  arrayOf =com.libretv.android.data.database.ApiSourceDao_Impl.Companion  
createFlow =com.libretv.android.data.database.ApiSourceDao_Impl.Companion  	emptyList =com.libretv.android.data.database.ApiSourceDao_Impl.Companion  getColumnIndexOrThrow =com.libretv.android.data.database.ApiSourceDao_Impl.Companion  getRequiredConverters =com.libretv.android.data.database.ApiSourceDao_Impl.Companion  
mutableListOf =com.libretv.android.data.database.ApiSourceDao_Impl.Companion  performSuspending =com.libretv.android.data.database.ApiSourceDao_Impl.Companion  Gson ,com.libretv.android.data.database.Converters  	emptyList ,com.libretv.android.data.database.Converters  fromStringList ,com.libretv.android.data.database.Converters  gson ,com.libretv.android.data.database.Converters  toStringList ,com.libretv.android.data.database.Converters  ApiSourceDao 1com.libretv.android.data.database.LibreTVDatabase  ApiSourceDao_Impl 1com.libretv.android.data.database.LibreTVDatabase  AutoMigrationSpec 1com.libretv.android.data.database.LibreTVDatabase  	Companion 1com.libretv.android.data.database.LibreTVDatabase  Context 1com.libretv.android.data.database.LibreTVDatabase  
DATABASE_NAME 1com.libretv.android.data.database.LibreTVDatabase  InvalidationTracker 1com.libretv.android.data.database.LibreTVDatabase  KClass 1com.libretv.android.data.database.LibreTVDatabase  Lazy 1com.libretv.android.data.database.LibreTVDatabase  LibreTVDatabase 1com.libretv.android.data.database.LibreTVDatabase  List 1com.libretv.android.data.database.LibreTVDatabase  Map 1com.libretv.android.data.database.LibreTVDatabase  	Migration 1com.libretv.android.data.database.LibreTVDatabase  MutableList 1com.libretv.android.data.database.LibreTVDatabase  
MutableMap 1com.libretv.android.data.database.LibreTVDatabase  
MutableSet 1com.libretv.android.data.database.LibreTVDatabase  Room 1com.libretv.android.data.database.LibreTVDatabase  RoomOpenDelegate 1com.libretv.android.data.database.LibreTVDatabase  SQLiteConnection 1com.libretv.android.data.database.LibreTVDatabase  SearchHistoryDao 1com.libretv.android.data.database.LibreTVDatabase  SearchHistoryDao_Impl 1com.libretv.android.data.database.LibreTVDatabase  Set 1com.libretv.android.data.database.LibreTVDatabase  String 1com.libretv.android.data.database.LibreTVDatabase  	TableInfo 1com.libretv.android.data.database.LibreTVDatabase  VideoDao 1com.libretv.android.data.database.LibreTVDatabase  
VideoDao_Impl 1com.libretv.android.data.database.LibreTVDatabase  WatchHistoryDao 1com.libretv.android.data.database.LibreTVDatabase  WatchHistoryDao_Impl 1com.libretv.android.data.database.LibreTVDatabase  apiSourceDao 1com.libretv.android.data.database.LibreTVDatabase  create 1com.libretv.android.data.database.LibreTVDatabase  databaseBuilder 1com.libretv.android.data.database.LibreTVDatabase  dropFtsSyncTriggers 1com.libretv.android.data.database.LibreTVDatabase  execSQL 1com.libretv.android.data.database.LibreTVDatabase  getRequiredConverters 1com.libretv.android.data.database.LibreTVDatabase  internalInitInvalidationTracker 1com.libretv.android.data.database.LibreTVDatabase  java 1com.libretv.android.data.database.LibreTVDatabase  lazy 1com.libretv.android.data.database.LibreTVDatabase  
mutableListOf 1com.libretv.android.data.database.LibreTVDatabase  mutableMapOf 1com.libretv.android.data.database.LibreTVDatabase  mutableSetOf 1com.libretv.android.data.database.LibreTVDatabase  performClear 1com.libretv.android.data.database.LibreTVDatabase  read 1com.libretv.android.data.database.LibreTVDatabase  searchHistoryDao 1com.libretv.android.data.database.LibreTVDatabase  
trimMargin 1com.libretv.android.data.database.LibreTVDatabase  videoDao 1com.libretv.android.data.database.LibreTVDatabase  watchHistoryDao 1com.libretv.android.data.database.LibreTVDatabase  ApiSourceDao ;com.libretv.android.data.database.LibreTVDatabase.Companion  ApiSourceDao_Impl ;com.libretv.android.data.database.LibreTVDatabase.Companion  
DATABASE_NAME ;com.libretv.android.data.database.LibreTVDatabase.Companion  InvalidationTracker ;com.libretv.android.data.database.LibreTVDatabase.Companion  LibreTVDatabase ;com.libretv.android.data.database.LibreTVDatabase.Companion  Room ;com.libretv.android.data.database.LibreTVDatabase.Companion  RoomOpenDelegate ;com.libretv.android.data.database.LibreTVDatabase.Companion  SearchHistoryDao ;com.libretv.android.data.database.LibreTVDatabase.Companion  SearchHistoryDao_Impl ;com.libretv.android.data.database.LibreTVDatabase.Companion  	TableInfo ;com.libretv.android.data.database.LibreTVDatabase.Companion  VideoDao ;com.libretv.android.data.database.LibreTVDatabase.Companion  
VideoDao_Impl ;com.libretv.android.data.database.LibreTVDatabase.Companion  WatchHistoryDao ;com.libretv.android.data.database.LibreTVDatabase.Companion  WatchHistoryDao_Impl ;com.libretv.android.data.database.LibreTVDatabase.Companion  create ;com.libretv.android.data.database.LibreTVDatabase.Companion  databaseBuilder ;com.libretv.android.data.database.LibreTVDatabase.Companion  dropFtsSyncTriggers ;com.libretv.android.data.database.LibreTVDatabase.Companion  execSQL ;com.libretv.android.data.database.LibreTVDatabase.Companion  getRequiredConverters ;com.libretv.android.data.database.LibreTVDatabase.Companion  internalInitInvalidationTracker ;com.libretv.android.data.database.LibreTVDatabase.Companion  java ;com.libretv.android.data.database.LibreTVDatabase.Companion  lazy ;com.libretv.android.data.database.LibreTVDatabase.Companion  
mutableListOf ;com.libretv.android.data.database.LibreTVDatabase.Companion  mutableMapOf ;com.libretv.android.data.database.LibreTVDatabase.Companion  mutableSetOf ;com.libretv.android.data.database.LibreTVDatabase.Companion  read ;com.libretv.android.data.database.LibreTVDatabase.Companion  
trimMargin ;com.libretv.android.data.database.LibreTVDatabase.Companion  ValidationResult Bcom.libretv.android.data.database.LibreTVDatabase.RoomOpenDelegate  Column ;com.libretv.android.data.database.LibreTVDatabase.TableInfo  
ForeignKey ;com.libretv.android.data.database.LibreTVDatabase.TableInfo  Index ;com.libretv.android.data.database.LibreTVDatabase.TableInfo  ApiSourceDao 6com.libretv.android.data.database.LibreTVDatabase_Impl  ApiSourceDao_Impl 6com.libretv.android.data.database.LibreTVDatabase_Impl  InvalidationTracker 6com.libretv.android.data.database.LibreTVDatabase_Impl  RoomOpenDelegate 6com.libretv.android.data.database.LibreTVDatabase_Impl  SearchHistoryDao 6com.libretv.android.data.database.LibreTVDatabase_Impl  SearchHistoryDao_Impl 6com.libretv.android.data.database.LibreTVDatabase_Impl  	TableInfo 6com.libretv.android.data.database.LibreTVDatabase_Impl  VideoDao 6com.libretv.android.data.database.LibreTVDatabase_Impl  
VideoDao_Impl 6com.libretv.android.data.database.LibreTVDatabase_Impl  WatchHistoryDao 6com.libretv.android.data.database.LibreTVDatabase_Impl  WatchHistoryDao_Impl 6com.libretv.android.data.database.LibreTVDatabase_Impl  
_apiSourceDao 6com.libretv.android.data.database.LibreTVDatabase_Impl  _searchHistoryDao 6com.libretv.android.data.database.LibreTVDatabase_Impl  	_videoDao 6com.libretv.android.data.database.LibreTVDatabase_Impl  _watchHistoryDao 6com.libretv.android.data.database.LibreTVDatabase_Impl  dropFtsSyncTriggers 6com.libretv.android.data.database.LibreTVDatabase_Impl  execSQL 6com.libretv.android.data.database.LibreTVDatabase_Impl  getRequiredConverters 6com.libretv.android.data.database.LibreTVDatabase_Impl  internalInitInvalidationTracker 6com.libretv.android.data.database.LibreTVDatabase_Impl  lazy 6com.libretv.android.data.database.LibreTVDatabase_Impl  
mutableListOf 6com.libretv.android.data.database.LibreTVDatabase_Impl  mutableMapOf 6com.libretv.android.data.database.LibreTVDatabase_Impl  mutableSetOf 6com.libretv.android.data.database.LibreTVDatabase_Impl  read 6com.libretv.android.data.database.LibreTVDatabase_Impl  
trimMargin 6com.libretv.android.data.database.LibreTVDatabase_Impl  ValidationResult 2com.libretv.android.data.database.RoomOpenDelegate  OnConflictStrategy 2com.libretv.android.data.database.SearchHistoryDao  clearAllHistory 2com.libretv.android.data.database.SearchHistoryDao  deleteOldHistory 2com.libretv.android.data.database.SearchHistoryDao  deleteOldestEntries 2com.libretv.android.data.database.SearchHistoryDao  deleteSearch 2com.libretv.android.data.database.SearchHistoryDao  getHistoryCount 2com.libretv.android.data.database.SearchHistoryDao  getRecentSearches 2com.libretv.android.data.database.SearchHistoryDao  insertSearch 2com.libretv.android.data.database.SearchHistoryDao  
searchHistory 2com.libretv.android.data.database.SearchHistoryDao  	Companion 7com.libretv.android.data.database.SearchHistoryDao_Impl  EntityDeleteOrUpdateAdapter 7com.libretv.android.data.database.SearchHistoryDao_Impl  EntityInsertAdapter 7com.libretv.android.data.database.SearchHistoryDao_Impl  Flow 7com.libretv.android.data.database.SearchHistoryDao_Impl  Int 7com.libretv.android.data.database.SearchHistoryDao_Impl  KClass 7com.libretv.android.data.database.SearchHistoryDao_Impl  List 7com.libretv.android.data.database.SearchHistoryDao_Impl  Long 7com.libretv.android.data.database.SearchHistoryDao_Impl  MutableList 7com.libretv.android.data.database.SearchHistoryDao_Impl  RoomDatabase 7com.libretv.android.data.database.SearchHistoryDao_Impl  SQLiteStatement 7com.libretv.android.data.database.SearchHistoryDao_Impl  
SearchHistory 7com.libretv.android.data.database.SearchHistoryDao_Impl  String 7com.libretv.android.data.database.SearchHistoryDao_Impl  Unit 7com.libretv.android.data.database.SearchHistoryDao_Impl  __db 7com.libretv.android.data.database.SearchHistoryDao_Impl  __deleteAdapterOfSearchHistory 7com.libretv.android.data.database.SearchHistoryDao_Impl  __insertAdapterOfSearchHistory 7com.libretv.android.data.database.SearchHistoryDao_Impl  arrayOf 7com.libretv.android.data.database.SearchHistoryDao_Impl  
createFlow 7com.libretv.android.data.database.SearchHistoryDao_Impl  	emptyList 7com.libretv.android.data.database.SearchHistoryDao_Impl  getColumnIndexOrThrow 7com.libretv.android.data.database.SearchHistoryDao_Impl  getRequiredConverters 7com.libretv.android.data.database.SearchHistoryDao_Impl  
mutableListOf 7com.libretv.android.data.database.SearchHistoryDao_Impl  performSuspending 7com.libretv.android.data.database.SearchHistoryDao_Impl  
SearchHistory Acom.libretv.android.data.database.SearchHistoryDao_Impl.Companion  arrayOf Acom.libretv.android.data.database.SearchHistoryDao_Impl.Companion  
createFlow Acom.libretv.android.data.database.SearchHistoryDao_Impl.Companion  	emptyList Acom.libretv.android.data.database.SearchHistoryDao_Impl.Companion  getColumnIndexOrThrow Acom.libretv.android.data.database.SearchHistoryDao_Impl.Companion  getRequiredConverters Acom.libretv.android.data.database.SearchHistoryDao_Impl.Companion  
mutableListOf Acom.libretv.android.data.database.SearchHistoryDao_Impl.Companion  performSuspending Acom.libretv.android.data.database.SearchHistoryDao_Impl.Companion  Column +com.libretv.android.data.database.TableInfo  
ForeignKey +com.libretv.android.data.database.TableInfo  Index +com.libretv.android.data.database.TableInfo  OnConflictStrategy *com.libretv.android.data.database.VideoDao  deleteOldVideos *com.libretv.android.data.database.VideoDao  getFavoriteVideos *com.libretv.android.data.database.VideoDao  insertVideos *com.libretv.android.data.database.VideoDao  searchVideos *com.libretv.android.data.database.VideoDao  updateFavoriteStatus *com.libretv.android.data.database.VideoDao  updateWatchProgress *com.libretv.android.data.database.VideoDao  Boolean /com.libretv.android.data.database.VideoDao_Impl  	Companion /com.libretv.android.data.database.VideoDao_Impl  EntityDeleteOrUpdateAdapter /com.libretv.android.data.database.VideoDao_Impl  EntityInsertAdapter /com.libretv.android.data.database.VideoDao_Impl  Flow /com.libretv.android.data.database.VideoDao_Impl  Int /com.libretv.android.data.database.VideoDao_Impl  KClass /com.libretv.android.data.database.VideoDao_Impl  List /com.libretv.android.data.database.VideoDao_Impl  Long /com.libretv.android.data.database.VideoDao_Impl  MutableList /com.libretv.android.data.database.VideoDao_Impl  RoomDatabase /com.libretv.android.data.database.VideoDao_Impl  SQLiteStatement /com.libretv.android.data.database.VideoDao_Impl  String /com.libretv.android.data.database.VideoDao_Impl  Unit /com.libretv.android.data.database.VideoDao_Impl  	VideoInfo /com.libretv.android.data.database.VideoDao_Impl  __db /com.libretv.android.data.database.VideoDao_Impl  __deleteAdapterOfVideoInfo /com.libretv.android.data.database.VideoDao_Impl  __insertAdapterOfVideoInfo /com.libretv.android.data.database.VideoDao_Impl  __updateAdapterOfVideoInfo /com.libretv.android.data.database.VideoDao_Impl  arrayOf /com.libretv.android.data.database.VideoDao_Impl  
createFlow /com.libretv.android.data.database.VideoDao_Impl  	emptyList /com.libretv.android.data.database.VideoDao_Impl  getColumnIndexOrThrow /com.libretv.android.data.database.VideoDao_Impl  getRequiredConverters /com.libretv.android.data.database.VideoDao_Impl  
mutableListOf /com.libretv.android.data.database.VideoDao_Impl  performSuspending /com.libretv.android.data.database.VideoDao_Impl  	VideoInfo 9com.libretv.android.data.database.VideoDao_Impl.Companion  arrayOf 9com.libretv.android.data.database.VideoDao_Impl.Companion  
createFlow 9com.libretv.android.data.database.VideoDao_Impl.Companion  	emptyList 9com.libretv.android.data.database.VideoDao_Impl.Companion  getColumnIndexOrThrow 9com.libretv.android.data.database.VideoDao_Impl.Companion  getRequiredConverters 9com.libretv.android.data.database.VideoDao_Impl.Companion  
mutableListOf 9com.libretv.android.data.database.VideoDao_Impl.Companion  performSuspending 9com.libretv.android.data.database.VideoDao_Impl.Companion  OnConflictStrategy 1com.libretv.android.data.database.WatchHistoryDao  clearAllHistory 1com.libretv.android.data.database.WatchHistoryDao  deleteOldHistory 1com.libretv.android.data.database.WatchHistoryDao  deleteWatchHistory 1com.libretv.android.data.database.WatchHistoryDao  getAllWatchHistory 1com.libretv.android.data.database.WatchHistoryDao  getRecentWatchHistory 1com.libretv.android.data.database.WatchHistoryDao  getWatchHistory 1com.libretv.android.data.database.WatchHistoryDao  getWatchHistoryById 1com.libretv.android.data.database.WatchHistoryDao  insertWatchHistory 1com.libretv.android.data.database.WatchHistoryDao  
updateEpisode 1com.libretv.android.data.database.WatchHistoryDao  updateProgress 1com.libretv.android.data.database.WatchHistoryDao  updateWatchHistory 1com.libretv.android.data.database.WatchHistoryDao  	Companion 6com.libretv.android.data.database.WatchHistoryDao_Impl  
Converters 6com.libretv.android.data.database.WatchHistoryDao_Impl  EntityDeleteOrUpdateAdapter 6com.libretv.android.data.database.WatchHistoryDao_Impl  EntityInsertAdapter 6com.libretv.android.data.database.WatchHistoryDao_Impl  Flow 6com.libretv.android.data.database.WatchHistoryDao_Impl  Int 6com.libretv.android.data.database.WatchHistoryDao_Impl  KClass 6com.libretv.android.data.database.WatchHistoryDao_Impl  List 6com.libretv.android.data.database.WatchHistoryDao_Impl  Long 6com.libretv.android.data.database.WatchHistoryDao_Impl  MutableList 6com.libretv.android.data.database.WatchHistoryDao_Impl  RoomDatabase 6com.libretv.android.data.database.WatchHistoryDao_Impl  SQLiteStatement 6com.libretv.android.data.database.WatchHistoryDao_Impl  String 6com.libretv.android.data.database.WatchHistoryDao_Impl  Unit 6com.libretv.android.data.database.WatchHistoryDao_Impl  WatchHistory 6com.libretv.android.data.database.WatchHistoryDao_Impl  __converters 6com.libretv.android.data.database.WatchHistoryDao_Impl  __db 6com.libretv.android.data.database.WatchHistoryDao_Impl  __deleteAdapterOfWatchHistory 6com.libretv.android.data.database.WatchHistoryDao_Impl  __insertAdapterOfWatchHistory 6com.libretv.android.data.database.WatchHistoryDao_Impl  __updateAdapterOfWatchHistory 6com.libretv.android.data.database.WatchHistoryDao_Impl  arrayOf 6com.libretv.android.data.database.WatchHistoryDao_Impl  
createFlow 6com.libretv.android.data.database.WatchHistoryDao_Impl  	emptyList 6com.libretv.android.data.database.WatchHistoryDao_Impl  getColumnIndexOrThrow 6com.libretv.android.data.database.WatchHistoryDao_Impl  getRequiredConverters 6com.libretv.android.data.database.WatchHistoryDao_Impl  
mutableListOf 6com.libretv.android.data.database.WatchHistoryDao_Impl  performSuspending 6com.libretv.android.data.database.WatchHistoryDao_Impl  
Converters @com.libretv.android.data.database.WatchHistoryDao_Impl.Companion  WatchHistory @com.libretv.android.data.database.WatchHistoryDao_Impl.Companion  __converters @com.libretv.android.data.database.WatchHistoryDao_Impl.Companion  arrayOf @com.libretv.android.data.database.WatchHistoryDao_Impl.Companion  
createFlow @com.libretv.android.data.database.WatchHistoryDao_Impl.Companion  	emptyList @com.libretv.android.data.database.WatchHistoryDao_Impl.Companion  getColumnIndexOrThrow @com.libretv.android.data.database.WatchHistoryDao_Impl.Companion  getRequiredConverters @com.libretv.android.data.database.WatchHistoryDao_Impl.Companion  
mutableListOf @com.libretv.android.data.database.WatchHistoryDao_Impl.Companion  performSuspending @com.libretv.android.data.database.WatchHistoryDao_Impl.Companion  ApiResponse com.libretv.android.data.model  	ApiSource com.libretv.android.data.model  Boolean com.libretv.android.data.model  Entity com.libretv.android.data.model  Int com.libretv.android.data.model  List com.libretv.android.data.model  Long com.libretv.android.data.model  
PrimaryKey com.libretv.android.data.model  
SearchHistory com.libretv.android.data.model  
SerialName com.libretv.android.data.model  Serializable com.libretv.android.data.model  String com.libretv.android.data.model  System com.libretv.android.data.model  T com.libretv.android.data.model  VideoDetailInfo com.libretv.android.data.model  	VideoInfo com.libretv.android.data.model  WatchHistory com.libretv.android.data.model  	emptyList com.libretv.android.data.model  Int *com.libretv.android.data.model.ApiResponse  List *com.libretv.android.data.model.ApiResponse  
SerialName *com.libretv.android.data.model.ApiResponse  String *com.libretv.android.data.model.ApiResponse  T *com.libretv.android.data.model.ApiResponse  VideoDetailInfo *com.libretv.android.data.model.ApiResponse  	emptyList *com.libretv.android.data.model.ApiResponse  episodes *com.libretv.android.data.model.ApiResponse  list *com.libretv.android.data.model.ApiResponse  	videoInfo *com.libretv.android.data.model.ApiResponse  	emptyList 4com.libretv.android.data.model.ApiResponse.Companion  api (com.libretv.android.data.model.ApiSource  code (com.libretv.android.data.model.ApiSource  detail (com.libretv.android.data.model.ApiSource  isAdult (com.libretv.android.data.model.ApiSource  isCustom (com.libretv.android.data.model.ApiSource  	isEnabled (com.libretv.android.data.model.ApiSource  name (com.libretv.android.data.model.ApiSource  order (com.libretv.android.data.model.ApiSource  query ,com.libretv.android.data.model.SearchHistory  	timestamp ,com.libretv.android.data.model.SearchHistory  
SerialName .com.libretv.android.data.model.VideoDetailInfo  String .com.libretv.android.data.model.VideoDetailInfo  Boolean (com.libretv.android.data.model.VideoInfo  	Companion (com.libretv.android.data.model.VideoInfo  Long (com.libretv.android.data.model.VideoInfo  
PrimaryKey (com.libretv.android.data.model.VideoInfo  
SerialName (com.libretv.android.data.model.VideoInfo  String (com.libretv.android.data.model.VideoInfo  apiUrl (com.libretv.android.data.model.VideoInfo  copy (com.libretv.android.data.model.VideoInfo  
isFavorite (com.libretv.android.data.model.VideoInfo  
lastWatchTime (com.libretv.android.data.model.VideoInfo  
sourceCode (com.libretv.android.data.model.VideoInfo  
sourceName (com.libretv.android.data.model.VideoInfo  typeName (com.libretv.android.data.model.VideoInfo  vodActor (com.libretv.android.data.model.VideoInfo  vodArea (com.libretv.android.data.model.VideoInfo  
vodContent (com.libretv.android.data.model.VideoInfo  vodDirector (com.libretv.android.data.model.VideoInfo  vodId (com.libretv.android.data.model.VideoInfo  vodName (com.libretv.android.data.model.VideoInfo  vodPic (com.libretv.android.data.model.VideoInfo  
vodPlayUrl (com.libretv.android.data.model.VideoInfo  
vodRemarks (com.libretv.android.data.model.VideoInfo  vodYear (com.libretv.android.data.model.VideoInfo  
watchProgress (com.libretv.android.data.model.VideoInfo  copy +com.libretv.android.data.model.WatchHistory  episodeIndex +com.libretv.android.data.model.WatchHistory  episodeTitle +com.libretv.android.data.model.WatchHistory  episodes +com.libretv.android.data.model.WatchHistory  id +com.libretv.android.data.model.WatchHistory  
lastWatchTime +com.libretv.android.data.model.WatchHistory  
sourceCode +com.libretv.android.data.model.WatchHistory  
sourceName +com.libretv.android.data.model.WatchHistory  
totalDuration +com.libretv.android.data.model.WatchHistory  
videoCover +com.libretv.android.data.model.WatchHistory  videoId +com.libretv.android.data.model.WatchHistory  
videoTitle +com.libretv.android.data.model.WatchHistory  
watchProgress +com.libretv.android.data.model.WatchHistory  AnnotationRetention  com.libretv.android.data.network  ApiResponse  com.libretv.android.data.network  ApiRetrofit  com.libretv.android.data.network  
ApiService  com.libretv.android.data.network  ApiSourceConfig  com.libretv.android.data.network  Boolean  com.libretv.android.data.network  DoubanApiService  com.libretv.android.data.network  DoubanResponse  com.libretv.android.data.network  DoubanRetrofit  com.libretv.android.data.network  
DoubanSubject  com.libretv.android.data.network  ErrorHandlingInterceptor  com.libretv.android.data.network  GET  com.libretv.android.data.network  Gson  com.libretv.android.data.network  GsonBuilder  com.libretv.android.data.network  GsonConverterFactory  com.libretv.android.data.network  HttpLoggingInterceptor  com.libretv.android.data.network  IOException  com.libretv.android.data.network  	InstallIn  com.libretv.android.data.network  Int  com.libretv.android.data.network  Interceptor  com.libretv.android.data.network  InternalApiService  com.libretv.android.data.network  InternalRetrofit  com.libretv.android.data.network  List  com.libretv.android.data.network  Module  com.libretv.android.data.network  
NetworkConfig  com.libretv.android.data.network  OkHttpClient  com.libretv.android.data.network  Provides  com.libretv.android.data.network  	Qualifier  com.libretv.android.data.network  Query  com.libretv.android.data.network  Response  com.libretv.android.data.network  	Retention  com.libretv.android.data.network  Retrofit  com.libretv.android.data.network  	Singleton  com.libretv.android.data.network  SingletonComponent  com.libretv.android.data.network  String  com.libretv.android.data.network  Throws  com.libretv.android.data.network  TimeUnit  com.libretv.android.data.network  Url  com.libretv.android.data.network  UserAgentInterceptor  com.libretv.android.data.network  VideoDetailInfo  com.libretv.android.data.network  	VideoInfo  com.libretv.android.data.network  android  com.libretv.android.data.network  apply  com.libretv.android.data.network  	emptyList  com.libretv.android.data.network  java  com.libretv.android.data.network  mapOf  com.libretv.android.data.network  	retrofit2  com.libretv.android.data.network  to  com.libretv.android.data.network  searchVideos +com.libretv.android.data.network.ApiService  adult 0com.libretv.android.data.network.ApiSourceConfig  api 0com.libretv.android.data.network.ApiSourceConfig  detail 0com.libretv.android.data.network.ApiSourceConfig  name 0com.libretv.android.data.network.ApiSourceConfig  getHotMovies 1com.libretv.android.data.network.DoubanApiService  subjects /com.libretv.android.data.network.DoubanResponse  cover .com.libretv.android.data.network.DoubanSubject  rate .com.libretv.android.data.network.DoubanSubject  title .com.libretv.android.data.network.DoubanSubject  IOException 9com.libretv.android.data.network.ErrorHandlingInterceptor  android 9com.libretv.android.data.network.ErrorHandlingInterceptor  Chain ,com.libretv.android.data.network.Interceptor  aggregatedSearch 3com.libretv.android.data.network.InternalApiService  	getDetail 3com.libretv.android.data.network.InternalApiService  ApiSourceConfig .com.libretv.android.data.network.NetworkConfig  CONNECT_TIMEOUT .com.libretv.android.data.network.NetworkConfig  DEFAULT_API_SOURCES .com.libretv.android.data.network.NetworkConfig  READ_TIMEOUT .com.libretv.android.data.network.NetworkConfig  
USER_AGENT .com.libretv.android.data.network.NetworkConfig  
WRITE_TIMEOUT .com.libretv.android.data.network.NetworkConfig  mapOf .com.libretv.android.data.network.NetworkConfig  to .com.libretv.android.data.network.NetworkConfig  IOException 5com.libretv.android.data.network.UserAgentInterceptor  
NetworkConfig 5com.libretv.android.data.network.UserAgentInterceptor  http *com.libretv.android.data.network.retrofit2  Path /com.libretv.android.data.network.retrofit2.http  ADULT_CONTENT_ENABLED $com.libretv.android.data.preferences  AUTO_CLEAN_CACHE $com.libretv.android.data.preferences  AUTO_PLAY_NEXT $com.libretv.android.data.preferences  ApplicationContext $com.libretv.android.data.preferences  Boolean $com.libretv.android.data.preferences  
CACHE_ENABLED $com.libretv.android.data.preferences  
CACHE_SIZE_MB $com.libretv.android.data.preferences  Context $com.libretv.android.data.preferences  	DataStore $com.libretv.android.data.preferences  FIRST_LAUNCH $com.libretv.android.data.preferences  Float $com.libretv.android.data.preferences  Flow $com.libretv.android.data.preferences  GRID_COLUMNS $com.libretv.android.data.preferences  Inject $com.libretv.android.data.preferences  Int $com.libretv.android.data.preferences  LAST_VERSION_CODE $com.libretv.android.data.preferences  PASSWORD_ENABLED $com.libretv.android.data.preferences  
PASSWORD_HASH $com.libretv.android.data.preferences  PLAYER_AUTO_FULLSCREEN $com.libretv.android.data.preferences  PLAYER_BRIGHTNESS $com.libretv.android.data.preferences  PLAYER_SPEED $com.libretv.android.data.preferences  
PLAYER_VOLUME $com.libretv.android.data.preferences  	PROXY_URL $com.libretv.android.data.preferences  Preferences $com.libretv.android.data.preferences  REMEMBER_PROGRESS $com.libretv.android.data.preferences  SHOW_ADULT_WARNING $com.libretv.android.data.preferences  SettingsManager $com.libretv.android.data.preferences  	Singleton $com.libretv.android.data.preferences  String $com.libretv.android.data.preferences  
THEME_MODE $com.libretv.android.data.preferences  TIMEOUT_SECONDS $com.libretv.android.data.preferences  	USE_PROXY $com.libretv.android.data.preferences  booleanPreferencesKey $com.libretv.android.data.preferences  edit $com.libretv.android.data.preferences  floatPreferencesKey $com.libretv.android.data.preferences  intPreferencesKey $com.libretv.android.data.preferences  map $com.libretv.android.data.preferences  preferencesDataStore $com.libretv.android.data.preferences  provideDelegate $com.libretv.android.data.preferences  stringPreferencesKey $com.libretv.android.data.preferences  ADULT_CONTENT_ENABLED 4com.libretv.android.data.preferences.SettingsManager  AUTO_CLEAN_CACHE 4com.libretv.android.data.preferences.SettingsManager  AUTO_PLAY_NEXT 4com.libretv.android.data.preferences.SettingsManager  ApplicationContext 4com.libretv.android.data.preferences.SettingsManager  Boolean 4com.libretv.android.data.preferences.SettingsManager  
CACHE_ENABLED 4com.libretv.android.data.preferences.SettingsManager  
CACHE_SIZE_MB 4com.libretv.android.data.preferences.SettingsManager  Context 4com.libretv.android.data.preferences.SettingsManager  	DataStore 4com.libretv.android.data.preferences.SettingsManager  FIRST_LAUNCH 4com.libretv.android.data.preferences.SettingsManager  Float 4com.libretv.android.data.preferences.SettingsManager  Flow 4com.libretv.android.data.preferences.SettingsManager  GRID_COLUMNS 4com.libretv.android.data.preferences.SettingsManager  Inject 4com.libretv.android.data.preferences.SettingsManager  Int 4com.libretv.android.data.preferences.SettingsManager  LAST_VERSION_CODE 4com.libretv.android.data.preferences.SettingsManager  PASSWORD_ENABLED 4com.libretv.android.data.preferences.SettingsManager  
PASSWORD_HASH 4com.libretv.android.data.preferences.SettingsManager  PLAYER_AUTO_FULLSCREEN 4com.libretv.android.data.preferences.SettingsManager  PLAYER_BRIGHTNESS 4com.libretv.android.data.preferences.SettingsManager  PLAYER_SPEED 4com.libretv.android.data.preferences.SettingsManager  
PLAYER_VOLUME 4com.libretv.android.data.preferences.SettingsManager  	PROXY_URL 4com.libretv.android.data.preferences.SettingsManager  Preferences 4com.libretv.android.data.preferences.SettingsManager  REMEMBER_PROGRESS 4com.libretv.android.data.preferences.SettingsManager  SHOW_ADULT_WARNING 4com.libretv.android.data.preferences.SettingsManager  String 4com.libretv.android.data.preferences.SettingsManager  
THEME_MODE 4com.libretv.android.data.preferences.SettingsManager  TIMEOUT_SECONDS 4com.libretv.android.data.preferences.SettingsManager  	USE_PROXY 4com.libretv.android.data.preferences.SettingsManager  booleanPreferencesKey 4com.libretv.android.data.preferences.SettingsManager  cacheSizeMB 4com.libretv.android.data.preferences.SettingsManager  clearAllSettings 4com.libretv.android.data.preferences.SettingsManager  context 4com.libretv.android.data.preferences.SettingsManager  	dataStore 4com.libretv.android.data.preferences.SettingsManager  edit 4com.libretv.android.data.preferences.SettingsManager  floatPreferencesKey 4com.libretv.android.data.preferences.SettingsManager  gridColumns 4com.libretv.android.data.preferences.SettingsManager  intPreferencesKey 4com.libretv.android.data.preferences.SettingsManager  isAdultContentEnabled 4com.libretv.android.data.preferences.SettingsManager  isAutoPlayNext 4com.libretv.android.data.preferences.SettingsManager  isCacheEnabled 4com.libretv.android.data.preferences.SettingsManager  isPlayerAutoFullscreen 4com.libretv.android.data.preferences.SettingsManager  isRememberProgress 4com.libretv.android.data.preferences.SettingsManager  map 4com.libretv.android.data.preferences.SettingsManager  preferencesDataStore 4com.libretv.android.data.preferences.SettingsManager  provideDelegate 4com.libretv.android.data.preferences.SettingsManager  setAdultContentEnabled 4com.libretv.android.data.preferences.SettingsManager  setAutoPlayNext 4com.libretv.android.data.preferences.SettingsManager  setCacheEnabled 4com.libretv.android.data.preferences.SettingsManager  setCacheSizeMB 4com.libretv.android.data.preferences.SettingsManager  setGridColumns 4com.libretv.android.data.preferences.SettingsManager  setPlayerAutoFullscreen 4com.libretv.android.data.preferences.SettingsManager  setRememberProgress 4com.libretv.android.data.preferences.SettingsManager  setThemeMode 4com.libretv.android.data.preferences.SettingsManager  stringPreferencesKey 4com.libretv.android.data.preferences.SettingsManager  	themeMode 4com.libretv.android.data.preferences.SettingsManager  ADULT_CONTENT_ENABLED >com.libretv.android.data.preferences.SettingsManager.Companion  AUTO_CLEAN_CACHE >com.libretv.android.data.preferences.SettingsManager.Companion  AUTO_PLAY_NEXT >com.libretv.android.data.preferences.SettingsManager.Companion  
CACHE_ENABLED >com.libretv.android.data.preferences.SettingsManager.Companion  
CACHE_SIZE_MB >com.libretv.android.data.preferences.SettingsManager.Companion  FIRST_LAUNCH >com.libretv.android.data.preferences.SettingsManager.Companion  GRID_COLUMNS >com.libretv.android.data.preferences.SettingsManager.Companion  LAST_VERSION_CODE >com.libretv.android.data.preferences.SettingsManager.Companion  PASSWORD_ENABLED >com.libretv.android.data.preferences.SettingsManager.Companion  
PASSWORD_HASH >com.libretv.android.data.preferences.SettingsManager.Companion  PLAYER_AUTO_FULLSCREEN >com.libretv.android.data.preferences.SettingsManager.Companion  PLAYER_BRIGHTNESS >com.libretv.android.data.preferences.SettingsManager.Companion  PLAYER_SPEED >com.libretv.android.data.preferences.SettingsManager.Companion  
PLAYER_VOLUME >com.libretv.android.data.preferences.SettingsManager.Companion  	PROXY_URL >com.libretv.android.data.preferences.SettingsManager.Companion  REMEMBER_PROGRESS >com.libretv.android.data.preferences.SettingsManager.Companion  SHOW_ADULT_WARNING >com.libretv.android.data.preferences.SettingsManager.Companion  
THEME_MODE >com.libretv.android.data.preferences.SettingsManager.Companion  TIMEOUT_SECONDS >com.libretv.android.data.preferences.SettingsManager.Companion  	USE_PROXY >com.libretv.android.data.preferences.SettingsManager.Companion  booleanPreferencesKey >com.libretv.android.data.preferences.SettingsManager.Companion  edit >com.libretv.android.data.preferences.SettingsManager.Companion  floatPreferencesKey >com.libretv.android.data.preferences.SettingsManager.Companion  intPreferencesKey >com.libretv.android.data.preferences.SettingsManager.Companion  map >com.libretv.android.data.preferences.SettingsManager.Companion  preferencesDataStore >com.libretv.android.data.preferences.SettingsManager.Companion  provideDelegate >com.libretv.android.data.preferences.SettingsManager.Companion  stringPreferencesKey >com.libretv.android.data.preferences.SettingsManager.Companion  
ApiService #com.libretv.android.data.repository  	ApiSource #com.libretv.android.data.repository  ApiSourceDao #com.libretv.android.data.repository  ApiSourceRepository #com.libretv.android.data.repository  Boolean #com.libretv.android.data.repository  	Exception #com.libretv.android.data.repository  Flow #com.libretv.android.data.repository  Inject #com.libretv.android.data.repository  Int #com.libretv.android.data.repository  InternalApiService #com.libretv.android.data.repository  List #com.libretv.android.data.repository  Long #com.libretv.android.data.repository  
NetworkConfig #com.libretv.android.data.repository  Result #com.libretv.android.data.repository  
SearchHistory #com.libretv.android.data.repository  SearchHistoryDao #com.libretv.android.data.repository  SearchRepository #com.libretv.android.data.repository  	Singleton #com.libretv.android.data.repository  String #com.libretv.android.data.repository  System #com.libretv.android.data.repository  Unit #com.libretv.android.data.repository  VideoDao #com.libretv.android.data.repository  VideoDetailInfo #com.libretv.android.data.repository  	VideoInfo #com.libretv.android.data.repository  VideoRepository #com.libretv.android.data.repository  WatchHistory #com.libretv.android.data.repository  WatchHistoryDao #com.libretv.android.data.repository  WatchHistoryRepository #com.libretv.android.data.repository  android #com.libretv.android.data.repository  
component1 #com.libretv.android.data.repository  
component2 #com.libretv.android.data.repository  	emptyList #com.libretv.android.data.repository  failure #com.libretv.android.data.repository  first #com.libretv.android.data.repository  ifEmpty #com.libretv.android.data.repository  
isNotBlank #com.libretv.android.data.repository  
isNotEmpty #com.libretv.android.data.repository  map #com.libretv.android.data.repository  
mutableListOf #com.libretv.android.data.repository  
startsWith #com.libretv.android.data.repository  success #com.libretv.android.data.repository  trim #com.libretv.android.data.repository  	ApiSource 7com.libretv.android.data.repository.ApiSourceRepository  	Exception 7com.libretv.android.data.repository.ApiSourceRepository  
NetworkConfig 7com.libretv.android.data.repository.ApiSourceRepository  Result 7com.libretv.android.data.repository.ApiSourceRepository  System 7com.libretv.android.data.repository.ApiSourceRepository  Unit 7com.libretv.android.data.repository.ApiSourceRepository  apiSourceDao 7com.libretv.android.data.repository.ApiSourceRepository  
component1 7com.libretv.android.data.repository.ApiSourceRepository  
component2 7com.libretv.android.data.repository.ApiSourceRepository  deleteAllCustomSources 7com.libretv.android.data.repository.ApiSourceRepository  failure 7com.libretv.android.data.repository.ApiSourceRepository  first 7com.libretv.android.data.repository.ApiSourceRepository  initializeDefaultSources 7com.libretv.android.data.repository.ApiSourceRepository  map 7com.libretv.android.data.repository.ApiSourceRepository  
startsWith 7com.libretv.android.data.repository.ApiSourceRepository  success 7com.libretv.android.data.repository.ApiSourceRepository  
SearchHistory 4com.libretv.android.data.repository.SearchRepository  System 4com.libretv.android.data.repository.SearchRepository  addSearchHistory 4com.libretv.android.data.repository.SearchRepository  clearAllHistory 4com.libretv.android.data.repository.SearchRepository  getRecentSearches 4com.libretv.android.data.repository.SearchRepository  
isNotBlank 4com.libretv.android.data.repository.SearchRepository  searchHistoryDao 4com.libretv.android.data.repository.SearchRepository  trim 4com.libretv.android.data.repository.SearchRepository  	Exception 3com.libretv.android.data.repository.VideoRepository  Result 3com.libretv.android.data.repository.VideoRepository  System 3com.libretv.android.data.repository.VideoRepository  android 3com.libretv.android.data.repository.VideoRepository  
apiService 3com.libretv.android.data.repository.VideoRepository  apiSourceDao 3com.libretv.android.data.repository.VideoRepository  
cleanOldCache 3com.libretv.android.data.repository.VideoRepository  	emptyList 3com.libretv.android.data.repository.VideoRepository  failure 3com.libretv.android.data.repository.VideoRepository  first 3com.libretv.android.data.repository.VideoRepository  internalApiService 3com.libretv.android.data.repository.VideoRepository  
isNotEmpty 3com.libretv.android.data.repository.VideoRepository  map 3com.libretv.android.data.repository.VideoRepository  
mutableListOf 3com.libretv.android.data.repository.VideoRepository  searchFromEnabledSources 3com.libretv.android.data.repository.VideoRepository  searchVideos 3com.libretv.android.data.repository.VideoRepository  success 3com.libretv.android.data.repository.VideoRepository  videoDao 3com.libretv.android.data.repository.VideoRepository  System :com.libretv.android.data.repository.WatchHistoryRepository  WatchHistory :com.libretv.android.data.repository.WatchHistoryRepository  clearAllHistory :com.libretv.android.data.repository.WatchHistoryRepository  deleteWatchHistory :com.libretv.android.data.repository.WatchHistoryRepository  	emptyList :com.libretv.android.data.repository.WatchHistoryRepository  getAllWatchHistory :com.libretv.android.data.repository.WatchHistoryRepository  getRecentWatchHistory :com.libretv.android.data.repository.WatchHistoryRepository  ifEmpty :com.libretv.android.data.repository.WatchHistoryRepository  watchHistoryDao :com.libretv.android.data.repository.WatchHistoryRepository  AnnotationRetention com.libretv.android.di  ApiRetrofit com.libretv.android.di  
ApiService com.libretv.android.di  ApiSourceDao com.libretv.android.di  ApplicationContext com.libretv.android.di  Context com.libretv.android.di  DatabaseModule com.libretv.android.di  DoubanApiService com.libretv.android.di  DoubanRetrofit com.libretv.android.di  ErrorHandlingInterceptor com.libretv.android.di  Gson com.libretv.android.di  GsonBuilder com.libretv.android.di  GsonConverterFactory com.libretv.android.di  HttpLoggingInterceptor com.libretv.android.di  	InstallIn com.libretv.android.di  InternalApiService com.libretv.android.di  InternalRetrofit com.libretv.android.di  LibreTVDatabase com.libretv.android.di  Module com.libretv.android.di  
NetworkConfig com.libretv.android.di  
NetworkModule com.libretv.android.di  OkHttpClient com.libretv.android.di  Provides com.libretv.android.di  	Qualifier com.libretv.android.di  	Retention com.libretv.android.di  Retrofit com.libretv.android.di  SearchHistoryDao com.libretv.android.di  	Singleton com.libretv.android.di  SingletonComponent com.libretv.android.di  TimeUnit com.libretv.android.di  UserAgentInterceptor com.libretv.android.di  VideoDao com.libretv.android.di  WatchHistoryDao com.libretv.android.di  apply com.libretv.android.di  create com.libretv.android.di  java com.libretv.android.di  LibreTVDatabase %com.libretv.android.di.DatabaseModule  create %com.libretv.android.di.DatabaseModule  
ApiService $com.libretv.android.di.NetworkModule  DoubanApiService $com.libretv.android.di.NetworkModule  ErrorHandlingInterceptor $com.libretv.android.di.NetworkModule  GsonBuilder $com.libretv.android.di.NetworkModule  GsonConverterFactory $com.libretv.android.di.NetworkModule  HttpLoggingInterceptor $com.libretv.android.di.NetworkModule  InternalApiService $com.libretv.android.di.NetworkModule  
NetworkConfig $com.libretv.android.di.NetworkModule  OkHttpClient $com.libretv.android.di.NetworkModule  Retrofit $com.libretv.android.di.NetworkModule  TimeUnit $com.libretv.android.di.NetworkModule  UserAgentInterceptor $com.libretv.android.di.NetworkModule  apply $com.libretv.android.di.NetworkModule  java $com.libretv.android.di.NetworkModule  AndroidEntryPoint  com.libretv.android.presentation  Bundle  com.libretv.android.presentation  ComponentActivity  com.libretv.android.presentation  
Composable  com.libretv.android.presentation  
LibreTVApp  com.libretv.android.presentation  MainActivity  com.libretv.android.presentation  
MaterialTheme  com.libretv.android.presentation  Modifier  com.libretv.android.presentation  fillMaxSize  com.libretv.android.presentation  
LibreTVApp -com.libretv.android.presentation.MainActivity  enableEdgeToEdge -com.libretv.android.presentation.MainActivity  installSplashScreen -com.libretv.android.presentation.MainActivity  
setContent -com.libretv.android.presentation.MainActivity  	Alignment +com.libretv.android.presentation.components  
AsyncImage +com.libretv.android.presentation.components  Boolean +com.libretv.android.presentation.components  Box +com.libretv.android.presentation.components  Card +com.libretv.android.presentation.components  CardDefaults +com.libretv.android.presentation.components  Column +com.libretv.android.presentation.components  ColumnScope +com.libretv.android.presentation.components  
Composable +com.libretv.android.presentation.components  ContentScale +com.libretv.android.presentation.components  DoubanMovieCard +com.libretv.android.presentation.components  
DoubanSubject +com.libretv.android.presentation.components  ExperimentalMaterial3Api +com.libretv.android.presentation.components  Icon +com.libretv.android.presentation.components  
IconButton +com.libretv.android.presentation.components  Icons +com.libretv.android.presentation.components  	ImeAction +com.libretv.android.presentation.components  LinearProgressIndicator +com.libretv.android.presentation.components  
MaterialTheme +com.libretv.android.presentation.components  Modifier +com.libretv.android.presentation.components  OptIn +com.libretv.android.presentation.components  OutlinedTextField +com.libretv.android.presentation.components  RoundedCornerShape +com.libretv.android.presentation.components  Row +com.libretv.android.presentation.components  	SearchBar +com.libretv.android.presentation.components  SimpleSearchField +com.libretv.android.presentation.components  String +com.libretv.android.presentation.components  Text +com.libretv.android.presentation.components  TextOverflow +com.libretv.android.presentation.components  Unit +com.libretv.android.presentation.components  	VideoCard +com.libretv.android.presentation.components  	VideoInfo +com.libretv.android.presentation.components  WatchHistory +com.libretv.android.presentation.components  aspectRatio +com.libretv.android.presentation.components  
cardElevation +com.libretv.android.presentation.components  clip +com.libretv.android.presentation.components  coerceIn +com.libretv.android.presentation.components  fillMaxWidth +com.libretv.android.presentation.components  
isNotBlank +com.libretv.android.presentation.components  
isNotEmpty +com.libretv.android.presentation.components  
isNullOrBlank +com.libretv.android.presentation.components  padding +com.libretv.android.presentation.components  remember +com.libretv.android.presentation.components  
Composable +com.libretv.android.presentation.navigation  ExperimentalMaterial3Api +com.libretv.android.presentation.navigation  
HistoryScreen +com.libretv.android.presentation.navigation  
HomeScreen +com.libretv.android.presentation.navigation  Icon +com.libretv.android.presentation.navigation  Icons +com.libretv.android.presentation.navigation  ImageVector +com.libretv.android.presentation.navigation  LibreTVNavigation +com.libretv.android.presentation.navigation  Modifier +com.libretv.android.presentation.navigation  
NavigationBar +com.libretv.android.presentation.navigation  NavigationBarItem +com.libretv.android.presentation.navigation  OptIn +com.libretv.android.presentation.navigation  Scaffold +com.libretv.android.presentation.navigation  Screen +com.libretv.android.presentation.navigation  SearchScreen +com.libretv.android.presentation.navigation  SettingsScreen +com.libretv.android.presentation.navigation  String +com.libretv.android.presentation.navigation  Text +com.libretv.android.presentation.navigation  any +com.libretv.android.presentation.navigation  bottomNavItems +com.libretv.android.presentation.navigation  currentBackStackEntryAsState +com.libretv.android.presentation.navigation  findStartDestination +com.libretv.android.presentation.navigation  forEach +com.libretv.android.presentation.navigation  getValue +com.libretv.android.presentation.navigation  listOf +com.libretv.android.presentation.navigation  padding +com.libretv.android.presentation.navigation  provideDelegate +com.libretv.android.presentation.navigation  History 2com.libretv.android.presentation.navigation.Screen  Home 2com.libretv.android.presentation.navigation.Screen  Icons 2com.libretv.android.presentation.navigation.Screen  ImageVector 2com.libretv.android.presentation.navigation.Screen  Screen 2com.libretv.android.presentation.navigation.Screen  Search 2com.libretv.android.presentation.navigation.Screen  Settings 2com.libretv.android.presentation.navigation.Screen  String 2com.libretv.android.presentation.navigation.Screen  icon 2com.libretv.android.presentation.navigation.Screen  route 2com.libretv.android.presentation.navigation.Screen  title 2com.libretv.android.presentation.navigation.Screen  route :com.libretv.android.presentation.navigation.Screen.History  route 7com.libretv.android.presentation.navigation.Screen.Home  route 9com.libretv.android.presentation.navigation.Screen.Search  route ;com.libretv.android.presentation.navigation.Screen.Settings  AlertDialog 0com.libretv.android.presentation.screens.history  	Alignment 0com.libretv.android.presentation.screens.history  Arrangement 0com.libretv.android.presentation.screens.history  Boolean 0com.libretv.android.presentation.screens.history  Box 0com.libretv.android.presentation.screens.history  Card 0com.libretv.android.presentation.screens.history  CircularProgressIndicator 0com.libretv.android.presentation.screens.history  Column 0com.libretv.android.presentation.screens.history  
Composable 0com.libretv.android.presentation.screens.history  Date 0com.libretv.android.presentation.screens.history  	Exception 0com.libretv.android.presentation.screens.history  ExperimentalMaterial3Api 0com.libretv.android.presentation.screens.history  
FontWeight 0com.libretv.android.presentation.screens.history  
HiltViewModel 0com.libretv.android.presentation.screens.history  HistoryItem 0com.libretv.android.presentation.screens.history  
HistoryScreen 0com.libretv.android.presentation.screens.history  HistoryUiState 0com.libretv.android.presentation.screens.history  HistoryViewModel 0com.libretv.android.presentation.screens.history  Icon 0com.libretv.android.presentation.screens.history  
IconButton 0com.libretv.android.presentation.screens.history  Icons 0com.libretv.android.presentation.screens.history  Inject 0com.libretv.android.presentation.screens.history  
LazyColumn 0com.libretv.android.presentation.screens.history  List 0com.libretv.android.presentation.screens.history  Locale 0com.libretv.android.presentation.screens.history  
MaterialTheme 0com.libretv.android.presentation.screens.history  Modifier 0com.libretv.android.presentation.screens.history  MutableStateFlow 0com.libretv.android.presentation.screens.history  OptIn 0com.libretv.android.presentation.screens.history  Row 0com.libretv.android.presentation.screens.history  Spacer 0com.libretv.android.presentation.screens.history  	StateFlow 0com.libretv.android.presentation.screens.history  String 0com.libretv.android.presentation.screens.history  Text 0com.libretv.android.presentation.screens.history  
TextButton 0com.libretv.android.presentation.screens.history  Unit 0com.libretv.android.presentation.screens.history  	VideoCard 0com.libretv.android.presentation.screens.history  	ViewModel 0com.libretv.android.presentation.screens.history  WatchHistory 0com.libretv.android.presentation.screens.history  WatchHistoryRepository 0com.libretv.android.presentation.screens.history  _uiState 0com.libretv.android.presentation.screens.history  android 0com.libretv.android.presentation.screens.history  asStateFlow 0com.libretv.android.presentation.screens.history  collectAsState 0com.libretv.android.presentation.screens.history  com 0com.libretv.android.presentation.screens.history  	emptyList 0com.libretv.android.presentation.screens.history  fillMaxSize 0com.libretv.android.presentation.screens.history  fillMaxWidth 0com.libretv.android.presentation.screens.history  getValue 0com.libretv.android.presentation.screens.history  height 0com.libretv.android.presentation.screens.history  
isNotEmpty 0com.libretv.android.presentation.screens.history  
isNullOrBlank 0com.libretv.android.presentation.screens.history  launch 0com.libretv.android.presentation.screens.history  mutableStateOf 0com.libretv.android.presentation.screens.history  padding 0com.libretv.android.presentation.screens.history  provideDelegate 0com.libretv.android.presentation.screens.history  remember 0com.libretv.android.presentation.screens.history  setValue 0com.libretv.android.presentation.screens.history  spacedBy 0com.libretv.android.presentation.screens.history  update 0com.libretv.android.presentation.screens.history  watchHistoryRepository 0com.libretv.android.presentation.screens.history  weight 0com.libretv.android.presentation.screens.history  width 0com.libretv.android.presentation.screens.history  copy ?com.libretv.android.presentation.screens.history.HistoryUiState  	isLoading ?com.libretv.android.presentation.screens.history.HistoryUiState  watchHistory ?com.libretv.android.presentation.screens.history.HistoryUiState  HistoryUiState Acom.libretv.android.presentation.screens.history.HistoryViewModel  MutableStateFlow Acom.libretv.android.presentation.screens.history.HistoryViewModel  _uiState Acom.libretv.android.presentation.screens.history.HistoryViewModel  android Acom.libretv.android.presentation.screens.history.HistoryViewModel  asStateFlow Acom.libretv.android.presentation.screens.history.HistoryViewModel  clearAllHistory Acom.libretv.android.presentation.screens.history.HistoryViewModel  
deleteHistory Acom.libretv.android.presentation.screens.history.HistoryViewModel  launch Acom.libretv.android.presentation.screens.history.HistoryViewModel  loadWatchHistory Acom.libretv.android.presentation.screens.history.HistoryViewModel  uiState Acom.libretv.android.presentation.screens.history.HistoryViewModel  update Acom.libretv.android.presentation.screens.history.HistoryViewModel  viewModelScope Acom.libretv.android.presentation.screens.history.HistoryViewModel  watchHistoryRepository Acom.libretv.android.presentation.screens.history.HistoryViewModel  libretv 4com.libretv.android.presentation.screens.history.com  android <com.libretv.android.presentation.screens.history.com.libretv  data Dcom.libretv.android.presentation.screens.history.com.libretv.android  model Icom.libretv.android.presentation.screens.history.com.libretv.android.data  WatchHistory Ocom.libretv.android.presentation.screens.history.com.libretv.android.data.model  	Alignment -com.libretv.android.presentation.screens.home  Arrangement -com.libretv.android.presentation.screens.home  Boolean -com.libretv.android.presentation.screens.home  Box -com.libretv.android.presentation.screens.home  Card -com.libretv.android.presentation.screens.home  CardDefaults -com.libretv.android.presentation.screens.home  CategoryGrid -com.libretv.android.presentation.screens.home  CircularProgressIndicator -com.libretv.android.presentation.screens.home  Column -com.libretv.android.presentation.screens.home  
Composable -com.libretv.android.presentation.screens.home  DoubanApiService -com.libretv.android.presentation.screens.home  DoubanMovieCard -com.libretv.android.presentation.screens.home  
DoubanSubject -com.libretv.android.presentation.screens.home  	Exception -com.libretv.android.presentation.screens.home  ExperimentalMaterial3Api -com.libretv.android.presentation.screens.home  
FontWeight -com.libretv.android.presentation.screens.home  
HiltViewModel -com.libretv.android.presentation.screens.home  
HomeScreen -com.libretv.android.presentation.screens.home  HomeUiState -com.libretv.android.presentation.screens.home  
HomeViewModel -com.libretv.android.presentation.screens.home  Inject -com.libretv.android.presentation.screens.home  LazyRow -com.libretv.android.presentation.screens.home  List -com.libretv.android.presentation.screens.home  
MaterialTheme -com.libretv.android.presentation.screens.home  Modifier -com.libretv.android.presentation.screens.home  MutableStateFlow -com.libretv.android.presentation.screens.home  OptIn -com.libretv.android.presentation.screens.home  
PaddingValues -com.libretv.android.presentation.screens.home  Pair -com.libretv.android.presentation.screens.home  Row -com.libretv.android.presentation.screens.home  	SearchBar -com.libretv.android.presentation.screens.home  SectionTitle -com.libretv.android.presentation.screens.home  Spacer -com.libretv.android.presentation.screens.home  	StateFlow -com.libretv.android.presentation.screens.home  String -com.libretv.android.presentation.screens.home  Text -com.libretv.android.presentation.screens.home  Unit -com.libretv.android.presentation.screens.home  	VideoCard -com.libretv.android.presentation.screens.home  VideoRepository -com.libretv.android.presentation.screens.home  	ViewModel -com.libretv.android.presentation.screens.home  WatchHistory -com.libretv.android.presentation.screens.home  WatchHistoryRepository -com.libretv.android.presentation.screens.home  _uiState -com.libretv.android.presentation.screens.home  android -com.libretv.android.presentation.screens.home  asStateFlow -com.libretv.android.presentation.screens.home  
cardColors -com.libretv.android.presentation.screens.home  chunked -com.libretv.android.presentation.screens.home  collectAsState -com.libretv.android.presentation.screens.home  	emptyList -com.libretv.android.presentation.screens.home  fillMaxSize -com.libretv.android.presentation.screens.home  fillMaxWidth -com.libretv.android.presentation.screens.home  forEach -com.libretv.android.presentation.screens.home  getValue -com.libretv.android.presentation.screens.home  height -com.libretv.android.presentation.screens.home  
isNotEmpty -com.libretv.android.presentation.screens.home  launch -com.libretv.android.presentation.screens.home  let -com.libretv.android.presentation.screens.home  listOf -com.libretv.android.presentation.screens.home  loadDoubanRecommendations -com.libretv.android.presentation.screens.home  	onFailure -com.libretv.android.presentation.screens.home  	onSuccess -com.libretv.android.presentation.screens.home  padding -com.libretv.android.presentation.screens.home  provideDelegate -com.libretv.android.presentation.screens.home  repeat -com.libretv.android.presentation.screens.home  spacedBy -com.libretv.android.presentation.screens.home  take -com.libretv.android.presentation.screens.home  to -com.libretv.android.presentation.screens.home  update -com.libretv.android.presentation.screens.home  videoRepository -com.libretv.android.presentation.screens.home  watchHistoryRepository -com.libretv.android.presentation.screens.home  weight -com.libretv.android.presentation.screens.home  width -com.libretv.android.presentation.screens.home  copy 9com.libretv.android.presentation.screens.home.HomeUiState  doubanRecommendations 9com.libretv.android.presentation.screens.home.HomeUiState  error 9com.libretv.android.presentation.screens.home.HomeUiState  	isLoading 9com.libretv.android.presentation.screens.home.HomeUiState  
recentWatched 9com.libretv.android.presentation.screens.home.HomeUiState  HomeUiState ;com.libretv.android.presentation.screens.home.HomeViewModel  MutableStateFlow ;com.libretv.android.presentation.screens.home.HomeViewModel  _uiState ;com.libretv.android.presentation.screens.home.HomeViewModel  android ;com.libretv.android.presentation.screens.home.HomeViewModel  asStateFlow ;com.libretv.android.presentation.screens.home.HomeViewModel  doubanApiService ;com.libretv.android.presentation.screens.home.HomeViewModel  	emptyList ;com.libretv.android.presentation.screens.home.HomeViewModel  launch ;com.libretv.android.presentation.screens.home.HomeViewModel  loadDoubanRecommendations ;com.libretv.android.presentation.screens.home.HomeViewModel  loadHomeData ;com.libretv.android.presentation.screens.home.HomeViewModel  	onFailure ;com.libretv.android.presentation.screens.home.HomeViewModel  	onSuccess ;com.libretv.android.presentation.screens.home.HomeViewModel  searchDoubanMovie ;com.libretv.android.presentation.screens.home.HomeViewModel  take ;com.libretv.android.presentation.screens.home.HomeViewModel  uiState ;com.libretv.android.presentation.screens.home.HomeViewModel  update ;com.libretv.android.presentation.screens.home.HomeViewModel  videoRepository ;com.libretv.android.presentation.screens.home.HomeViewModel  viewModelScope ;com.libretv.android.presentation.screens.home.HomeViewModel  watchHistoryRepository ;com.libretv.android.presentation.screens.home.HomeViewModel  	Alignment /com.libretv.android.presentation.screens.search  Arrangement /com.libretv.android.presentation.screens.search  Boolean /com.libretv.android.presentation.screens.search  Box /com.libretv.android.presentation.screens.search  Button /com.libretv.android.presentation.screens.search  Card /com.libretv.android.presentation.screens.search  CardDefaults /com.libretv.android.presentation.screens.search  CircularProgressIndicator /com.libretv.android.presentation.screens.search  Column /com.libretv.android.presentation.screens.search  
Composable /com.libretv.android.presentation.screens.search  	Exception /com.libretv.android.presentation.screens.search  ExperimentalMaterial3Api /com.libretv.android.presentation.screens.search  
FontWeight /com.libretv.android.presentation.screens.search  	GridCells /com.libretv.android.presentation.screens.search  
HiltViewModel /com.libretv.android.presentation.screens.search  Icon /com.libretv.android.presentation.screens.search  
IconButton /com.libretv.android.presentation.screens.search  Icons /com.libretv.android.presentation.screens.search  Inject /com.libretv.android.presentation.screens.search  LazyRow /com.libretv.android.presentation.screens.search  LazyVerticalGrid /com.libretv.android.presentation.screens.search  List /com.libretv.android.presentation.screens.search  
MaterialTheme /com.libretv.android.presentation.screens.search  Modifier /com.libretv.android.presentation.screens.search  MutableStateFlow /com.libretv.android.presentation.screens.search  OptIn /com.libretv.android.presentation.screens.search  Row /com.libretv.android.presentation.screens.search  
SearchHistory /com.libretv.android.presentation.screens.search  SearchRepository /com.libretv.android.presentation.screens.search  SearchScreen /com.libretv.android.presentation.screens.search  
SearchUiState /com.libretv.android.presentation.screens.search  SearchViewModel /com.libretv.android.presentation.screens.search  SimpleSearchField /com.libretv.android.presentation.screens.search  Spacer /com.libretv.android.presentation.screens.search  	StateFlow /com.libretv.android.presentation.screens.search  String /com.libretv.android.presentation.screens.search  SuggestionChip /com.libretv.android.presentation.screens.search  Text /com.libretv.android.presentation.screens.search  Unit /com.libretv.android.presentation.screens.search  	VideoCard /com.libretv.android.presentation.screens.search  	VideoInfo /com.libretv.android.presentation.screens.search  VideoRepository /com.libretv.android.presentation.screens.search  	ViewModel /com.libretv.android.presentation.screens.search  _uiState /com.libretv.android.presentation.screens.search  asStateFlow /com.libretv.android.presentation.screens.search  
cardColors /com.libretv.android.presentation.screens.search  collectAsState /com.libretv.android.presentation.screens.search  	emptyList /com.libretv.android.presentation.screens.search  fillMaxSize /com.libretv.android.presentation.screens.search  fillMaxWidth /com.libretv.android.presentation.screens.search  getValue /com.libretv.android.presentation.screens.search  height /com.libretv.android.presentation.screens.search  isBlank /com.libretv.android.presentation.screens.search  isEmpty /com.libretv.android.presentation.screens.search  
isNotEmpty /com.libretv.android.presentation.screens.search  
isNullOrEmpty /com.libretv.android.presentation.screens.search  launch /com.libretv.android.presentation.screens.search  	onFailure /com.libretv.android.presentation.screens.search  	onSuccess /com.libretv.android.presentation.screens.search  padding /com.libretv.android.presentation.screens.search  provideDelegate /com.libretv.android.presentation.screens.search  searchRepository /com.libretv.android.presentation.screens.search  spacedBy /com.libretv.android.presentation.screens.search  update /com.libretv.android.presentation.screens.search  videoRepository /com.libretv.android.presentation.screens.search  weight /com.libretv.android.presentation.screens.search  copy =com.libretv.android.presentation.screens.search.SearchUiState  error =com.libretv.android.presentation.screens.search.SearchUiState  	isLoading =com.libretv.android.presentation.screens.search.SearchUiState  query =com.libretv.android.presentation.screens.search.SearchUiState  
searchHistory =com.libretv.android.presentation.screens.search.SearchUiState  
searchResults =com.libretv.android.presentation.screens.search.SearchUiState  MutableStateFlow ?com.libretv.android.presentation.screens.search.SearchViewModel  
SearchUiState ?com.libretv.android.presentation.screens.search.SearchViewModel  _uiState ?com.libretv.android.presentation.screens.search.SearchViewModel  asStateFlow ?com.libretv.android.presentation.screens.search.SearchViewModel  	emptyList ?com.libretv.android.presentation.screens.search.SearchViewModel  isBlank ?com.libretv.android.presentation.screens.search.SearchViewModel  launch ?com.libretv.android.presentation.screens.search.SearchViewModel  loadSearchHistory ?com.libretv.android.presentation.screens.search.SearchViewModel  	onFailure ?com.libretv.android.presentation.screens.search.SearchViewModel  	onSuccess ?com.libretv.android.presentation.screens.search.SearchViewModel  search ?com.libretv.android.presentation.screens.search.SearchViewModel  searchRepository ?com.libretv.android.presentation.screens.search.SearchViewModel  uiState ?com.libretv.android.presentation.screens.search.SearchViewModel  update ?com.libretv.android.presentation.screens.search.SearchViewModel  updateQuery ?com.libretv.android.presentation.screens.search.SearchViewModel  videoRepository ?com.libretv.android.presentation.screens.search.SearchViewModel  viewModelScope ?com.libretv.android.presentation.screens.search.SearchViewModel  	Alignment 1com.libretv.android.presentation.screens.settings  Arrangement 1com.libretv.android.presentation.screens.settings  Boolean 1com.libretv.android.presentation.screens.settings  Card 1com.libretv.android.presentation.screens.settings  CardDefaults 1com.libretv.android.presentation.screens.settings  ChevronRight 1com.libretv.android.presentation.screens.settings  ClickableSettingItem 1com.libretv.android.presentation.screens.settings  Code 1com.libretv.android.presentation.screens.settings  Column 1com.libretv.android.presentation.screens.settings  ColumnScope 1com.libretv.android.presentation.screens.settings  
Composable 1com.libretv.android.presentation.screens.settings  Delete 1com.libretv.android.presentation.screens.settings  	Exception 1com.libretv.android.presentation.screens.settings  ExperimentalMaterial3Api 1com.libretv.android.presentation.screens.settings  Folder 1com.libretv.android.presentation.screens.settings  
FontWeight 1com.libretv.android.presentation.screens.settings  
Fullscreen 1com.libretv.android.presentation.screens.settings  GridView 1com.libretv.android.presentation.screens.settings  
HiltViewModel 1com.libretv.android.presentation.screens.settings  History 1com.libretv.android.presentation.screens.settings  Icon 1com.libretv.android.presentation.screens.settings  Icons 1com.libretv.android.presentation.screens.settings  ImageVector 1com.libretv.android.presentation.screens.settings  Info 1com.libretv.android.presentation.screens.settings  Inject 1com.libretv.android.presentation.screens.settings  Int 1com.libretv.android.presentation.screens.settings  
MaterialTheme 1com.libretv.android.presentation.screens.settings  Modifier 1com.libretv.android.presentation.screens.settings  MutableStateFlow 1com.libretv.android.presentation.screens.settings  OptIn 1com.libretv.android.presentation.screens.settings  Palette 1com.libretv.android.presentation.screens.settings  	PlayArrow 1com.libretv.android.presentation.screens.settings  Row 1com.libretv.android.presentation.screens.settings  SearchRepository 1com.libretv.android.presentation.screens.settings  SettingsManager 1com.libretv.android.presentation.screens.settings  SettingsScreen 1com.libretv.android.presentation.screens.settings  SettingsSection 1com.libretv.android.presentation.screens.settings  SettingsUiState 1com.libretv.android.presentation.screens.settings  SettingsViewModel 1com.libretv.android.presentation.screens.settings  Source 1com.libretv.android.presentation.screens.settings  	StateFlow 1com.libretv.android.presentation.screens.settings  Storage 1com.libretv.android.presentation.screens.settings  String 1com.libretv.android.presentation.screens.settings  Switch 1com.libretv.android.presentation.screens.settings  SwitchSettingItem 1com.libretv.android.presentation.screens.settings  Text 1com.libretv.android.presentation.screens.settings  Unit 1com.libretv.android.presentation.screens.settings  VideoRepository 1com.libretv.android.presentation.screens.settings  	ViewModel 1com.libretv.android.presentation.screens.settings  Warning 1com.libretv.android.presentation.screens.settings  WatchHistoryRepository 1com.libretv.android.presentation.screens.settings  _uiState 1com.libretv.android.presentation.screens.settings  android 1com.libretv.android.presentation.screens.settings  asStateFlow 1com.libretv.android.presentation.screens.settings  
cardColors 1com.libretv.android.presentation.screens.settings  collectAsState 1com.libretv.android.presentation.screens.settings  fillMaxSize 1com.libretv.android.presentation.screens.settings  fillMaxWidth 1com.libretv.android.presentation.screens.settings  getValue 1com.libretv.android.presentation.screens.settings  launch 1com.libretv.android.presentation.screens.settings  padding 1com.libretv.android.presentation.screens.settings  provideDelegate 1com.libretv.android.presentation.screens.settings  searchRepository 1com.libretv.android.presentation.screens.settings  settingsManager 1com.libretv.android.presentation.screens.settings  size 1com.libretv.android.presentation.screens.settings  spacedBy 1com.libretv.android.presentation.screens.settings  videoRepository 1com.libretv.android.presentation.screens.settings  weight 1com.libretv.android.presentation.screens.settings  adultContentEnabled Acom.libretv.android.presentation.screens.settings.SettingsUiState  autoPlayNext Acom.libretv.android.presentation.screens.settings.SettingsUiState  cacheEnabled Acom.libretv.android.presentation.screens.settings.SettingsUiState  cacheSizeMB Acom.libretv.android.presentation.screens.settings.SettingsUiState  copy Acom.libretv.android.presentation.screens.settings.SettingsUiState  gridColumns Acom.libretv.android.presentation.screens.settings.SettingsUiState  playerAutoFullscreen Acom.libretv.android.presentation.screens.settings.SettingsUiState  rememberProgress Acom.libretv.android.presentation.screens.settings.SettingsUiState  	themeMode Acom.libretv.android.presentation.screens.settings.SettingsUiState  MutableStateFlow Ccom.libretv.android.presentation.screens.settings.SettingsViewModel  SettingsUiState Ccom.libretv.android.presentation.screens.settings.SettingsViewModel  _uiState Ccom.libretv.android.presentation.screens.settings.SettingsViewModel  android Ccom.libretv.android.presentation.screens.settings.SettingsViewModel  asStateFlow Ccom.libretv.android.presentation.screens.settings.SettingsViewModel  
clearCache Ccom.libretv.android.presentation.screens.settings.SettingsViewModel  launch Ccom.libretv.android.presentation.screens.settings.SettingsViewModel  searchRepository Ccom.libretv.android.presentation.screens.settings.SettingsViewModel  setAdultContentEnabled Ccom.libretv.android.presentation.screens.settings.SettingsViewModel  setAutoPlayNext Ccom.libretv.android.presentation.screens.settings.SettingsViewModel  setCacheEnabled Ccom.libretv.android.presentation.screens.settings.SettingsViewModel  setPlayerAutoFullscreen Ccom.libretv.android.presentation.screens.settings.SettingsViewModel  setRememberProgress Ccom.libretv.android.presentation.screens.settings.SettingsViewModel  settingsManager Ccom.libretv.android.presentation.screens.settings.SettingsViewModel  uiState Ccom.libretv.android.presentation.screens.settings.SettingsViewModel  videoRepository Ccom.libretv.android.presentation.screens.settings.SettingsViewModel  viewModelScope Ccom.libretv.android.presentation.screens.settings.SettingsViewModel  Activity &com.libretv.android.presentation.theme  Boolean &com.libretv.android.presentation.theme  Build &com.libretv.android.presentation.theme  Color &com.libretv.android.presentation.theme  
Composable &com.libretv.android.presentation.theme  DarkColorScheme &com.libretv.android.presentation.theme  
FontFamily &com.libretv.android.presentation.theme  
FontWeight &com.libretv.android.presentation.theme  LibreTVPrimary &com.libretv.android.presentation.theme  LibreTVPrimaryVariant &com.libretv.android.presentation.theme  LibreTVSecondary &com.libretv.android.presentation.theme  LibreTVSecondaryVariant &com.libretv.android.presentation.theme  LibreTVTheme &com.libretv.android.presentation.theme  LightColorScheme &com.libretv.android.presentation.theme  
Typography &com.libretv.android.presentation.theme  Unit &com.libretv.android.presentation.theme  WindowCompat &com.libretv.android.presentation.theme  Module dagger  Provides dagger  	InstallIn dagger.hilt  AndroidEntryPoint dagger.hilt.android  HiltAndroidApp dagger.hilt.android  
HiltViewModel dagger.hilt.android.lifecycle  ApplicationContext dagger.hilt.android.qualifiers  SingletonComponent dagger.hilt.components  IOException java.io  Class 	java.lang  	Exception 	java.lang  message java.lang.Exception  currentTimeMillis java.lang.System  SimpleDateFormat 	java.text  format java.text.DateFormat  format java.text.Format  format java.text.SimpleDateFormat  AlertDialog 	java.util  	Alignment 	java.util  Arrangement 	java.util  Box 	java.util  Card 	java.util  CircularProgressIndicator 	java.util  Column 	java.util  
Composable 	java.util  Date 	java.util  ExperimentalMaterial3Api 	java.util  
FontWeight 	java.util  HistoryItem 	java.util  HistoryViewModel 	java.util  Icon 	java.util  
IconButton 	java.util  Icons 	java.util  
LazyColumn 	java.util  Locale 	java.util  
MaterialTheme 	java.util  Modifier 	java.util  OptIn 	java.util  Row 	java.util  Spacer 	java.util  String 	java.util  Text 	java.util  
TextButton 	java.util  Unit 	java.util  	VideoCard 	java.util  collectAsState 	java.util  com 	java.util  fillMaxSize 	java.util  fillMaxWidth 	java.util  getValue 	java.util  height 	java.util  
isNotEmpty 	java.util  
isNullOrBlank 	java.util  mutableStateOf 	java.util  padding 	java.util  provideDelegate 	java.util  remember 	java.util  setValue 	java.util  spacedBy 	java.util  weight 	java.util  width 	java.util  
getDefault java.util.Locale  libretv 
java.util.com  android java.util.com.libretv  data java.util.com.libretv.android  model "java.util.com.libretv.android.data  WatchHistory (java.util.com.libretv.android.data.model  TimeUnit java.util.concurrent  SECONDS java.util.concurrent.TimeUnit  	Generated javax.annotation.processing  Inject javax.inject  	Qualifier javax.inject  	Singleton javax.inject  Array kotlin  Boolean kotlin  CharSequence kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  Int kotlin  Lazy kotlin  Long kotlin  Nothing kotlin  OptIn kotlin  Pair kotlin  Result kotlin  String kotlin  Suppress kotlin  	Throwable kotlin  Unit kotlin  apply kotlin  arrayOf kotlin  lazy kotlin  let kotlin  map kotlin  	onFailure kotlin  	onSuccess kotlin  repeat kotlin  to kotlin  not kotlin.Boolean  isEmpty kotlin.CharSequence  sp 
kotlin.Double  coerceIn kotlin.Float  div kotlin.Float  invoke kotlin.Function0  invoke kotlin.Function1  invoke kotlin.Function2  	compareTo 
kotlin.Int  minus 
kotlin.Int  plus 
kotlin.Int  times 
kotlin.Int  toLong 
kotlin.Int  value kotlin.Lazy  	compareTo kotlin.Long  minus kotlin.Long  toFloat kotlin.Long  toInt kotlin.Long  
component1 kotlin.Pair  
component2 kotlin.Pair  	Companion 
kotlin.Result  failure 
kotlin.Result  	onFailure 
kotlin.Result  	onSuccess 
kotlin.Result  success 
kotlin.Result  failure kotlin.Result.Companion  success kotlin.Result.Companion  isBlank 
kotlin.String  isEmpty 
kotlin.String  
isNotBlank 
kotlin.String  
isNotEmpty 
kotlin.String  
isNullOrBlank 
kotlin.String  
isNullOrEmpty 
kotlin.String  let 
kotlin.String  plus 
kotlin.String  
startsWith 
kotlin.String  to 
kotlin.String  trim 
kotlin.String  
trimMargin 
kotlin.String  message kotlin.Throwable  AnnotationRetention kotlin.annotation  	Retention kotlin.annotation  BINARY %kotlin.annotation.AnnotationRetention  Iterator kotlin.collections  List kotlin.collections  Map kotlin.collections  MutableList kotlin.collections  
MutableMap kotlin.collections  
MutableSet kotlin.collections  Set kotlin.collections  any kotlin.collections  chunked kotlin.collections  
component1 kotlin.collections  
component2 kotlin.collections  	emptyList kotlin.collections  forEach kotlin.collections  ifEmpty kotlin.collections  isEmpty kotlin.collections  
isNotEmpty kotlin.collections  
isNullOrEmpty kotlin.collections  listOf kotlin.collections  map kotlin.collections  mapOf kotlin.collections  
mutableListOf kotlin.collections  mutableMapOf kotlin.collections  mutableSetOf kotlin.collections  take kotlin.collections  hasNext kotlin.collections.Iterator  next kotlin.collections.Iterator  chunked kotlin.collections.List  ifEmpty kotlin.collections.List  isEmpty kotlin.collections.List  
isNotEmpty kotlin.collections.List  iterator kotlin.collections.List  map kotlin.collections.List  size kotlin.collections.List  take kotlin.collections.List  Entry kotlin.collections.Map  map kotlin.collections.Map  
component1 kotlin.collections.Map.Entry  
component2 kotlin.collections.Map.Entry  add kotlin.collections.MutableList  addAll kotlin.collections.MutableList  
isNotEmpty kotlin.collections.MutableList  put kotlin.collections.MutableMap  SuspendFunction1 kotlin.coroutines  
startsWith 	kotlin.io  Throws 
kotlin.jvm  java 
kotlin.jvm  ReadOnlyProperty kotlin.properties  getValue "kotlin.properties.ReadOnlyProperty  provideDelegate "kotlin.properties.ReadOnlyProperty  coerceIn 
kotlin.ranges  KClass kotlin.reflect  
KFunction1 kotlin.reflect  KMutableProperty0 kotlin.reflect  
KProperty0 kotlin.reflect  
KProperty2 kotlin.reflect  java kotlin.reflect.KClass  Sequence kotlin.sequences  any kotlin.sequences  chunked kotlin.sequences  forEach kotlin.sequences  ifEmpty kotlin.sequences  map kotlin.sequences  take kotlin.sequences  any kotlin.sequences.Sequence  any kotlin.text  chunked kotlin.text  forEach kotlin.text  ifEmpty kotlin.text  isBlank kotlin.text  isEmpty kotlin.text  
isNotBlank kotlin.text  
isNotEmpty kotlin.text  
isNullOrBlank kotlin.text  
isNullOrEmpty kotlin.text  map kotlin.text  repeat kotlin.text  
startsWith kotlin.text  take kotlin.text  trim kotlin.text  
trimMargin kotlin.text  CoroutineScope kotlinx.coroutines  Job kotlinx.coroutines  launch kotlinx.coroutines  _uiState !kotlinx.coroutines.CoroutineScope  android !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  loadDoubanRecommendations !kotlinx.coroutines.CoroutineScope  	onFailure !kotlinx.coroutines.CoroutineScope  	onSuccess !kotlinx.coroutines.CoroutineScope  searchRepository !kotlinx.coroutines.CoroutineScope  settingsManager !kotlinx.coroutines.CoroutineScope  update !kotlinx.coroutines.CoroutineScope  videoRepository !kotlinx.coroutines.CoroutineScope  watchHistoryRepository !kotlinx.coroutines.CoroutineScope  Boolean kotlinx.coroutines.flow  DoubanApiService kotlinx.coroutines.flow  
DoubanSubject kotlinx.coroutines.flow  	Exception kotlinx.coroutines.flow  Flow kotlinx.coroutines.flow  
FlowCollector kotlinx.coroutines.flow  
HiltViewModel kotlinx.coroutines.flow  HistoryUiState kotlinx.coroutines.flow  HomeUiState kotlinx.coroutines.flow  Inject kotlinx.coroutines.flow  Int kotlinx.coroutines.flow  List kotlinx.coroutines.flow  MutableStateFlow kotlinx.coroutines.flow  
SearchHistory kotlinx.coroutines.flow  SearchRepository kotlinx.coroutines.flow  
SearchUiState kotlinx.coroutines.flow  SettingsManager kotlinx.coroutines.flow  SettingsUiState kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  String kotlinx.coroutines.flow  	VideoInfo kotlinx.coroutines.flow  VideoRepository kotlinx.coroutines.flow  	ViewModel kotlinx.coroutines.flow  WatchHistory kotlinx.coroutines.flow  WatchHistoryRepository kotlinx.coroutines.flow  _uiState kotlinx.coroutines.flow  android kotlinx.coroutines.flow  asStateFlow kotlinx.coroutines.flow  	emptyList kotlinx.coroutines.flow  first kotlinx.coroutines.flow  isBlank kotlinx.coroutines.flow  launch kotlinx.coroutines.flow  loadDoubanRecommendations kotlinx.coroutines.flow  map kotlinx.coroutines.flow  	onFailure kotlinx.coroutines.flow  	onSuccess kotlinx.coroutines.flow  searchRepository kotlinx.coroutines.flow  settingsManager kotlinx.coroutines.flow  take kotlinx.coroutines.flow  update kotlinx.coroutines.flow  videoRepository kotlinx.coroutines.flow  watchHistoryRepository kotlinx.coroutines.flow  collect kotlinx.coroutines.flow.Flow  first kotlinx.coroutines.flow.Flow  map kotlinx.coroutines.flow.Flow  <SAM-CONSTRUCTOR> %kotlinx.coroutines.flow.FlowCollector  asStateFlow (kotlinx.coroutines.flow.MutableStateFlow  update (kotlinx.coroutines.flow.MutableStateFlow  value (kotlinx.coroutines.flow.MutableStateFlow  collectAsState !kotlinx.coroutines.flow.StateFlow  
SerialName kotlinx.serialization  Serializable kotlinx.serialization  HttpUrl okhttp3  Interceptor okhttp3  OkHttpClient okhttp3  Request okhttp3  Response okhttp3  Chain okhttp3.Interceptor  proceed okhttp3.Interceptor.Chain  request okhttp3.Interceptor.Chain  Builder okhttp3.OkHttpClient  	Companion okhttp3.OkHttpClient  addInterceptor okhttp3.OkHttpClient.Builder  build okhttp3.OkHttpClient.Builder  connectTimeout okhttp3.OkHttpClient.Builder  readTimeout okhttp3.OkHttpClient.Builder  writeTimeout okhttp3.OkHttpClient.Builder  Builder okhttp3.Request  
newBuilder okhttp3.Request  url okhttp3.Request  build okhttp3.Request.Builder  header okhttp3.Request.Builder  code okhttp3.Response  isSuccessful okhttp3.Response  HttpLoggingInterceptor okhttp3.logging  HttpLoggingInterceptor &okhttp3.logging.HttpLoggingInterceptor  Level &okhttp3.logging.HttpLoggingInterceptor  apply &okhttp3.logging.HttpLoggingInterceptor  level &okhttp3.logging.HttpLoggingInterceptor  BODY ,okhttp3.logging.HttpLoggingInterceptor.Level  Response 	retrofit2  Retrofit 	retrofit2  body retrofit2.Response  code retrofit2.Response  isSuccessful retrofit2.Response  Builder retrofit2.Retrofit  create retrofit2.Retrofit  addConverterFactory retrofit2.Retrofit.Builder  baseUrl retrofit2.Retrofit.Builder  build retrofit2.Retrofit.Builder  client retrofit2.Retrofit.Builder  GsonConverterFactory retrofit2.converter.gson  create -retrofit2.converter.gson.GsonConverterFactory  GET retrofit2.http  Path retrofit2.http  Query retrofit2.http  Url retrofit2.http                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           