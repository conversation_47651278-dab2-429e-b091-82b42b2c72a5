package com.libretv.android.presentation.screens.history

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.libretv.android.data.model.WatchHistory
import com.libretv.android.data.repository.WatchHistoryRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 历史记录ViewModel
 */
@HiltViewModel
class HistoryViewModel @Inject constructor(
    private val watchHistoryRepository: WatchHistoryRepository
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(HistoryUiState())
    val uiState: StateFlow<HistoryUiState> = _uiState.asStateFlow()
    
    init {
        loadWatchHistory()
    }
    
    private fun loadWatchHistory() {
        viewModelScope.launch {
            _uiState.update { it.copy(isLoading = true) }
            
            watchHistoryRepository.getAllWatchHistory()
                .collect { history ->
                    _uiState.update { 
                        it.copy(
                            isLoading = false,
                            watchHistory = history
                        )
                    }
                }
        }
    }
    
    fun deleteHistory(history: WatchHistory) {
        viewModelScope.launch {
            try {
                watchHistoryRepository.deleteWatchHistory(history)
            } catch (e: Exception) {
                // 处理错误
                android.util.Log.e("HistoryViewModel", "Failed to delete history", e)
            }
        }
    }
    
    fun clearAllHistory() {
        viewModelScope.launch {
            try {
                watchHistoryRepository.clearAllHistory()
            } catch (e: Exception) {
                // 处理错误
                android.util.Log.e("HistoryViewModel", "Failed to clear all history", e)
            }
        }
    }
    
    fun refresh() {
        loadWatchHistory()
    }
}

/**
 * 历史记录UI状态
 */
data class HistoryUiState(
    val isLoading: Boolean = false,
    val watchHistory: List<WatchHistory> = emptyList()
)
