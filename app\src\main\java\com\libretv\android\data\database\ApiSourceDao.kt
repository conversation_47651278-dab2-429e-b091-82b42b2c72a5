package com.libretv.android.data.database

import androidx.room.*
import com.libretv.android.data.model.ApiSource
import kotlinx.coroutines.flow.Flow

/**
 * API源数据访问对象
 */
@Dao
interface ApiSourceDao {
    
    @Query("SELECT * FROM api_sources ORDER BY `order` ASC, name ASC")
    fun getAllApiSources(): Flow<List<ApiSource>>
    
    @Query("SELECT * FROM api_sources WHERE isEnabled = 1 ORDER BY `order` ASC, name ASC")
    fun getEnabledApiSources(): Flow<List<ApiSource>>
    
    @Query("SELECT * FROM api_sources WHERE isAdult = 0 AND isEnabled = 1 ORDER BY `order` ASC, name ASC")
    fun getNormalApiSources(): Flow<List<ApiSource>>
    
    @Query("SELECT * FROM api_sources WHERE isAdult = 1 AND isEnabled = 1 ORDER BY `order` ASC, name ASC")
    fun getAdultApiSources(): Flow<List<ApiSource>>
    
    @Query("SELECT * FROM api_sources WHERE isCustom = 1 ORDER BY `order` ASC, name ASC")
    fun getCustomApiSources(): Flow<List<ApiSource>>
    
    @Query("SELECT * FROM api_sources WHERE code = :code")
    suspend fun getApiSourceByCode(code: String): ApiSource?
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertApiSource(apiSource: ApiSource)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertApiSources(apiSources: List<ApiSource>)
    
    @Update
    suspend fun updateApiSource(apiSource: ApiSource)
    
    @Delete
    suspend fun deleteApiSource(apiSource: ApiSource)
    
    @Query("UPDATE api_sources SET isEnabled = :isEnabled WHERE code = :code")
    suspend fun updateEnabledStatus(code: String, isEnabled: Boolean)
    
    @Query("UPDATE api_sources SET `order` = :order WHERE code = :code")
    suspend fun updateOrder(code: String, order: Int)
    
    @Query("DELETE FROM api_sources WHERE isCustom = 1")
    suspend fun deleteAllCustomSources()
}
