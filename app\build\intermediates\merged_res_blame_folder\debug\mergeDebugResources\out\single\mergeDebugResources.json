[{"merged": "D:\\SDK\\.gradle\\daemon\\8.11.1\\com.libretv.android.app-debug-4:\\drawable_ic_launcher_foreground.xml.flat", "source": "D:\\SDK\\.gradle\\daemon\\8.11.1\\com.libretv.android.app-main-6:\\drawable\\ic_launcher_foreground.xml"}, {"merged": "D:\\SDK\\.gradle\\daemon\\8.11.1\\com.libretv.android.app-debug-4:\\mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "D:\\SDK\\.gradle\\daemon\\8.11.1\\com.libretv.android.app-main-6:\\mipmap-xxxhdpi\\ic_launcher_round.webp"}, {"merged": "D:\\SDK\\.gradle\\daemon\\8.11.1\\com.libretv.android.app-debug-4:\\mipmap-xhdpi_ic_launcher.webp.flat", "source": "D:\\SDK\\.gradle\\daemon\\8.11.1\\com.libretv.android.app-main-6:\\mipmap-xhdpi\\ic_launcher.webp"}, {"merged": "D:\\SDK\\.gradle\\daemon\\8.11.1\\com.libretv.android.app-debug-4:\\mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "D:\\SDK\\.gradle\\daemon\\8.11.1\\com.libretv.android.app-main-6:\\mipmap-anydpi-v26\\ic_launcher.xml"}, {"merged": "D:\\SDK\\.gradle\\daemon\\8.11.1\\com.libretv.android.app-debug-4:\\xml_data_extraction_rules.xml.flat", "source": "D:\\SDK\\.gradle\\daemon\\8.11.1\\com.libretv.android.app-main-6:\\xml\\data_extraction_rules.xml"}, {"merged": "D:\\SDK\\.gradle\\daemon\\8.11.1\\com.libretv.android.app-debug-4:\\mipmap-hdpi_ic_launcher.webp.flat", "source": "D:\\SDK\\.gradle\\daemon\\8.11.1\\com.libretv.android.app-main-6:\\mipmap-hdpi\\ic_launcher.webp"}, {"merged": "D:\\SDK\\.gradle\\daemon\\8.11.1\\com.libretv.android.app-debug-4:\\xml_backup_rules.xml.flat", "source": "D:\\SDK\\.gradle\\daemon\\8.11.1\\com.libretv.android.app-main-6:\\xml\\backup_rules.xml"}, {"merged": "D:\\SDK\\.gradle\\daemon\\8.11.1\\com.libretv.android.app-debug-4:\\mipmap-mdpi_ic_launcher_round.webp.flat", "source": "D:\\SDK\\.gradle\\daemon\\8.11.1\\com.libretv.android.app-main-6:\\mipmap-mdpi\\ic_launcher_round.webp"}, {"merged": "D:\\SDK\\.gradle\\daemon\\8.11.1\\com.libretv.android.app-debug-4:\\mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "D:\\SDK\\.gradle\\daemon\\8.11.1\\com.libretv.android.app-main-6:\\mipmap-xxhdpi\\ic_launcher_round.webp"}, {"merged": "D:\\SDK\\.gradle\\daemon\\8.11.1\\com.libretv.android.app-debug-4:\\mipmap-hdpi_ic_launcher_round.webp.flat", "source": "D:\\SDK\\.gradle\\daemon\\8.11.1\\com.libretv.android.app-main-6:\\mipmap-hdpi\\ic_launcher_round.webp"}, {"merged": "com.libretv.android.app-debug-4:/xml_file_paths.xml.flat", "source": "com.libretv.android.app-main-6:/xml/file_paths.xml"}, {"merged": "D:\\SDK\\.gradle\\daemon\\8.11.1\\com.libretv.android.app-debug-4:\\mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "D:\\SDK\\.gradle\\daemon\\8.11.1\\com.libretv.android.app-main-6:\\mipmap-xxxhdpi\\ic_launcher.webp"}, {"merged": "D:\\SDK\\.gradle\\daemon\\8.11.1\\com.libretv.android.app-debug-4:\\mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "D:\\SDK\\.gradle\\daemon\\8.11.1\\com.libretv.android.app-main-6:\\mipmap-xhdpi\\ic_launcher_round.webp"}, {"merged": "D:\\SDK\\.gradle\\daemon\\8.11.1\\com.libretv.android.app-debug-4:\\drawable_ic_launcher_background.xml.flat", "source": "D:\\SDK\\.gradle\\daemon\\8.11.1\\com.libretv.android.app-main-6:\\drawable\\ic_launcher_background.xml"}, {"merged": "D:\\SDK\\.gradle\\daemon\\8.11.1\\com.libretv.android.app-debug-4:\\mipmap-mdpi_ic_launcher.webp.flat", "source": "D:\\SDK\\.gradle\\daemon\\8.11.1\\com.libretv.android.app-main-6:\\mipmap-mdpi\\ic_launcher.webp"}, {"merged": "D:\\SDK\\.gradle\\daemon\\8.11.1\\com.libretv.android.app-debug-4:\\mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "D:\\SDK\\.gradle\\daemon\\8.11.1\\com.libretv.android.app-main-6:\\mipmap-anydpi-v26\\ic_launcher_round.xml"}, {"merged": "D:\\SDK\\.gradle\\daemon\\8.11.1\\com.libretv.android.app-debug-4:\\mipmap-xxhdpi_ic_launcher.webp.flat", "source": "D:\\SDK\\.gradle\\daemon\\8.11.1\\com.libretv.android.app-main-6:\\mipmap-xxhdpi\\ic_launcher.webp"}]