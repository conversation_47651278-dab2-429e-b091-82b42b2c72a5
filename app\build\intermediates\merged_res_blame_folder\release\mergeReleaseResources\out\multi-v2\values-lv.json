{"logs": [{"outputFile": "com.libretv.android.app-mergeReleaseResources-2:/values-lv/values-lv.xml", "map": [{"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\6b27351f1a96970e2ba142187de324d4\\transformed\\media3-ui-1.7.1\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,567,831,915,998,1081,1176,1271,1344,1411,1505,1599,1665,1732,1795,1871,1977,2088,2195,2269,2351,2425,2498,2598,2697,2763,2829,2882,2940,2988,3049,3107,3183,3247,3312,3377,3434,3500,3552,3616,3694,3772,3827", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "17,12,12,83,82,82,94,94,72,66,93,93,65,66,62,75,105,110,106,73,81,73,72,99,98,65,65,52,57,47,60,57,75,63,64,64,56,65,51,63,77,77,54,66", "endOffsets": "281,562,826,910,993,1076,1171,1266,1339,1406,1500,1594,1660,1727,1790,1866,1972,2083,2190,2264,2346,2420,2493,2593,2692,2758,2824,2877,2935,2983,3044,3102,3178,3242,3307,3372,3429,3495,3547,3611,3689,3767,3822,3889"}, "to": {"startLines": "2,11,16,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,336,617,3791,3875,3958,4041,4136,4231,4304,4371,4465,4559,4625,4692,4755,4831,4937,5048,5155,5229,5311,5385,5458,5558,5657,5723,6507,6560,6618,6666,6727,6785,6861,6925,6990,7055,7112,7178,7230,7294,7372,7450,7505", "endLines": "10,15,20,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101", "endColumns": "17,12,12,83,82,82,94,94,72,66,93,93,65,66,62,75,105,110,106,73,81,73,72,99,98,65,65,52,57,47,60,57,75,63,64,64,56,65,51,63,77,77,54,66", "endOffsets": "331,612,876,3870,3953,4036,4131,4226,4299,4366,4460,4554,4620,4687,4750,4826,4932,5043,5150,5224,5306,5380,5453,5553,5652,5718,5784,6555,6613,6661,6722,6780,6856,6920,6985,7050,7107,7173,7225,7289,7367,7445,7500,7567"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\26497276d619c831fa389bb2f1588ec8\\transformed\\media3-exoplayer-1.7.1\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,196,268,338,418,495,596,694", "endColumns": "74,65,71,69,79,76,100,97,78", "endOffsets": "125,191,263,333,413,490,591,689,768"}, "to": {"startLines": "76,77,78,79,80,81,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "5789,5864,5930,6002,6072,6152,6229,6330,6428", "endColumns": "74,65,71,69,79,76,100,97,78", "endOffsets": "5859,5925,5997,6067,6147,6224,6325,6423,6502"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\0b493fc5385f598ec7f47c0a10bc88bb\\transformed\\material3-release\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,182,309,424,549,658,758,875,1013,1131,1278,1364,1462,1556,1657,1776,1900,2003,2141,2272,2410,2593,2725,2844,2971,3091,3186,3285,3406,3541,3643,3757,3863,3998,4143,4252,4355,4438,4533,4627,4737,4827,4914,5025,5105,5191,5286,5390,5481,5579,5668,5775,5877,5977,6130,6210,6315", "endColumns": "126,126,114,124,108,99,116,137,117,146,85,97,93,100,118,123,102,137,130,137,182,131,118,126,119,94,98,120,134,101,113,105,134,144,108,102,82,94,93,109,89,86,110,79,85,94,103,90,97,88,106,101,99,152,79,104,98", "endOffsets": "177,304,419,544,653,753,870,1008,1126,1273,1359,1457,1551,1652,1771,1895,1998,2136,2267,2405,2588,2720,2839,2966,3086,3181,3280,3401,3536,3638,3752,3858,3993,4138,4247,4350,4433,4528,4622,4732,4822,4909,5020,5100,5186,5281,5385,5476,5574,5663,5770,5872,5972,6125,6205,6310,6409"}, "to": {"startLines": "104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7746,7873,8000,8115,8240,8349,8449,8566,8704,8822,8969,9055,9153,9247,9348,9467,9591,9694,9832,9963,10101,10284,10416,10535,10662,10782,10877,10976,11097,11232,11334,11448,11554,11689,11834,11943,12046,12129,12224,12318,12428,12518,12605,12716,12796,12882,12977,13081,13172,13270,13359,13466,13568,13668,13821,13901,14006", "endColumns": "126,126,114,124,108,99,116,137,117,146,85,97,93,100,118,123,102,137,130,137,182,131,118,126,119,94,98,120,134,101,113,105,134,144,108,102,82,94,93,109,89,86,110,79,85,94,103,90,97,88,106,101,99,152,79,104,98", "endOffsets": "7868,7995,8110,8235,8344,8444,8561,8699,8817,8964,9050,9148,9242,9343,9462,9586,9689,9827,9958,10096,10279,10411,10530,10657,10777,10872,10971,11092,11227,11329,11443,11549,11684,11829,11938,12041,12124,12219,12313,12423,12513,12600,12711,12791,12877,12972,13076,13167,13265,13354,13461,13563,13663,13816,13896,14001,14100"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\2425a4573e4fb65053b44e46c3806d87\\transformed\\foundation-release\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,142,232", "endColumns": "86,89,89", "endOffsets": "137,227,317"}, "to": {"startLines": "21,180,181", "startColumns": "4,4,4", "startOffsets": "881,15720,15810", "endColumns": "86,89,89", "endOffsets": "963,15805,15895"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\4c56c0fb8a9fdb1c153785f7a46f8077\\transformed\\media3-session-1.7.1\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,239,314,420,517,618,703,786,879,967,1034,1130,1224,1317,1393,1490,1572,1671,1741,1811,1892,1979,2073", "endColumns": "75,107,74,105,96,100,84,82,92,87,66,95,93,92,75,96,81,98,69,69,80,86,93,102", "endOffsets": "126,234,309,415,512,613,698,781,874,962,1029,1125,1219,1312,1388,1485,1567,1666,1736,1806,1887,1974,2068,2171"}, "to": {"startLines": "32,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,161,162,163,164,165,166", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1982,2251,2359,2434,2540,2637,2738,2823,2906,2999,3087,3154,3250,3344,3437,3513,3610,3692,14105,14175,14245,14326,14413,14507", "endColumns": "75,107,74,105,96,100,84,82,92,87,66,95,93,92,75,96,81,98,69,69,80,86,93,102", "endOffsets": "2053,2354,2429,2535,2632,2733,2818,2901,2994,3082,3149,3245,3339,3432,3508,3605,3687,3786,14170,14240,14321,14408,14502,14605"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\955c05c442e591726e4833ad1e0fc303\\transformed\\core-1.16.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,563,671,786", "endColumns": "97,101,99,100,106,107,114,100", "endOffsets": "148,250,350,451,558,666,781,882"}, "to": {"startLines": "22,23,24,25,26,27,28,176", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "968,1066,1168,1268,1369,1476,1584,15351", "endColumns": "97,101,99,100,106,107,114,100", "endOffsets": "1061,1163,1263,1364,1471,1579,1694,15447"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\406d1819cde1682167f3af9259cf68c0\\transformed\\ui-release\\res\\values-lv\\values-lv.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "193,291,380,476,579,669,755,843,936,1020,1105,1192,1265,1355,1431,1508,1584,1662,1730", "endColumns": "97,88,95,102,89,85,87,92,83,84,86,72,89,75,76,75,77,67,121", "endOffsets": "286,375,471,574,664,750,838,931,1015,1100,1187,1260,1350,1426,1503,1579,1657,1725,1847"}, "to": {"startLines": "29,30,31,33,34,102,103,167,168,169,170,171,172,173,174,175,177,178,179", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1699,1797,1886,2058,2161,7572,7658,14610,14703,14787,14872,14959,15032,15122,15198,15275,15452,15530,15598", "endColumns": "97,88,95,102,89,85,87,92,83,84,86,72,89,75,76,75,77,67,121", "endOffsets": "1792,1881,1977,2156,2246,7653,7741,14698,14782,14867,14954,15027,15117,15193,15270,15346,15525,15593,15715"}}]}]}