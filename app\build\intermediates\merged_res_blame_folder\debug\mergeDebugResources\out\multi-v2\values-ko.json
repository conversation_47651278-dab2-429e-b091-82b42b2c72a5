{"logs": [{"outputFile": "com.libretv.android.app-mergeDebugResources-2:/values-ko/values-ko.xml", "map": [{"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\26497276d619c831fa389bb2f1588ec8\\transformed\\media3-exoplayer-1.7.1\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,172,232,288,360,419,501,581", "endColumns": "58,57,59,55,71,58,81,79,62", "endOffsets": "109,167,227,283,355,414,496,576,639"}, "to": {"startLines": "74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "4935,4994,5052,5112,5168,5240,5299,5381,5461", "endColumns": "58,57,59,55,71,58,81,79,62", "endOffsets": "4989,5047,5107,5163,5235,5294,5376,5456,5519"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\4c56c0fb8a9fdb1c153785f7a46f8077\\transformed\\media3-session-1.7.1\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,276,370,457,532,608,687,768,846,904,994,1075,1157,1230,1317,1385,1467,1534,1598,1670,1746,1824", "endColumns": "69,83,66,93,86,74,75,78,80,77,57,89,80,81,72,86,67,81,66,63,71,75,77,81", "endOffsets": "120,204,271,365,452,527,603,682,763,841,899,989,1070,1152,1225,1312,1380,1462,1529,1593,1665,1741,1819,1901"}, "to": {"startLines": "30,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,160,161,162,163,164,165", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1654,1894,1978,2045,2139,2226,2301,2377,2456,2537,2615,2673,2763,2844,2926,2999,3086,3154,12399,12466,12530,12602,12678,12756", "endColumns": "69,83,66,93,86,74,75,78,80,77,57,89,80,81,72,86,67,81,66,63,71,75,77,81", "endOffsets": "1719,1973,2040,2134,2221,2296,2372,2451,2532,2610,2668,2758,2839,2921,2994,3081,3149,3231,12461,12525,12597,12673,12751,12833"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\0b493fc5385f598ec7f47c0a10bc88bb\\transformed\\material3-release\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,160,264,370,474,566,655,761,880,990,1112,1194,1291,1376,1466,1575,1689,1791,1904,2015,2127,2260,2369,2473,2580,2689,2775,2870,2979,3088,3179,3277,3374,3488,3607,3706,3798,3872,3961,4049,4143,4226,4308,4403,4483,4565,4662,4757,4852,4949,5032,5128,5222,5320,5437,5517,5611", "endColumns": "104,103,105,103,91,88,105,118,109,121,81,96,84,89,108,113,101,112,110,111,132,108,103,106,108,85,94,108,108,90,97,96,113,118,98,91,73,88,87,93,82,81,94,79,81,96,94,94,96,82,95,93,97,116,79,93,90", "endOffsets": "155,259,365,469,561,650,756,875,985,1107,1189,1286,1371,1461,1570,1684,1786,1899,2010,2122,2255,2364,2468,2575,2684,2770,2865,2974,3083,3174,3272,3369,3483,3602,3701,3793,3867,3956,4044,4138,4221,4303,4398,4478,4560,4657,4752,4847,4944,5027,5123,5217,5315,5432,5512,5606,5697"}, "to": {"startLines": "102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6667,6772,6876,6982,7086,7178,7267,7373,7492,7602,7724,7806,7903,7988,8078,8187,8301,8403,8516,8627,8739,8872,8981,9085,9192,9301,9387,9482,9591,9700,9791,9889,9986,10100,10219,10318,10410,10484,10573,10661,10755,10838,10920,11015,11095,11177,11274,11369,11464,11561,11644,11740,11834,11932,12049,12129,12223", "endColumns": "104,103,105,103,91,88,105,118,109,121,81,96,84,89,108,113,101,112,110,111,132,108,103,106,108,85,94,108,108,90,97,96,113,118,98,91,73,88,87,93,82,81,94,79,81,96,94,94,96,82,95,93,97,116,79,93,90", "endOffsets": "6767,6871,6977,7081,7173,7262,7368,7487,7597,7719,7801,7898,7983,8073,8182,8296,8398,8511,8622,8734,8867,8976,9080,9187,9296,9382,9477,9586,9695,9786,9884,9981,10095,10214,10313,10405,10479,10568,10656,10750,10833,10915,11010,11090,11172,11269,11364,11459,11556,11639,11735,11829,11927,12044,12124,12218,12309"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\955c05c442e591726e4833ad1e0fc303\\transformed\\core-1.16.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,438,534,632,732", "endColumns": "91,99,93,96,95,97,99,100", "endOffsets": "142,242,336,433,529,627,727,828"}, "to": {"startLines": "20,21,22,23,24,25,26,175", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "734,826,926,1020,1117,1213,1311,13514", "endColumns": "91,99,93,96,95,97,99,100", "endOffsets": "821,921,1015,1112,1208,1306,1406,13610"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\6b27351f1a96970e2ba142187de324d4\\transformed\\media3-ui-1.7.1\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,454,612,686,759,830,911,989,1048,1109,1186,1262,1326,1387,1446,1511,1599,1687,1776,1840,1909,1974,2032,2109,2185,2246,2311,2364,2421,2467,2526,2582,2644,2701,2761,2817,2873,2937,2987,3043,3113,3183,3236", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,73,72,70,80,77,58,60,76,75,63,60,58,64,87,87,88,63,68,64,57,76,75,60,64,52,56,45,58,55,61,56,59,55,55,63,49,55,69,69,52,63", "endOffsets": "282,449,607,681,754,825,906,984,1043,1104,1181,1257,1321,1382,1441,1506,1594,1682,1771,1835,1904,1969,2027,2104,2180,2241,2306,2359,2416,2462,2521,2577,2639,2696,2756,2812,2868,2932,2982,3038,3108,3178,3231,3295"}, "to": {"startLines": "2,11,15,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,337,504,3236,3310,3383,3454,3535,3613,3672,3733,3810,3886,3950,4011,4070,4135,4223,4311,4400,4464,4533,4598,4656,4733,4809,4870,5524,5577,5634,5680,5739,5795,5857,5914,5974,6030,6086,6150,6200,6256,6326,6396,6449", "endLines": "10,14,18,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99", "endColumns": "17,12,12,73,72,70,80,77,58,60,76,75,63,60,58,64,87,87,88,63,68,64,57,76,75,60,64,52,56,45,58,55,61,56,59,55,55,63,49,55,69,69,52,63", "endOffsets": "332,499,657,3305,3378,3449,3530,3608,3667,3728,3805,3881,3945,4006,4065,4130,4218,4306,4395,4459,4528,4593,4651,4728,4804,4865,4930,5572,5629,5675,5734,5790,5852,5909,5969,6025,6081,6145,6195,6251,6321,6391,6444,6508"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\2425a4573e4fb65053b44e46c3806d87\\transformed\\foundation-release\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,127,209", "endColumns": "71,81,78", "endOffsets": "122,204,283"}, "to": {"startLines": "19,179,180", "startColumns": "4,4,4", "startOffsets": "662,13867,13949", "endColumns": "71,81,78", "endOffsets": "729,13944,14023"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\3bf0e9913a8fb17f961a2eb6334c8d97\\transformed\\material-release\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "84", "endOffsets": "135"}, "to": {"startLines": "159", "startColumns": "4", "startOffsets": "12314", "endColumns": "84", "endOffsets": "12394"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\406d1819cde1682167f3af9259cf68c0\\transformed\\ui-release\\res\\values-ko\\values-ko.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "178,257,333,421,511,591,666,745,824,903,976,1052,1120,1201,1277,1351,1421,1495,1559", "endColumns": "78,75,87,89,79,74,78,78,78,72,75,67,80,75,73,69,73,63,113", "endOffsets": "252,328,416,506,586,661,740,819,898,971,1047,1115,1196,1272,1346,1416,1490,1554,1668"}, "to": {"startLines": "27,28,29,31,32,100,101,166,167,168,169,170,171,172,173,174,176,177,178", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1411,1490,1566,1724,1814,6513,6588,12838,12917,12996,13069,13145,13213,13294,13370,13444,13615,13689,13753", "endColumns": "78,75,87,89,79,74,78,78,78,72,75,67,80,75,73,69,73,63,113", "endOffsets": "1485,1561,1649,1809,1889,6583,6662,12912,12991,13064,13140,13208,13289,13365,13439,13509,13684,13748,13862"}}]}]}