package com.libretv.android

import android.app.Application
import dagger.hilt.android.HiltAndroidApp

/**
 * LibreTV Android应用程序类
 * 
 * 基于LibreTV Web版本的Android实现
 * 提供完整的视频搜索和播放功能
 */
@HiltAndroidApp
class LibreTVApplication : Application() {
    
    override fun onCreate() {
        super.onCreate()
        
        // 初始化应用程序
        initializeApp()
    }
    
    private fun initializeApp() {
        // 这里可以添加应用程序初始化逻辑
        // 例如：崩溃报告、分析工具等
    }
}
