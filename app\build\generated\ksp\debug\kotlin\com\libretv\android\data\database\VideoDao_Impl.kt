package com.libretv.android.`data`.database

import androidx.room.EntityDeleteOrUpdateAdapter
import androidx.room.EntityInsertAdapter
import androidx.room.RoomDatabase
import androidx.room.coroutines.createFlow
import androidx.room.util.getColumnIndexOrThrow
import androidx.room.util.performSuspending
import androidx.sqlite.SQLiteStatement
import com.libretv.android.`data`.model.VideoInfo
import javax.`annotation`.processing.Generated
import kotlin.Boolean
import kotlin.Int
import kotlin.Long
import kotlin.String
import kotlin.Suppress
import kotlin.Unit
import kotlin.collections.List
import kotlin.collections.MutableList
import kotlin.collections.mutableListOf
import kotlin.reflect.KClass
import kotlinx.coroutines.flow.Flow

@Generated(value = ["androidx.room.RoomProcessor"])
@Suppress(names = ["UNCHECKED_CAST", "DEPRECATION", "REDUNDANT_PROJECTION", "REMOVAL"])
public class VideoDao_Impl(
  __db: RoomDatabase,
) : VideoDao {
  private val __db: RoomDatabase

  private val __insertAdapterOfVideoInfo: EntityInsertAdapter<VideoInfo>

  private val __deleteAdapterOfVideoInfo: EntityDeleteOrUpdateAdapter<VideoInfo>

  private val __updateAdapterOfVideoInfo: EntityDeleteOrUpdateAdapter<VideoInfo>
  init {
    this.__db = __db
    this.__insertAdapterOfVideoInfo = object : EntityInsertAdapter<VideoInfo>() {
      protected override fun createQuery(): String =
          "INSERT OR REPLACE INTO `videos` (`vodId`,`vodName`,`vodPic`,`vodRemarks`,`vodYear`,`vodArea`,`vodDirector`,`vodActor`,`vodContent`,`vodPlayUrl`,`typeName`,`sourceName`,`sourceCode`,`apiUrl`,`lastWatchTime`,`watchProgress`,`isFavorite`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)"

      protected override fun bind(statement: SQLiteStatement, entity: VideoInfo) {
        statement.bindText(1, entity.vodId)
        statement.bindText(2, entity.vodName)
        val _tmpVodPic: String? = entity.vodPic
        if (_tmpVodPic == null) {
          statement.bindNull(3)
        } else {
          statement.bindText(3, _tmpVodPic)
        }
        val _tmpVodRemarks: String? = entity.vodRemarks
        if (_tmpVodRemarks == null) {
          statement.bindNull(4)
        } else {
          statement.bindText(4, _tmpVodRemarks)
        }
        val _tmpVodYear: String? = entity.vodYear
        if (_tmpVodYear == null) {
          statement.bindNull(5)
        } else {
          statement.bindText(5, _tmpVodYear)
        }
        val _tmpVodArea: String? = entity.vodArea
        if (_tmpVodArea == null) {
          statement.bindNull(6)
        } else {
          statement.bindText(6, _tmpVodArea)
        }
        val _tmpVodDirector: String? = entity.vodDirector
        if (_tmpVodDirector == null) {
          statement.bindNull(7)
        } else {
          statement.bindText(7, _tmpVodDirector)
        }
        val _tmpVodActor: String? = entity.vodActor
        if (_tmpVodActor == null) {
          statement.bindNull(8)
        } else {
          statement.bindText(8, _tmpVodActor)
        }
        val _tmpVodContent: String? = entity.vodContent
        if (_tmpVodContent == null) {
          statement.bindNull(9)
        } else {
          statement.bindText(9, _tmpVodContent)
        }
        val _tmpVodPlayUrl: String? = entity.vodPlayUrl
        if (_tmpVodPlayUrl == null) {
          statement.bindNull(10)
        } else {
          statement.bindText(10, _tmpVodPlayUrl)
        }
        val _tmpTypeName: String? = entity.typeName
        if (_tmpTypeName == null) {
          statement.bindNull(11)
        } else {
          statement.bindText(11, _tmpTypeName)
        }
        val _tmpSourceName: String? = entity.sourceName
        if (_tmpSourceName == null) {
          statement.bindNull(12)
        } else {
          statement.bindText(12, _tmpSourceName)
        }
        val _tmpSourceCode: String? = entity.sourceCode
        if (_tmpSourceCode == null) {
          statement.bindNull(13)
        } else {
          statement.bindText(13, _tmpSourceCode)
        }
        val _tmpApiUrl: String? = entity.apiUrl
        if (_tmpApiUrl == null) {
          statement.bindNull(14)
        } else {
          statement.bindText(14, _tmpApiUrl)
        }
        statement.bindLong(15, entity.lastWatchTime)
        statement.bindLong(16, entity.watchProgress)
        val _tmp: Int = if (entity.isFavorite) 1 else 0
        statement.bindLong(17, _tmp.toLong())
      }
    }
    this.__deleteAdapterOfVideoInfo = object : EntityDeleteOrUpdateAdapter<VideoInfo>() {
      protected override fun createQuery(): String = "DELETE FROM `videos` WHERE `vodId` = ?"

      protected override fun bind(statement: SQLiteStatement, entity: VideoInfo) {
        statement.bindText(1, entity.vodId)
      }
    }
    this.__updateAdapterOfVideoInfo = object : EntityDeleteOrUpdateAdapter<VideoInfo>() {
      protected override fun createQuery(): String =
          "UPDATE OR ABORT `videos` SET `vodId` = ?,`vodName` = ?,`vodPic` = ?,`vodRemarks` = ?,`vodYear` = ?,`vodArea` = ?,`vodDirector` = ?,`vodActor` = ?,`vodContent` = ?,`vodPlayUrl` = ?,`typeName` = ?,`sourceName` = ?,`sourceCode` = ?,`apiUrl` = ?,`lastWatchTime` = ?,`watchProgress` = ?,`isFavorite` = ? WHERE `vodId` = ?"

      protected override fun bind(statement: SQLiteStatement, entity: VideoInfo) {
        statement.bindText(1, entity.vodId)
        statement.bindText(2, entity.vodName)
        val _tmpVodPic: String? = entity.vodPic
        if (_tmpVodPic == null) {
          statement.bindNull(3)
        } else {
          statement.bindText(3, _tmpVodPic)
        }
        val _tmpVodRemarks: String? = entity.vodRemarks
        if (_tmpVodRemarks == null) {
          statement.bindNull(4)
        } else {
          statement.bindText(4, _tmpVodRemarks)
        }
        val _tmpVodYear: String? = entity.vodYear
        if (_tmpVodYear == null) {
          statement.bindNull(5)
        } else {
          statement.bindText(5, _tmpVodYear)
        }
        val _tmpVodArea: String? = entity.vodArea
        if (_tmpVodArea == null) {
          statement.bindNull(6)
        } else {
          statement.bindText(6, _tmpVodArea)
        }
        val _tmpVodDirector: String? = entity.vodDirector
        if (_tmpVodDirector == null) {
          statement.bindNull(7)
        } else {
          statement.bindText(7, _tmpVodDirector)
        }
        val _tmpVodActor: String? = entity.vodActor
        if (_tmpVodActor == null) {
          statement.bindNull(8)
        } else {
          statement.bindText(8, _tmpVodActor)
        }
        val _tmpVodContent: String? = entity.vodContent
        if (_tmpVodContent == null) {
          statement.bindNull(9)
        } else {
          statement.bindText(9, _tmpVodContent)
        }
        val _tmpVodPlayUrl: String? = entity.vodPlayUrl
        if (_tmpVodPlayUrl == null) {
          statement.bindNull(10)
        } else {
          statement.bindText(10, _tmpVodPlayUrl)
        }
        val _tmpTypeName: String? = entity.typeName
        if (_tmpTypeName == null) {
          statement.bindNull(11)
        } else {
          statement.bindText(11, _tmpTypeName)
        }
        val _tmpSourceName: String? = entity.sourceName
        if (_tmpSourceName == null) {
          statement.bindNull(12)
        } else {
          statement.bindText(12, _tmpSourceName)
        }
        val _tmpSourceCode: String? = entity.sourceCode
        if (_tmpSourceCode == null) {
          statement.bindNull(13)
        } else {
          statement.bindText(13, _tmpSourceCode)
        }
        val _tmpApiUrl: String? = entity.apiUrl
        if (_tmpApiUrl == null) {
          statement.bindNull(14)
        } else {
          statement.bindText(14, _tmpApiUrl)
        }
        statement.bindLong(15, entity.lastWatchTime)
        statement.bindLong(16, entity.watchProgress)
        val _tmp: Int = if (entity.isFavorite) 1 else 0
        statement.bindLong(17, _tmp.toLong())
        statement.bindText(18, entity.vodId)
      }
    }
  }

  public override suspend fun insertVideo(video: VideoInfo): Unit = performSuspending(__db, false,
      true) { _connection ->
    __insertAdapterOfVideoInfo.insert(_connection, video)
  }

  public override suspend fun insertVideos(videos: List<VideoInfo>): Unit = performSuspending(__db,
      false, true) { _connection ->
    __insertAdapterOfVideoInfo.insert(_connection, videos)
  }

  public override suspend fun deleteVideo(video: VideoInfo): Unit = performSuspending(__db, false,
      true) { _connection ->
    __deleteAdapterOfVideoInfo.handle(_connection, video)
  }

  public override suspend fun updateVideo(video: VideoInfo): Unit = performSuspending(__db, false,
      true) { _connection ->
    __updateAdapterOfVideoInfo.handle(_connection, video)
  }

  public override suspend fun getVideoById(vodId: String): VideoInfo? {
    val _sql: String = "SELECT * FROM videos WHERE vodId = ?"
    return performSuspending(__db, true, false) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        var _argIndex: Int = 1
        _stmt.bindText(_argIndex, vodId)
        val _columnIndexOfVodId: Int = getColumnIndexOrThrow(_stmt, "vodId")
        val _columnIndexOfVodName: Int = getColumnIndexOrThrow(_stmt, "vodName")
        val _columnIndexOfVodPic: Int = getColumnIndexOrThrow(_stmt, "vodPic")
        val _columnIndexOfVodRemarks: Int = getColumnIndexOrThrow(_stmt, "vodRemarks")
        val _columnIndexOfVodYear: Int = getColumnIndexOrThrow(_stmt, "vodYear")
        val _columnIndexOfVodArea: Int = getColumnIndexOrThrow(_stmt, "vodArea")
        val _columnIndexOfVodDirector: Int = getColumnIndexOrThrow(_stmt, "vodDirector")
        val _columnIndexOfVodActor: Int = getColumnIndexOrThrow(_stmt, "vodActor")
        val _columnIndexOfVodContent: Int = getColumnIndexOrThrow(_stmt, "vodContent")
        val _columnIndexOfVodPlayUrl: Int = getColumnIndexOrThrow(_stmt, "vodPlayUrl")
        val _columnIndexOfTypeName: Int = getColumnIndexOrThrow(_stmt, "typeName")
        val _columnIndexOfSourceName: Int = getColumnIndexOrThrow(_stmt, "sourceName")
        val _columnIndexOfSourceCode: Int = getColumnIndexOrThrow(_stmt, "sourceCode")
        val _columnIndexOfApiUrl: Int = getColumnIndexOrThrow(_stmt, "apiUrl")
        val _columnIndexOfLastWatchTime: Int = getColumnIndexOrThrow(_stmt, "lastWatchTime")
        val _columnIndexOfWatchProgress: Int = getColumnIndexOrThrow(_stmt, "watchProgress")
        val _columnIndexOfIsFavorite: Int = getColumnIndexOrThrow(_stmt, "isFavorite")
        val _result: VideoInfo?
        if (_stmt.step()) {
          val _tmpVodId: String
          _tmpVodId = _stmt.getText(_columnIndexOfVodId)
          val _tmpVodName: String
          _tmpVodName = _stmt.getText(_columnIndexOfVodName)
          val _tmpVodPic: String?
          if (_stmt.isNull(_columnIndexOfVodPic)) {
            _tmpVodPic = null
          } else {
            _tmpVodPic = _stmt.getText(_columnIndexOfVodPic)
          }
          val _tmpVodRemarks: String?
          if (_stmt.isNull(_columnIndexOfVodRemarks)) {
            _tmpVodRemarks = null
          } else {
            _tmpVodRemarks = _stmt.getText(_columnIndexOfVodRemarks)
          }
          val _tmpVodYear: String?
          if (_stmt.isNull(_columnIndexOfVodYear)) {
            _tmpVodYear = null
          } else {
            _tmpVodYear = _stmt.getText(_columnIndexOfVodYear)
          }
          val _tmpVodArea: String?
          if (_stmt.isNull(_columnIndexOfVodArea)) {
            _tmpVodArea = null
          } else {
            _tmpVodArea = _stmt.getText(_columnIndexOfVodArea)
          }
          val _tmpVodDirector: String?
          if (_stmt.isNull(_columnIndexOfVodDirector)) {
            _tmpVodDirector = null
          } else {
            _tmpVodDirector = _stmt.getText(_columnIndexOfVodDirector)
          }
          val _tmpVodActor: String?
          if (_stmt.isNull(_columnIndexOfVodActor)) {
            _tmpVodActor = null
          } else {
            _tmpVodActor = _stmt.getText(_columnIndexOfVodActor)
          }
          val _tmpVodContent: String?
          if (_stmt.isNull(_columnIndexOfVodContent)) {
            _tmpVodContent = null
          } else {
            _tmpVodContent = _stmt.getText(_columnIndexOfVodContent)
          }
          val _tmpVodPlayUrl: String?
          if (_stmt.isNull(_columnIndexOfVodPlayUrl)) {
            _tmpVodPlayUrl = null
          } else {
            _tmpVodPlayUrl = _stmt.getText(_columnIndexOfVodPlayUrl)
          }
          val _tmpTypeName: String?
          if (_stmt.isNull(_columnIndexOfTypeName)) {
            _tmpTypeName = null
          } else {
            _tmpTypeName = _stmt.getText(_columnIndexOfTypeName)
          }
          val _tmpSourceName: String?
          if (_stmt.isNull(_columnIndexOfSourceName)) {
            _tmpSourceName = null
          } else {
            _tmpSourceName = _stmt.getText(_columnIndexOfSourceName)
          }
          val _tmpSourceCode: String?
          if (_stmt.isNull(_columnIndexOfSourceCode)) {
            _tmpSourceCode = null
          } else {
            _tmpSourceCode = _stmt.getText(_columnIndexOfSourceCode)
          }
          val _tmpApiUrl: String?
          if (_stmt.isNull(_columnIndexOfApiUrl)) {
            _tmpApiUrl = null
          } else {
            _tmpApiUrl = _stmt.getText(_columnIndexOfApiUrl)
          }
          val _tmpLastWatchTime: Long
          _tmpLastWatchTime = _stmt.getLong(_columnIndexOfLastWatchTime)
          val _tmpWatchProgress: Long
          _tmpWatchProgress = _stmt.getLong(_columnIndexOfWatchProgress)
          val _tmpIsFavorite: Boolean
          val _tmp: Int
          _tmp = _stmt.getLong(_columnIndexOfIsFavorite).toInt()
          _tmpIsFavorite = _tmp != 0
          _result =
              VideoInfo(_tmpVodId,_tmpVodName,_tmpVodPic,_tmpVodRemarks,_tmpVodYear,_tmpVodArea,_tmpVodDirector,_tmpVodActor,_tmpVodContent,_tmpVodPlayUrl,_tmpTypeName,_tmpSourceName,_tmpSourceCode,_tmpApiUrl,_tmpLastWatchTime,_tmpWatchProgress,_tmpIsFavorite)
        } else {
          _result = null
        }
        _result
      } finally {
        _stmt.close()
      }
    }
  }

  public override fun getFavoriteVideos(): Flow<List<VideoInfo>> {
    val _sql: String = "SELECT * FROM videos WHERE isFavorite = 1 ORDER BY lastWatchTime DESC"
    return createFlow(__db, false, arrayOf("videos")) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        val _columnIndexOfVodId: Int = getColumnIndexOrThrow(_stmt, "vodId")
        val _columnIndexOfVodName: Int = getColumnIndexOrThrow(_stmt, "vodName")
        val _columnIndexOfVodPic: Int = getColumnIndexOrThrow(_stmt, "vodPic")
        val _columnIndexOfVodRemarks: Int = getColumnIndexOrThrow(_stmt, "vodRemarks")
        val _columnIndexOfVodYear: Int = getColumnIndexOrThrow(_stmt, "vodYear")
        val _columnIndexOfVodArea: Int = getColumnIndexOrThrow(_stmt, "vodArea")
        val _columnIndexOfVodDirector: Int = getColumnIndexOrThrow(_stmt, "vodDirector")
        val _columnIndexOfVodActor: Int = getColumnIndexOrThrow(_stmt, "vodActor")
        val _columnIndexOfVodContent: Int = getColumnIndexOrThrow(_stmt, "vodContent")
        val _columnIndexOfVodPlayUrl: Int = getColumnIndexOrThrow(_stmt, "vodPlayUrl")
        val _columnIndexOfTypeName: Int = getColumnIndexOrThrow(_stmt, "typeName")
        val _columnIndexOfSourceName: Int = getColumnIndexOrThrow(_stmt, "sourceName")
        val _columnIndexOfSourceCode: Int = getColumnIndexOrThrow(_stmt, "sourceCode")
        val _columnIndexOfApiUrl: Int = getColumnIndexOrThrow(_stmt, "apiUrl")
        val _columnIndexOfLastWatchTime: Int = getColumnIndexOrThrow(_stmt, "lastWatchTime")
        val _columnIndexOfWatchProgress: Int = getColumnIndexOrThrow(_stmt, "watchProgress")
        val _columnIndexOfIsFavorite: Int = getColumnIndexOrThrow(_stmt, "isFavorite")
        val _result: MutableList<VideoInfo> = mutableListOf()
        while (_stmt.step()) {
          val _item: VideoInfo
          val _tmpVodId: String
          _tmpVodId = _stmt.getText(_columnIndexOfVodId)
          val _tmpVodName: String
          _tmpVodName = _stmt.getText(_columnIndexOfVodName)
          val _tmpVodPic: String?
          if (_stmt.isNull(_columnIndexOfVodPic)) {
            _tmpVodPic = null
          } else {
            _tmpVodPic = _stmt.getText(_columnIndexOfVodPic)
          }
          val _tmpVodRemarks: String?
          if (_stmt.isNull(_columnIndexOfVodRemarks)) {
            _tmpVodRemarks = null
          } else {
            _tmpVodRemarks = _stmt.getText(_columnIndexOfVodRemarks)
          }
          val _tmpVodYear: String?
          if (_stmt.isNull(_columnIndexOfVodYear)) {
            _tmpVodYear = null
          } else {
            _tmpVodYear = _stmt.getText(_columnIndexOfVodYear)
          }
          val _tmpVodArea: String?
          if (_stmt.isNull(_columnIndexOfVodArea)) {
            _tmpVodArea = null
          } else {
            _tmpVodArea = _stmt.getText(_columnIndexOfVodArea)
          }
          val _tmpVodDirector: String?
          if (_stmt.isNull(_columnIndexOfVodDirector)) {
            _tmpVodDirector = null
          } else {
            _tmpVodDirector = _stmt.getText(_columnIndexOfVodDirector)
          }
          val _tmpVodActor: String?
          if (_stmt.isNull(_columnIndexOfVodActor)) {
            _tmpVodActor = null
          } else {
            _tmpVodActor = _stmt.getText(_columnIndexOfVodActor)
          }
          val _tmpVodContent: String?
          if (_stmt.isNull(_columnIndexOfVodContent)) {
            _tmpVodContent = null
          } else {
            _tmpVodContent = _stmt.getText(_columnIndexOfVodContent)
          }
          val _tmpVodPlayUrl: String?
          if (_stmt.isNull(_columnIndexOfVodPlayUrl)) {
            _tmpVodPlayUrl = null
          } else {
            _tmpVodPlayUrl = _stmt.getText(_columnIndexOfVodPlayUrl)
          }
          val _tmpTypeName: String?
          if (_stmt.isNull(_columnIndexOfTypeName)) {
            _tmpTypeName = null
          } else {
            _tmpTypeName = _stmt.getText(_columnIndexOfTypeName)
          }
          val _tmpSourceName: String?
          if (_stmt.isNull(_columnIndexOfSourceName)) {
            _tmpSourceName = null
          } else {
            _tmpSourceName = _stmt.getText(_columnIndexOfSourceName)
          }
          val _tmpSourceCode: String?
          if (_stmt.isNull(_columnIndexOfSourceCode)) {
            _tmpSourceCode = null
          } else {
            _tmpSourceCode = _stmt.getText(_columnIndexOfSourceCode)
          }
          val _tmpApiUrl: String?
          if (_stmt.isNull(_columnIndexOfApiUrl)) {
            _tmpApiUrl = null
          } else {
            _tmpApiUrl = _stmt.getText(_columnIndexOfApiUrl)
          }
          val _tmpLastWatchTime: Long
          _tmpLastWatchTime = _stmt.getLong(_columnIndexOfLastWatchTime)
          val _tmpWatchProgress: Long
          _tmpWatchProgress = _stmt.getLong(_columnIndexOfWatchProgress)
          val _tmpIsFavorite: Boolean
          val _tmp: Int
          _tmp = _stmt.getLong(_columnIndexOfIsFavorite).toInt()
          _tmpIsFavorite = _tmp != 0
          _item =
              VideoInfo(_tmpVodId,_tmpVodName,_tmpVodPic,_tmpVodRemarks,_tmpVodYear,_tmpVodArea,_tmpVodDirector,_tmpVodActor,_tmpVodContent,_tmpVodPlayUrl,_tmpTypeName,_tmpSourceName,_tmpSourceCode,_tmpApiUrl,_tmpLastWatchTime,_tmpWatchProgress,_tmpIsFavorite)
          _result.add(_item)
        }
        _result
      } finally {
        _stmt.close()
      }
    }
  }

  public override suspend fun searchVideos(query: String): List<VideoInfo> {
    val _sql: String =
        "SELECT * FROM videos WHERE vodName LIKE '%' || ? || '%' ORDER BY lastWatchTime DESC"
    return performSuspending(__db, true, false) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        var _argIndex: Int = 1
        _stmt.bindText(_argIndex, query)
        val _columnIndexOfVodId: Int = getColumnIndexOrThrow(_stmt, "vodId")
        val _columnIndexOfVodName: Int = getColumnIndexOrThrow(_stmt, "vodName")
        val _columnIndexOfVodPic: Int = getColumnIndexOrThrow(_stmt, "vodPic")
        val _columnIndexOfVodRemarks: Int = getColumnIndexOrThrow(_stmt, "vodRemarks")
        val _columnIndexOfVodYear: Int = getColumnIndexOrThrow(_stmt, "vodYear")
        val _columnIndexOfVodArea: Int = getColumnIndexOrThrow(_stmt, "vodArea")
        val _columnIndexOfVodDirector: Int = getColumnIndexOrThrow(_stmt, "vodDirector")
        val _columnIndexOfVodActor: Int = getColumnIndexOrThrow(_stmt, "vodActor")
        val _columnIndexOfVodContent: Int = getColumnIndexOrThrow(_stmt, "vodContent")
        val _columnIndexOfVodPlayUrl: Int = getColumnIndexOrThrow(_stmt, "vodPlayUrl")
        val _columnIndexOfTypeName: Int = getColumnIndexOrThrow(_stmt, "typeName")
        val _columnIndexOfSourceName: Int = getColumnIndexOrThrow(_stmt, "sourceName")
        val _columnIndexOfSourceCode: Int = getColumnIndexOrThrow(_stmt, "sourceCode")
        val _columnIndexOfApiUrl: Int = getColumnIndexOrThrow(_stmt, "apiUrl")
        val _columnIndexOfLastWatchTime: Int = getColumnIndexOrThrow(_stmt, "lastWatchTime")
        val _columnIndexOfWatchProgress: Int = getColumnIndexOrThrow(_stmt, "watchProgress")
        val _columnIndexOfIsFavorite: Int = getColumnIndexOrThrow(_stmt, "isFavorite")
        val _result: MutableList<VideoInfo> = mutableListOf()
        while (_stmt.step()) {
          val _item: VideoInfo
          val _tmpVodId: String
          _tmpVodId = _stmt.getText(_columnIndexOfVodId)
          val _tmpVodName: String
          _tmpVodName = _stmt.getText(_columnIndexOfVodName)
          val _tmpVodPic: String?
          if (_stmt.isNull(_columnIndexOfVodPic)) {
            _tmpVodPic = null
          } else {
            _tmpVodPic = _stmt.getText(_columnIndexOfVodPic)
          }
          val _tmpVodRemarks: String?
          if (_stmt.isNull(_columnIndexOfVodRemarks)) {
            _tmpVodRemarks = null
          } else {
            _tmpVodRemarks = _stmt.getText(_columnIndexOfVodRemarks)
          }
          val _tmpVodYear: String?
          if (_stmt.isNull(_columnIndexOfVodYear)) {
            _tmpVodYear = null
          } else {
            _tmpVodYear = _stmt.getText(_columnIndexOfVodYear)
          }
          val _tmpVodArea: String?
          if (_stmt.isNull(_columnIndexOfVodArea)) {
            _tmpVodArea = null
          } else {
            _tmpVodArea = _stmt.getText(_columnIndexOfVodArea)
          }
          val _tmpVodDirector: String?
          if (_stmt.isNull(_columnIndexOfVodDirector)) {
            _tmpVodDirector = null
          } else {
            _tmpVodDirector = _stmt.getText(_columnIndexOfVodDirector)
          }
          val _tmpVodActor: String?
          if (_stmt.isNull(_columnIndexOfVodActor)) {
            _tmpVodActor = null
          } else {
            _tmpVodActor = _stmt.getText(_columnIndexOfVodActor)
          }
          val _tmpVodContent: String?
          if (_stmt.isNull(_columnIndexOfVodContent)) {
            _tmpVodContent = null
          } else {
            _tmpVodContent = _stmt.getText(_columnIndexOfVodContent)
          }
          val _tmpVodPlayUrl: String?
          if (_stmt.isNull(_columnIndexOfVodPlayUrl)) {
            _tmpVodPlayUrl = null
          } else {
            _tmpVodPlayUrl = _stmt.getText(_columnIndexOfVodPlayUrl)
          }
          val _tmpTypeName: String?
          if (_stmt.isNull(_columnIndexOfTypeName)) {
            _tmpTypeName = null
          } else {
            _tmpTypeName = _stmt.getText(_columnIndexOfTypeName)
          }
          val _tmpSourceName: String?
          if (_stmt.isNull(_columnIndexOfSourceName)) {
            _tmpSourceName = null
          } else {
            _tmpSourceName = _stmt.getText(_columnIndexOfSourceName)
          }
          val _tmpSourceCode: String?
          if (_stmt.isNull(_columnIndexOfSourceCode)) {
            _tmpSourceCode = null
          } else {
            _tmpSourceCode = _stmt.getText(_columnIndexOfSourceCode)
          }
          val _tmpApiUrl: String?
          if (_stmt.isNull(_columnIndexOfApiUrl)) {
            _tmpApiUrl = null
          } else {
            _tmpApiUrl = _stmt.getText(_columnIndexOfApiUrl)
          }
          val _tmpLastWatchTime: Long
          _tmpLastWatchTime = _stmt.getLong(_columnIndexOfLastWatchTime)
          val _tmpWatchProgress: Long
          _tmpWatchProgress = _stmt.getLong(_columnIndexOfWatchProgress)
          val _tmpIsFavorite: Boolean
          val _tmp: Int
          _tmp = _stmt.getLong(_columnIndexOfIsFavorite).toInt()
          _tmpIsFavorite = _tmp != 0
          _item =
              VideoInfo(_tmpVodId,_tmpVodName,_tmpVodPic,_tmpVodRemarks,_tmpVodYear,_tmpVodArea,_tmpVodDirector,_tmpVodActor,_tmpVodContent,_tmpVodPlayUrl,_tmpTypeName,_tmpSourceName,_tmpSourceCode,_tmpApiUrl,_tmpLastWatchTime,_tmpWatchProgress,_tmpIsFavorite)
          _result.add(_item)
        }
        _result
      } finally {
        _stmt.close()
      }
    }
  }

  public override suspend fun deleteOldVideos(timestamp: Long) {
    val _sql: String = "DELETE FROM videos WHERE isFavorite = 0 AND lastWatchTime < ?"
    return performSuspending(__db, false, true) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        var _argIndex: Int = 1
        _stmt.bindLong(_argIndex, timestamp)
        _stmt.step()
      } finally {
        _stmt.close()
      }
    }
  }

  public override suspend fun updateFavoriteStatus(vodId: String, isFavorite: Boolean) {
    val _sql: String = "UPDATE videos SET isFavorite = ? WHERE vodId = ?"
    return performSuspending(__db, false, true) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        var _argIndex: Int = 1
        val _tmp: Int = if (isFavorite) 1 else 0
        _stmt.bindLong(_argIndex, _tmp.toLong())
        _argIndex = 2
        _stmt.bindText(_argIndex, vodId)
        _stmt.step()
      } finally {
        _stmt.close()
      }
    }
  }

  public override suspend fun updateWatchProgress(
    vodId: String,
    progress: Long,
    timestamp: Long,
  ) {
    val _sql: String = "UPDATE videos SET watchProgress = ?, lastWatchTime = ? WHERE vodId = ?"
    return performSuspending(__db, false, true) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        var _argIndex: Int = 1
        _stmt.bindLong(_argIndex, progress)
        _argIndex = 2
        _stmt.bindLong(_argIndex, timestamp)
        _argIndex = 3
        _stmt.bindText(_argIndex, vodId)
        _stmt.step()
      } finally {
        _stmt.close()
      }
    }
  }

  public companion object {
    public fun getRequiredConverters(): List<KClass<*>> = emptyList()
  }
}
