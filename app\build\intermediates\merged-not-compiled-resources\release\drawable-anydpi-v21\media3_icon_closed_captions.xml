<!-- Copyright 2024 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="24dp"
    android:height="24dp"
    android:viewportWidth="960"
    android:viewportHeight="960">
  <path
      android:fillColor="@android:color/white"
      android:pathData="M200,800Q167,800 143.5,776.5Q120,753 120,720L120,240Q120,207 143.5,183.5Q167,160 200,160L760,160Q793,160 816.5,183.5Q840,207 840,240L840,720Q840,753 816.5,776.5Q793,800 760,800L200,800ZM200,720L760,720Q760,720 760,720Q760,720 760,720L760,240Q760,240 760,240Q760,240 760,240L200,240Q200,240 200,240Q200,240 200,240L200,720Q200,720 200,720Q200,720 200,720ZM280,600L400,600Q417,600 428.5,588.5Q440,577 440,560L440,520L380,520L380,540Q380,540 380,540Q380,540 380,540L300,540Q300,540 300,540Q300,540 300,540L300,420Q300,420 300,420Q300,420 300,420L380,420Q380,420 380,420Q380,420 380,420L380,440L440,440L440,400Q440,383 428.5,371.5Q417,360 400,360L280,360Q263,360 251.5,371.5Q240,383 240,400L240,560Q240,577 251.5,588.5Q263,600 280,600ZM560,600L680,600Q697,600 708.5,588.5Q720,577 720,560L720,520L660,520L660,540Q660,540 660,540Q660,540 660,540L580,540Q580,540 580,540Q580,540 580,540L580,420Q580,420 580,420Q580,420 580,420L660,420Q660,420 660,420Q660,420 660,420L660,440L720,440L720,400Q720,383 708.5,371.5Q697,360 680,360L560,360Q543,360 531.5,371.5Q520,383 520,400L520,560Q520,577 531.5,588.5Q543,600 560,600ZM200,720Q200,720 200,720Q200,720 200,720L200,240Q200,240 200,240Q200,240 200,240L200,240Q200,240 200,240Q200,240 200,240L200,720Q200,720 200,720Q200,720 200,720L200,720Z"/>
</vector>
