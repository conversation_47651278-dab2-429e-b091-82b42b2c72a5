  Activity android.app  Context android.content  ContextWrapper android.content  ContextThemeWrapper android.view  ComponentActivity androidx.activity  ComponentActivity androidx.core.app  	ViewModel androidx.lifecycle  OnConflictStrategy 
androidx.room  RoomDatabase 
androidx.room  	Companion  androidx.room.OnConflictStrategy  ApiSourceDao !com.libretv.android.data.database  ApiSourceDao_Impl !com.libretv.android.data.database  
Converters !com.libretv.android.data.database  LibreTVDatabase !com.libretv.android.data.database  LibreTVDatabase_Impl !com.libretv.android.data.database  SearchHistoryDao !com.libretv.android.data.database  SearchHistoryDao_Impl !com.libretv.android.data.database  VideoDao !com.libretv.android.data.database  
VideoDao_Impl !com.libretv.android.data.database  WatchHistoryDao !com.libretv.android.data.database  WatchHistoryDao_Impl !com.libretv.android.data.database  	Companion 3com.libretv.android.data.database.ApiSourceDao_Impl  	Companion 1com.libretv.android.data.database.LibreTVDatabase  	Companion 7com.libretv.android.data.database.SearchHistoryDao_Impl  	Companion /com.libretv.android.data.database.VideoDao_Impl  	Companion 6com.libretv.android.data.database.WatchHistoryDao_Impl  ApiResponse com.libretv.android.data.model  	ApiSource com.libretv.android.data.model  
SearchHistory com.libretv.android.data.model  VideoDetailInfo com.libretv.android.data.model  	VideoInfo com.libretv.android.data.model  WatchHistory com.libretv.android.data.model  
ApiService  com.libretv.android.data.network  DoubanApiService  com.libretv.android.data.network  InternalApiService  com.libretv.android.data.network  SettingsManager $com.libretv.android.data.preferences  	Companion 4com.libretv.android.data.preferences.SettingsManager  ApiSourceRepository #com.libretv.android.data.repository  SearchRepository #com.libretv.android.data.repository  VideoRepository #com.libretv.android.data.repository  WatchHistoryRepository #com.libretv.android.data.repository  DatabaseModule com.libretv.android.di  
NetworkModule com.libretv.android.di  MainActivity  com.libretv.android.presentation  HistoryViewModel 0com.libretv.android.presentation.screens.history  
HomeViewModel -com.libretv.android.presentation.screens.home  SearchViewModel /com.libretv.android.presentation.screens.search  SettingsViewModel 1com.libretv.android.presentation.screens.settings  AnnotationRetention kotlin.annotation                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             