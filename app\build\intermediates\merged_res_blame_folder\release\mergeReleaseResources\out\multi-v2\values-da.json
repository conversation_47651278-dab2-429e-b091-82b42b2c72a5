{"logs": [{"outputFile": "com.libretv.android.app-mergeReleaseResources-2:/values-da/values-da.xml", "map": [{"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\0b493fc5385f598ec7f47c0a10bc88bb\\transformed\\material3-release\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,284,391,505,605,700,812,956,1078,1227,1311,1411,1500,1594,1708,1826,1931,2056,2176,2312,2485,2615,2732,2854,2973,3063,3161,3280,3416,3514,3632,3734,3860,3993,4098,4196,4276,4369,4462,4576,4660,4745,4846,4926,5010,5111,5210,5305,5405,5492,5597,5699,5804,5921,6001,6103", "endColumns": "115,112,106,113,99,94,111,143,121,148,83,99,88,93,113,117,104,124,119,135,172,129,116,121,118,89,97,118,135,97,117,101,125,132,104,97,79,92,92,113,83,84,100,79,83,100,98,94,99,86,104,101,104,116,79,101,98", "endOffsets": "166,279,386,500,600,695,807,951,1073,1222,1306,1406,1495,1589,1703,1821,1926,2051,2171,2307,2480,2610,2727,2849,2968,3058,3156,3275,3411,3509,3627,3729,3855,3988,4093,4191,4271,4364,4457,4571,4655,4740,4841,4921,5005,5106,5205,5300,5400,5487,5592,5694,5799,5916,5996,6098,6197"}, "to": {"startLines": "102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7410,7526,7639,7746,7860,7960,8055,8167,8311,8433,8582,8666,8766,8855,8949,9063,9181,9286,9411,9531,9667,9840,9970,10087,10209,10328,10418,10516,10635,10771,10869,10987,11089,11215,11348,11453,11551,11631,11724,11817,11931,12015,12100,12201,12281,12365,12466,12565,12660,12760,12847,12952,13054,13159,13276,13356,13458", "endColumns": "115,112,106,113,99,94,111,143,121,148,83,99,88,93,113,117,104,124,119,135,172,129,116,121,118,89,97,118,135,97,117,101,125,132,104,97,79,92,92,113,83,84,100,79,83,100,98,94,99,86,104,101,104,116,79,101,98", "endOffsets": "7521,7634,7741,7855,7955,8050,8162,8306,8428,8577,8661,8761,8850,8944,9058,9176,9281,9406,9526,9662,9835,9965,10082,10204,10323,10413,10511,10630,10766,10864,10982,11084,11210,11343,11448,11546,11626,11719,11812,11926,12010,12095,12196,12276,12360,12461,12560,12655,12755,12842,12947,13049,13154,13271,13351,13453,13552"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\6b27351f1a96970e2ba142187de324d4\\transformed\\media3-ui-1.7.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,474,664,750,837,912,997,1084,1155,1219,1317,1413,1485,1550,1616,1686,1793,1900,2005,2078,2158,2234,2303,2382,2462,2525,2593,2646,2704,2752,2813,2883,2955,3023,3097,3161,3220,3284,3336,3397,3473,3548,3601", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,85,86,74,84,86,70,63,97,95,71,64,65,69,106,106,104,72,79,75,68,78,79,62,67,52,57,47,60,69,71,67,73,63,58,63,51,60,75,74,52,64", "endOffsets": "280,469,659,745,832,907,992,1079,1150,1214,1312,1408,1480,1545,1611,1681,1788,1895,2000,2073,2153,2229,2298,2377,2457,2520,2588,2641,2699,2747,2808,2878,2950,3018,3092,3156,3215,3279,3331,3392,3468,3543,3596,3661"}, "to": {"startLines": "2,11,15,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,524,3565,3651,3738,3813,3898,3985,4056,4120,4218,4314,4386,4451,4517,4587,4694,4801,4906,4979,5059,5135,5204,5283,5363,5426,6171,6224,6282,6330,6391,6461,6533,6601,6675,6739,6798,6862,6914,6975,7051,7126,7179", "endLines": "10,14,18,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99", "endColumns": "17,12,12,85,86,74,84,86,70,63,97,95,71,64,65,69,106,106,104,72,79,75,68,78,79,62,67,52,57,47,60,69,71,67,73,63,58,63,51,60,75,74,52,64", "endOffsets": "330,519,709,3646,3733,3808,3893,3980,4051,4115,4213,4309,4381,4446,4512,4582,4689,4796,4901,4974,5054,5130,5199,5278,5358,5421,5489,6219,6277,6325,6386,6456,6528,6596,6670,6734,6793,6857,6909,6970,7046,7121,7174,7239"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\4c56c0fb8a9fdb1c153785f7a46f8077\\transformed\\media3-session-1.7.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,226,313,410,505,599,681,769,872,955,1020,1121,1205,1297,1378,1475,1558,1656,1731,1799,1877,1955,2046", "endColumns": "74,95,86,96,94,93,81,87,102,82,64,100,83,91,80,96,82,97,74,67,77,77,90,96", "endOffsets": "125,221,308,405,500,594,676,764,867,950,1015,1116,1200,1292,1373,1470,1553,1651,1726,1794,1872,1950,2041,2138"}, "to": {"startLines": "30,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,159,160,161,162,163,164", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1783,2039,2135,2222,2319,2414,2508,2590,2678,2781,2864,2929,3030,3114,3206,3287,3384,3467,13557,13632,13700,13778,13856,13947", "endColumns": "74,95,86,96,94,93,81,87,102,82,64,100,83,91,80,96,82,97,74,67,77,77,90,96", "endOffsets": "1853,2130,2217,2314,2409,2503,2585,2673,2776,2859,2924,3025,3109,3201,3282,3379,3462,3560,13627,13695,13773,13851,13942,14039"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\955c05c442e591726e4833ad1e0fc303\\transformed\\core-1.16.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,350,448,555,664,782", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "146,248,345,443,550,659,777,878"}, "to": {"startLines": "20,21,22,23,24,25,26,174", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "789,885,987,1084,1182,1289,1398,14758", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "880,982,1079,1177,1284,1393,1511,14854"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\2425a4573e4fb65053b44e46c3806d87\\transformed\\foundation-release\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,130,220", "endColumns": "74,89,86", "endOffsets": "125,215,302"}, "to": {"startLines": "19,178,179", "startColumns": "4,4,4", "startOffsets": "714,15124,15214", "endColumns": "74,89,86", "endOffsets": "784,15209,15296"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\26497276d619c831fa389bb2f1588ec8\\transformed\\media3-exoplayer-1.7.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,189,253,322,399,473,573,664", "endColumns": "71,61,63,68,76,73,99,90,67", "endOffsets": "122,184,248,317,394,468,568,659,727"}, "to": {"startLines": "74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "5494,5566,5628,5692,5761,5838,5912,6012,6103", "endColumns": "71,61,63,68,76,73,99,90,67", "endOffsets": "5561,5623,5687,5756,5833,5907,6007,6098,6166"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\406d1819cde1682167f3af9259cf68c0\\transformed\\ui-release\\res\\values-da\\values-da.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "181,273,353,448,547,629,706,795,884,966,1047,1131,1201,1292,1366,1438,1509,1587,1654", "endColumns": "91,79,94,98,81,76,88,88,81,80,83,69,90,73,71,70,77,66,119", "endOffsets": "268,348,443,542,624,701,790,879,961,1042,1126,1196,1287,1361,1433,1504,1582,1649,1769"}, "to": {"startLines": "27,28,29,31,32,100,101,165,166,167,168,169,170,171,172,173,175,176,177", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1516,1608,1688,1858,1957,7244,7321,14044,14133,14215,14296,14380,14450,14541,14615,14687,14859,14937,15004", "endColumns": "91,79,94,98,81,76,88,88,81,80,83,69,90,73,71,70,77,66,119", "endOffsets": "1603,1683,1778,1952,2034,7316,7405,14128,14210,14291,14375,14445,14536,14610,14682,14753,14932,14999,15119"}}]}]}