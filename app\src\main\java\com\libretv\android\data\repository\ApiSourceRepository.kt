package com.libretv.android.data.repository

import com.libretv.android.data.database.ApiSourceDao
import com.libretv.android.data.model.ApiSource
import com.libretv.android.data.network.NetworkConfig
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import javax.inject.Inject
import javax.inject.Singleton

/**
 * API源数据仓库
 */
@Singleton
class ApiSourceRepository @Inject constructor(
    private val apiSourceDao: ApiSourceDao
) {
    
    /**
     * 获取所有API源
     */
    fun getAllApiSources(): Flow<List<ApiSource>> {
        return apiSourceDao.getAllApiSources()
    }
    
    /**
     * 获取启用的API源
     */
    fun getEnabledApiSources(): Flow<List<ApiSource>> {
        return apiSourceDao.getEnabledApiSources()
    }
    
    /**
     * 获取普通API源（非成人内容）
     */
    fun getNormalApiSources(): Flow<List<ApiSource>> {
        return apiSourceDao.getNormalApiSources()
    }
    
    /**
     * 获取成人API源
     */
    fun getAdultApiSources(): Flow<List<ApiSource>> {
        return apiSourceDao.getAdultApiSources()
    }
    
    /**
     * 获取自定义API源
     */
    fun getCustomApiSources(): Flow<List<ApiSource>> {
        return apiSourceDao.getCustomApiSources()
    }
    
    /**
     * 根据代码获取API源
     */
    suspend fun getApiSourceByCode(code: String): ApiSource? {
        return apiSourceDao.getApiSourceByCode(code)
    }
    
    /**
     * 添加API源
     */
    suspend fun addApiSource(apiSource: ApiSource) {
        apiSourceDao.insertApiSource(apiSource)
    }
    
    /**
     * 更新API源
     */
    suspend fun updateApiSource(apiSource: ApiSource) {
        apiSourceDao.updateApiSource(apiSource)
    }
    
    /**
     * 删除API源
     */
    suspend fun deleteApiSource(apiSource: ApiSource) {
        apiSourceDao.deleteApiSource(apiSource)
    }
    
    /**
     * 更新API源启用状态
     */
    suspend fun updateEnabledStatus(code: String, isEnabled: Boolean) {
        apiSourceDao.updateEnabledStatus(code, isEnabled)
    }
    
    /**
     * 更新API源排序
     */
    suspend fun updateOrder(code: String, order: Int) {
        apiSourceDao.updateOrder(code, order)
    }
    
    /**
     * 初始化默认API源
     */
    suspend fun initializeDefaultSources() {
        val existingSources = apiSourceDao.getAllApiSources().first()
        if (existingSources.isEmpty()) {
            val defaultSources = NetworkConfig.DEFAULT_API_SOURCES.map { (code, config) ->
                ApiSource(
                    code = code,
                    name = config.name,
                    api = config.api,
                    detail = config.detail,
                    isAdult = config.adult,
                    isEnabled = true,
                    isCustom = false,
                    order = 0
                )
            }
            apiSourceDao.insertApiSources(defaultSources)
        }
    }
    
    /**
     * 添加自定义API源
     */
    suspend fun addCustomApiSource(
        name: String,
        api: String,
        detail: String? = null,
        isAdult: Boolean = false
    ): Result<Unit> {
        return try {
            // 生成唯一的代码
            val code = "custom_${System.currentTimeMillis()}"
            
            // 验证API URL格式
            if (!api.startsWith("http")) {
                return Result.failure(Exception("API地址必须以http或https开头"))
            }
            
            val customSource = ApiSource(
                code = code,
                name = name,
                api = api,
                detail = detail,
                isAdult = isAdult,
                isEnabled = true,
                isCustom = true,
                order = 999 // 自定义源排在最后
            )
            
            apiSourceDao.insertApiSource(customSource)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 删除所有自定义源
     */
    suspend fun deleteAllCustomSources() {
        apiSourceDao.deleteAllCustomSources()
    }
    
    /**
     * 重置为默认配置
     */
    suspend fun resetToDefault() {
        // 删除所有自定义源
        deleteAllCustomSources()
        
        // 重新初始化默认源
        initializeDefaultSources()
    }
}
