{"logs": [{"outputFile": "com.libretv.android.app-mergeReleaseResources-2:/values-vi/values-vi.xml", "map": [{"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\955c05c442e591726e4833ad1e0fc303\\transformed\\core-1.16.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,556,669,785", "endColumns": "96,101,98,99,102,112,115,100", "endOffsets": "147,249,348,448,551,664,780,881"}, "to": {"startLines": "20,21,22,23,24,25,26,174", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "776,873,975,1074,1174,1277,1390,14930", "endColumns": "96,101,98,99,102,112,115,100", "endOffsets": "868,970,1069,1169,1272,1385,1501,15026"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\406d1819cde1682167f3af9259cf68c0\\transformed\\ui-release\\res\\values-vi\\values-vi.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "185,281,367,473,573,665,750,843,937,1018,1108,1199,1271,1358,1434,1511,1587,1664,1730", "endColumns": "95,85,105,99,91,84,92,93,80,89,90,71,86,75,76,75,76,65,113", "endOffsets": "276,362,468,568,660,745,838,932,1013,1103,1194,1266,1353,1429,1506,1582,1659,1725,1839"}, "to": {"startLines": "27,28,29,31,32,100,101,165,166,167,168,169,170,171,172,173,175,176,177", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1506,1602,1688,1866,1966,7410,7495,14186,14280,14361,14451,14542,14614,14701,14777,14854,15031,15108,15174", "endColumns": "95,85,105,99,91,84,92,93,80,89,90,71,86,75,76,75,76,65,113", "endOffsets": "1597,1683,1789,1961,2053,7490,7583,14275,14356,14446,14537,14609,14696,14772,14849,14925,15103,15169,15283"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\6b27351f1a96970e2ba142187de324d4\\transformed\\media3-ui-1.7.1\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,290,473,647,723,798,870,973,1074,1153,1221,1320,1421,1489,1552,1615,1683,1813,1933,2060,2128,2206,2276,2361,2446,2530,2593,2667,2720,2781,2831,2892,2954,3020,3084,3149,3210,3269,3338,3396,3456,3530,3604,3667", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,75,74,71,102,100,78,67,98,100,67,62,62,67,129,119,126,67,77,69,84,84,83,62,73,52,60,49,60,61,65,63,64,60,58,68,57,59,73,73,62,72", "endOffsets": "285,468,642,718,793,865,968,1069,1148,1216,1315,1416,1484,1547,1610,1678,1808,1928,2055,2123,2201,2271,2356,2441,2525,2588,2662,2715,2776,2826,2887,2949,3015,3079,3144,3205,3264,3333,3391,3451,3525,3599,3662,3735"}, "to": {"startLines": "2,11,15,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,340,523,3625,3701,3776,3848,3951,4052,4131,4199,4298,4399,4467,4530,4593,4661,4791,4911,5038,5106,5184,5254,5339,5424,5508,5571,6337,6390,6451,6501,6562,6624,6690,6754,6819,6880,6939,7008,7066,7126,7200,7274,7337", "endLines": "10,14,18,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99", "endColumns": "17,12,12,75,74,71,102,100,78,67,98,100,67,62,62,67,129,119,126,67,77,69,84,84,83,62,73,52,60,49,60,61,65,63,64,60,58,68,57,59,73,73,62,72", "endOffsets": "335,518,692,3696,3771,3843,3946,4047,4126,4194,4293,4394,4462,4525,4588,4656,4786,4906,5033,5101,5179,5249,5334,5419,5503,5566,5640,6385,6446,6496,6557,6619,6685,6749,6814,6875,6934,7003,7061,7121,7195,7269,7332,7405"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\4c56c0fb8a9fdb1c153785f7a46f8077\\transformed\\media3-session-1.7.1\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,227,311,411,499,594,699,784,884,985,1049,1150,1251,1344,1432,1524,1599,1694,1765,1831,1905,1981,2072", "endColumns": "71,99,83,99,87,94,104,84,99,100,63,100,100,92,87,91,74,94,70,65,73,75,90,89", "endOffsets": "122,222,306,406,494,589,694,779,879,980,1044,1145,1246,1339,1427,1519,1594,1689,1760,1826,1900,1976,2067,2157"}, "to": {"startLines": "30,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,159,160,161,162,163,164", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1794,2058,2158,2242,2342,2430,2525,2630,2715,2815,2916,2980,3081,3182,3275,3363,3455,3530,13718,13789,13855,13929,14005,14096", "endColumns": "71,99,83,99,87,94,104,84,99,100,63,100,100,92,87,91,74,94,70,65,73,75,90,89", "endOffsets": "1861,2153,2237,2337,2425,2520,2625,2710,2810,2911,2975,3076,3177,3270,3358,3450,3525,3620,13784,13850,13924,14000,14091,14181"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\26497276d619c831fa389bb2f1588ec8\\transformed\\media3-exoplayer-1.7.1\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,191,259,328,419,489,579,667", "endColumns": "72,62,67,68,90,69,89,87,79", "endOffsets": "123,186,254,323,414,484,574,662,742"}, "to": {"startLines": "74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "5645,5718,5781,5849,5918,6009,6079,6169,6257", "endColumns": "72,62,67,68,90,69,89,87,79", "endOffsets": "5713,5776,5844,5913,6004,6074,6164,6252,6332"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\2425a4573e4fb65053b44e46c3806d87\\transformed\\foundation-release\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,134,222", "endColumns": "78,87,86", "endOffsets": "129,217,304"}, "to": {"startLines": "19,178,179", "startColumns": "4,4,4", "startOffsets": "697,15288,15376", "endColumns": "78,87,86", "endOffsets": "771,15371,15458"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\0b493fc5385f598ec7f47c0a10bc88bb\\transformed\\material3-release\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,289,396,513,614,709,821,958,1078,1219,1303,1406,1495,1591,1710,1833,1941,2068,2191,2318,2477,2604,2727,2847,2966,3056,3156,3274,3407,3502,3608,3715,3838,3968,4076,4172,4251,4348,4444,4555,4644,4728,4835,4915,4998,5097,5195,5290,5389,5475,5576,5674,5776,5892,5972,6081", "endColumns": "118,114,106,116,100,94,111,136,119,140,83,102,88,95,118,122,107,126,122,126,158,126,122,119,118,89,99,117,132,94,105,106,122,129,107,95,78,96,95,110,88,83,106,79,82,98,97,94,98,85,100,97,101,115,79,108,103", "endOffsets": "169,284,391,508,609,704,816,953,1073,1214,1298,1401,1490,1586,1705,1828,1936,2063,2186,2313,2472,2599,2722,2842,2961,3051,3151,3269,3402,3497,3603,3710,3833,3963,4071,4167,4246,4343,4439,4550,4639,4723,4830,4910,4993,5092,5190,5285,5384,5470,5571,5669,5771,5887,5967,6076,6180"}, "to": {"startLines": "102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7588,7707,7822,7929,8046,8147,8242,8354,8491,8611,8752,8836,8939,9028,9124,9243,9366,9474,9601,9724,9851,10010,10137,10260,10380,10499,10589,10689,10807,10940,11035,11141,11248,11371,11501,11609,11705,11784,11881,11977,12088,12177,12261,12368,12448,12531,12630,12728,12823,12922,13008,13109,13207,13309,13425,13505,13614", "endColumns": "118,114,106,116,100,94,111,136,119,140,83,102,88,95,118,122,107,126,122,126,158,126,122,119,118,89,99,117,132,94,105,106,122,129,107,95,78,96,95,110,88,83,106,79,82,98,97,94,98,85,100,97,101,115,79,108,103", "endOffsets": "7702,7817,7924,8041,8142,8237,8349,8486,8606,8747,8831,8934,9023,9119,9238,9361,9469,9596,9719,9846,10005,10132,10255,10375,10494,10584,10684,10802,10935,11030,11136,11243,11366,11496,11604,11700,11779,11876,11972,12083,12172,12256,12363,12443,12526,12625,12723,12818,12917,13003,13104,13202,13304,13420,13500,13609,13713"}}]}]}