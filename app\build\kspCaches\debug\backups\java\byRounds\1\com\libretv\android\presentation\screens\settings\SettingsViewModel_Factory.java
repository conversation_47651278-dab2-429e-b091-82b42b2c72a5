package com.libretv.android.presentation.screens.settings;

import com.libretv.android.data.preferences.SettingsManager;
import com.libretv.android.data.repository.SearchRepository;
import com.libretv.android.data.repository.VideoRepository;
import com.libretv.android.data.repository.WatchHistoryRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class SettingsViewModel_Factory implements Factory<SettingsViewModel> {
  private final Provider<SettingsManager> settingsManagerProvider;

  private final Provider<VideoRepository> videoRepositoryProvider;

  private final Provider<SearchRepository> searchRepositoryProvider;

  private final Provider<WatchHistoryRepository> watchHistoryRepositoryProvider;

  public SettingsViewModel_Factory(Provider<SettingsManager> settingsManagerProvider,
      Provider<VideoRepository> videoRepositoryProvider,
      Provider<SearchRepository> searchRepositoryProvider,
      Provider<WatchHistoryRepository> watchHistoryRepositoryProvider) {
    this.settingsManagerProvider = settingsManagerProvider;
    this.videoRepositoryProvider = videoRepositoryProvider;
    this.searchRepositoryProvider = searchRepositoryProvider;
    this.watchHistoryRepositoryProvider = watchHistoryRepositoryProvider;
  }

  @Override
  public SettingsViewModel get() {
    return newInstance(settingsManagerProvider.get(), videoRepositoryProvider.get(), searchRepositoryProvider.get(), watchHistoryRepositoryProvider.get());
  }

  public static SettingsViewModel_Factory create(Provider<SettingsManager> settingsManagerProvider,
      Provider<VideoRepository> videoRepositoryProvider,
      Provider<SearchRepository> searchRepositoryProvider,
      Provider<WatchHistoryRepository> watchHistoryRepositoryProvider) {
    return new SettingsViewModel_Factory(settingsManagerProvider, videoRepositoryProvider, searchRepositoryProvider, watchHistoryRepositoryProvider);
  }

  public static SettingsViewModel newInstance(SettingsManager settingsManager,
      VideoRepository videoRepository, SearchRepository searchRepository,
      WatchHistoryRepository watchHistoryRepository) {
    return new SettingsViewModel(settingsManager, videoRepository, searchRepository, watchHistoryRepository);
  }
}
