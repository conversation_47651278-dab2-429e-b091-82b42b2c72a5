-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:69:9-77:20
	android:grantUriPermissions
		ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:73:13-47
	android:authorities
		INJECTED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:71:13-64
	android:exported
		ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:72:13-37
	android:name
		ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:70:13-62
manifest
ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:2:1-81:12
INJECTED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:2:1-81:12
INJECTED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:2:1-81:12
INJECTED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:2:1-81:12
MERGED from [com.google.accompanist:accompanist-permissions:0.36.0] D:\SDK\.gradle\caches\8.11.1\transforms\cc2cb17578c159457f7b2d4b9232ff23\transformed\accompanist-permissions-0.36.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.hilt:hilt-navigation-compose:1.2.0] D:\SDK\.gradle\caches\8.11.1\transforms\5985bd07a71b613d691d8d123a05d758\transformed\hilt-navigation-compose-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.hilt:hilt-navigation:1.2.0] D:\SDK\.gradle\caches\8.11.1\transforms\7003edcafc1db97194f17eecf29c3984\transformed\hilt-navigation-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.dagger:hilt-android:2.51.1] D:\SDK\.gradle\caches\8.11.1\transforms\348f86b87e5d9d7294ee368482789d03\transformed\hilt-android-2.51.1\AndroidManifest.xml:16:1-19:12
MERGED from [com.google.accompanist:accompanist-systemuicontroller:0.36.0] D:\SDK\.gradle\caches\8.11.1\transforms\e1279e91368e7e66f15742b998deb888\transformed\accompanist-systemuicontroller-0.36.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.navigation:navigation-runtime-android:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\01dc5809f9f3fc5db6925fb2fcbfb1ca\transformed\navigation-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-android:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\dab4f5d0853d53eccf431f943f79567f\transformed\navigation-common-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-compose-android:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\6cd0c5bde80ab747d68521b09cd24e5c\transformed\navigation-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment:1.5.1] D:\SDK\.gradle\caches\8.11.1\transforms\fc417f0dcf28f5cd08e849c99534415e\transformed\fragment-1.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-ui:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\6b27351f1a96970e2ba142187de324d4\transformed\media3-ui-1.7.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-session:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\4c56c0fb8a9fdb1c153785f7a46f8077\transformed\media3-session-1.7.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-extractor:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\b6c35c4655a652e6ba69dd576291707d\transformed\media3-extractor-1.7.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-container:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\526cde20a819140df353d2d3f0f3361a\transformed\media3-container-1.7.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-datasource:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\1390f49743a25fe6d94fb7342545161b\transformed\media3-datasource-1.7.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-decoder:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\4e23bcdf3c46bd5116ed570a4ed782f6\transformed\media3-decoder-1.7.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-database:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\70fe1dfd252ee8ec52e771d077c7ab5e\transformed\media3-database-1.7.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-common:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\b72cc75137f63a93338edf884253106d\transformed\media3-common-1.7.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-exoplayer-hls:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\c8b9c3f971012f80c285323b6023cc1f\transformed\media3-exoplayer-hls-1.7.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\26497276d619c831fa389bb2f1588ec8\transformed\media3-exoplayer-1.7.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.webkit:webkit:1.13.0] D:\SDK\.gradle\caches\8.11.1\transforms\724fad76f947ec0ad44ed3b605ca6171\transformed\webkit-1.13.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.recyclerview:recyclerview:1.3.0] D:\SDK\.gradle\caches\8.11.1\transforms\09378dcba741b68faa4989a5c91d6bd5\transformed\recyclerview-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media:media:1.7.0] D:\SDK\.gradle\caches\8.11.1\transforms\f3e3f56f2c306141a0feb6c9d27d1c59\transformed\media-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material3:material3-android:1.3.2] D:\SDK\.gradle\caches\8.11.1\transforms\0b493fc5385f598ec7f47c0a10bc88bb\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [io.coil-kt:coil-compose:2.7.0] D:\SDK\.gradle\caches\8.11.1\transforms\bfc65051bc08e535a643cb9fc33369ca\transformed\coil-compose-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-compose-base:2.7.0] D:\SDK\.gradle\caches\8.11.1\transforms\ac016a92d69f89e5c630a7c82441b326\transformed\coil-compose-base-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\3bf0e9913a8fb17f961a2eb6334c8d97\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\6c7764df04e0b2bd0730752b88e2e188\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\70b187968232ecc7b8659c6fe80053ca\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\789d3d6173c0badc34fc3d98b50903ea\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\b61e8830e57a1489b17504c9ec04b5e9\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\2425a4573e4fb65053b44e46c3806d87\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\8190cc1e7bc5bc94d787c73c22ca0668\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\ef68c43c52b3cada58885f55f8ffe7c7\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\ad97e69502215fb0c29b226859151671\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\48a97bdb0df5c58752dd8255a3b8d1d0\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\2ae293dfbd9b42bead7aeec5e2de7593\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\c81dacf843e38c0dfbcb23d834cd118d\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\083c6a44399ed0680db45eea281a3bef\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\abbd8e9524afba48716f5430cec85e10\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.loader:loader:1.0.0] D:\SDK\.gradle\caches\8.11.1\transforms\5550ae8a8200d2af715c6be9143224d4\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] D:\SDK\.gradle\caches\8.11.1\transforms\ee99a8d888896a22624ad6be6b8b1cbd\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] D:\SDK\.gradle\caches\8.11.1\transforms\18231bff7706857c617c62fc8e18ec41\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.autofill:autofill:1.0.0] D:\SDK\.gradle\caches\8.11.1\transforms\93dc1c1ea6d41c7617ce8a3b9d9b7dea\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.graphics:graphics-path:1.0.1] D:\SDK\.gradle\caches\8.11.1\transforms\b8e7be5ea4122c1ea04c523381b9f6f5\transformed\graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil:2.7.0] D:\SDK\.gradle\caches\8.11.1\transforms\95b29f37ca2c262e69837e65662ec5ad\transformed\coil-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-base:2.7.0] D:\SDK\.gradle\caches\8.11.1\transforms\ae738502719aa702fcbb63f9de86724f\transformed\coil-base-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] D:\SDK\.gradle\caches\8.11.1\transforms\b019d1ca88646cc0c0d931e66d340927\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] D:\SDK\.gradle\caches\8.11.1\transforms\59cdc15dce50dfc116303a77e3b71b21\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] D:\SDK\.gradle\caches\8.11.1\transforms\d5110ef54da3db3513c31f9d75a6193f\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\9b142462af8a97f10019a2008da92615\transformed\emoji2-1.4.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\955c05c442e591726e4833ad1e0fc303\transformed\core-1.16.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\3707669c1e417c1f43de410084b2b4ed\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\321c24468f4e838cb4c74bf09a588ece\transformed\lifecycle-livedata-core-ktx-2.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\4b630f5f904e2a49a93895faae1f36a6\transformed\lifecycle-livedata-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\f5179b12cdbb79e921dfb20a3d0ff96e\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\c2e1113140a79fb679da08a5dd3d16df\transformed\lifecycle-viewmodel-2.9.0\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\44c4165ba6d998610d7f987a40811eff\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] D:\SDK\.gradle\caches\8.11.1\transforms\cf78124e35ac9031d7cf108dc980f801\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-compose-android:1.3.0] D:\SDK\.gradle\caches\8.11.1\transforms\e606dea8fe633ef009fa31aa48ffd984\transformed\savedstate-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-android:1.3.0] D:\SDK\.gradle\caches\8.11.1\transforms\7fcb9610de1d6578fc689f0d62ec1e8a\transformed\savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\ecf6ab9b3b3e13c2912b30b1e461ce38\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\abd98ac6293ee055716cc9222dad2f40\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\261d870098534ec2ece245cefc76d24a\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\6eea3a4cd6d218636d040f6f3a8045e0\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\42ebb0b5d26725100a529684ef788d65\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\5dfa590fb18cb4dd61e989e6f16547b4\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] D:\SDK\.gradle\caches\8.11.1\transforms\8e8b716af17a48ee1907330c54e4aa47\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.8] D:\SDK\.gradle\caches\8.11.1\transforms\b5f1b561dbd21c0a994a42954cf70102\transformed\material-icons-extended-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.7.8] D:\SDK\.gradle\caches\8.11.1\transforms\1adbcb0f56cd263c2b52b7bf044645bc\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\406d1819cde1682167f3af9259cf68c0\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\7fc30fe9ba0c67d0c79e5cbdf2ea58c6\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:17:1-29:12
MERGED from [androidx.activity:activity:1.10.1] D:\SDK\.gradle\caches\8.11.1\transforms\e8e3d02eb8e4113eee298889264c6369\transformed\activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.10.1] D:\SDK\.gradle\caches\8.11.1\transforms\1d766e872d0eaef441d33d115aac128f\transformed\activity-ktx-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.10.1] D:\SDK\.gradle\caches\8.11.1\transforms\84f8e81563003932e4c283c58162c76a\transformed\activity-compose-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] D:\SDK\.gradle\caches\8.11.1\transforms\fe28f0070187b5cca4efcaab56677fb2\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\60348645bb2a9696e6863e63533d52cb\transformed\core-ktx-1.16.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.room:room-runtime-android:2.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\97e337fecb0b99825c65abd4fbf1529f\transformed\room-runtime-release\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\7d3d3a165b62359862100901e4fd7150\transformed\room-ktx-2.7.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-core-android:1.1.7] D:\SDK\.gradle\caches\8.11.1\transforms\92c34caaf89edbf2325dcfad6496f5ce\transformed\datastore-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-preferences-core-android:1.1.7] D:\SDK\.gradle\caches\8.11.1\transforms\b261a8f50d7174e3fe130fa4cea3d906\transformed\datastore-preferences-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-android:1.1.7] D:\SDK\.gradle\caches\8.11.1\transforms\ad4964ac6d7b7e560e9cd254c30db483\transformed\datastore-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-preferences-android:1.1.7] D:\SDK\.gradle\caches\8.11.1\transforms\048c82e7f709552935e5d9779ac37c87\transformed\datastore-preferences-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\d075d47f2575f03936e0044a623e9a1a\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\0716be2605c314055d993f64cf03c3b8\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-splashscreen:1.0.1] D:\SDK\.gradle\caches\8.11.1\transforms\80630b05ea7fb0c577cb3dfcc6eeb05c\transformed\core-splashscreen-1.0.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] D:\SDK\.gradle\caches\8.11.1\transforms\ce94e3873b30f034810697cfa145c180\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] D:\SDK\.gradle\caches\8.11.1\transforms\1fa1c77ec5ce78b692b7b2587ffef552\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.sqlite:sqlite-framework-android:2.5.0] D:\SDK\.gradle\caches\8.11.1\transforms\38826b023e6728b30ea72114e2d23c20\transformed\sqlite-framework-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-android:2.5.0] D:\SDK\.gradle\caches\8.11.1\transforms\0efaa518be26c1908c44770a4ec891b1\transformed\sqlite-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\SDK\.gradle\caches\8.11.1\transforms\77858e9412f8238d19f589fbbf47cb23\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.interpolator:interpolator:1.0.0] D:\SDK\.gradle\caches\8.11.1\transforms\0231d5e9d647674c89ad8519aff0afea\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\SDK\.gradle\caches\8.11.1\transforms\61e78a9aa048918f3a91576673b36af5\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] D:\SDK\.gradle\caches\8.11.1\transforms\d2a1f2c131f86c497800fc34dca20031\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] D:\SDK\.gradle\caches\8.11.1\transforms\cc3670d63789cc91641fc96b23b7b50b\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.exifinterface:exifinterface:1.3.7] D:\SDK\.gradle\caches\8.11.1\transforms\4f61a40e295383079b01dcda92b65b2e\transformed\exifinterface-1.3.7\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.dagger:dagger-lint-aar:2.51.1] D:\SDK\.gradle\caches\8.11.1\transforms\645b615bf028d29998195e813b93eb79\transformed\dagger-lint-aar-2.51.1\AndroidManifest.xml:16:1-19:12
	package
		INJECTED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:6:5-67
	android:name
		ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:6:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:7:5-79
MERGED from [androidx.media3:media3-common:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\b72cc75137f63a93338edf884253106d\transformed\media3-common-1.7.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-common:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\b72cc75137f63a93338edf884253106d\transformed\media3-common-1.7.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\26497276d619c831fa389bb2f1588ec8\transformed\media3-exoplayer-1.7.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\26497276d619c831fa389bb2f1588ec8\transformed\media3-exoplayer-1.7.1\AndroidManifest.xml:22:5-79
	android:name
		ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:7:22-76
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:8:5-76
	android:name
		ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:8:22-73
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:11:5-12:38
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:12:9-35
	android:name
		ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:11:22-78
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:13:5-14:38
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:14:9-35
	android:name
		ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:13:22-77
uses-permission#android.permission.READ_MEDIA_VIDEO
ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:17:5-18:32
	tools:targetApi
		ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:18:9-29
	android:name
		ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:17:22-72
uses-permission#android.permission.READ_MEDIA_AUDIO
ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:19:5-20:32
	tools:targetApi
		ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:20:9-29
	android:name
		ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:19:22-72
uses-permission#android.permission.WAKE_LOCK
ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:23:5-68
	android:name
		ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:23:22-65
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:26:5-77
	android:name
		ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:26:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK
ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:27:5-92
	android:name
		ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:27:22-89
application
ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:29:5-79:19
INJECTED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:29:5-79:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\083c6a44399ed0680db45eea281a3bef\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\083c6a44399ed0680db45eea281a3bef\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\9b142462af8a97f10019a2008da92615\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\9b142462af8a97f10019a2008da92615\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\955c05c442e591726e4833ad1e0fc303\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\955c05c442e591726e4833ad1e0fc303\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\3707669c1e417c1f43de410084b2b4ed\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\3707669c1e417c1f43de410084b2b4ed\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\7fc30fe9ba0c67d0c79e5cbdf2ea58c6\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:22:5-27:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\7fc30fe9ba0c67d0c79e5cbdf2ea58c6\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:22:5-27:19
MERGED from [androidx.room:room-runtime-android:2.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\97e337fecb0b99825c65abd4fbf1529f\transformed\room-runtime-release\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime-android:2.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\97e337fecb0b99825c65abd4fbf1529f\transformed\room-runtime-release\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\SDK\.gradle\caches\8.11.1\transforms\77858e9412f8238d19f589fbbf47cb23\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\SDK\.gradle\caches\8.11.1\transforms\77858e9412f8238d19f589fbbf47cb23\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\SDK\.gradle\caches\8.11.1\transforms\61e78a9aa048918f3a91576673b36af5\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\SDK\.gradle\caches\8.11.1\transforms\61e78a9aa048918f3a91576673b36af5\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml
	android:requestLegacyExternalStorage
		ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:40:9-52
	android:roundIcon
		ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:36:9-54
	android:icon
		ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:34:9-43
	android:appComponentFactory
		ADDED from [androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\955c05c442e591726e4833ad1e0fc303\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:37:9-35
	android:label
		ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:35:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:33:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:41:9-29
	android:allowBackup
		ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:31:9-35
	android:theme
		ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:38:9-45
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:32:9-65
	android:usesCleartextTraffic
		ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:39:9-44
	android:name
		ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:30:9-43
activity#com.libretv.android.presentation.MainActivity
ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:44:9-64:20
	android:screenOrientation
		ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:49:13-49
	android:label
		ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:47:13-45
	android:launchMode
		ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:50:13-43
	android:exported
		ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:46:13-36
	android:theme
		ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:48:13-49
	android:name
		ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:45:13-54
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:51:13-54:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:52:17-69
	android:name
		ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:52:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:53:17-77
	android:name
		ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:53:27-74
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:*+data:host:*+data:pathPattern:.*\\.m3u8+data:pathPattern:.*\\.m3u8+data:scheme:http+data:scheme:https
ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:57:13-63:29
	android:autoVerify
		ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:57:28-53
action#android.intent.action.VIEW
ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:58:17-69
	android:name
		ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:58:25-66
category#android.intent.category.DEFAULT
ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:59:17-76
	android:name
		ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:59:27-73
category#android.intent.category.BROWSABLE
ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:60:17-78
	android:name
		ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:60:27-75
data
ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:61:17-96
	android:host
		ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:61:45-61
	android:scheme
		ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:61:23-44
	android:pathPattern
		ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:61:62-93
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:74:13-76:54
	android:resource
		ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:76:17-51
	android:name
		ADDED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml:75:17-67
uses-sdk
INJECTED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml
MERGED from [com.google.accompanist:accompanist-permissions:0.36.0] D:\SDK\.gradle\caches\8.11.1\transforms\cc2cb17578c159457f7b2d4b9232ff23\transformed\accompanist-permissions-0.36.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-permissions:0.36.0] D:\SDK\.gradle\caches\8.11.1\transforms\cc2cb17578c159457f7b2d4b9232ff23\transformed\accompanist-permissions-0.36.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.hilt:hilt-navigation-compose:1.2.0] D:\SDK\.gradle\caches\8.11.1\transforms\5985bd07a71b613d691d8d123a05d758\transformed\hilt-navigation-compose-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation-compose:1.2.0] D:\SDK\.gradle\caches\8.11.1\transforms\5985bd07a71b613d691d8d123a05d758\transformed\hilt-navigation-compose-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.2.0] D:\SDK\.gradle\caches\8.11.1\transforms\7003edcafc1db97194f17eecf29c3984\transformed\hilt-navigation-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.2.0] D:\SDK\.gradle\caches\8.11.1\transforms\7003edcafc1db97194f17eecf29c3984\transformed\hilt-navigation-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.dagger:hilt-android:2.51.1] D:\SDK\.gradle\caches\8.11.1\transforms\348f86b87e5d9d7294ee368482789d03\transformed\hilt-android-2.51.1\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:hilt-android:2.51.1] D:\SDK\.gradle\caches\8.11.1\transforms\348f86b87e5d9d7294ee368482789d03\transformed\hilt-android-2.51.1\AndroidManifest.xml:18:3-42
MERGED from [com.google.accompanist:accompanist-systemuicontroller:0.36.0] D:\SDK\.gradle\caches\8.11.1\transforms\e1279e91368e7e66f15742b998deb888\transformed\accompanist-systemuicontroller-0.36.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-systemuicontroller:0.36.0] D:\SDK\.gradle\caches\8.11.1\transforms\e1279e91368e7e66f15742b998deb888\transformed\accompanist-systemuicontroller-0.36.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.navigation:navigation-runtime-android:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\01dc5809f9f3fc5db6925fb2fcbfb1ca\transformed\navigation-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-android:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\01dc5809f9f3fc5db6925fb2fcbfb1ca\transformed\navigation-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-android:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\dab4f5d0853d53eccf431f943f79567f\transformed\navigation-common-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-android:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\dab4f5d0853d53eccf431f943f79567f\transformed\navigation-common-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-compose-android:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\6cd0c5bde80ab747d68521b09cd24e5c\transformed\navigation-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose-android:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\6cd0c5bde80ab747d68521b09cd24e5c\transformed\navigation-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.5.1] D:\SDK\.gradle\caches\8.11.1\transforms\fc417f0dcf28f5cd08e849c99534415e\transformed\fragment-1.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.1] D:\SDK\.gradle\caches\8.11.1\transforms\fc417f0dcf28f5cd08e849c99534415e\transformed\fragment-1.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media3:media3-ui:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\6b27351f1a96970e2ba142187de324d4\transformed\media3-ui-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-ui:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\6b27351f1a96970e2ba142187de324d4\transformed\media3-ui-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-session:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\4c56c0fb8a9fdb1c153785f7a46f8077\transformed\media3-session-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-session:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\4c56c0fb8a9fdb1c153785f7a46f8077\transformed\media3-session-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\b6c35c4655a652e6ba69dd576291707d\transformed\media3-extractor-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\b6c35c4655a652e6ba69dd576291707d\transformed\media3-extractor-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\526cde20a819140df353d2d3f0f3361a\transformed\media3-container-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\526cde20a819140df353d2d3f0f3361a\transformed\media3-container-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\1390f49743a25fe6d94fb7342545161b\transformed\media3-datasource-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\1390f49743a25fe6d94fb7342545161b\transformed\media3-datasource-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\4e23bcdf3c46bd5116ed570a4ed782f6\transformed\media3-decoder-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\4e23bcdf3c46bd5116ed570a4ed782f6\transformed\media3-decoder-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\70fe1dfd252ee8ec52e771d077c7ab5e\transformed\media3-database-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\70fe1dfd252ee8ec52e771d077c7ab5e\transformed\media3-database-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\b72cc75137f63a93338edf884253106d\transformed\media3-common-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\b72cc75137f63a93338edf884253106d\transformed\media3-common-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-hls:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\c8b9c3f971012f80c285323b6023cc1f\transformed\media3-exoplayer-hls-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-hls:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\c8b9c3f971012f80c285323b6023cc1f\transformed\media3-exoplayer-hls-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\26497276d619c831fa389bb2f1588ec8\transformed\media3-exoplayer-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\26497276d619c831fa389bb2f1588ec8\transformed\media3-exoplayer-1.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.webkit:webkit:1.13.0] D:\SDK\.gradle\caches\8.11.1\transforms\724fad76f947ec0ad44ed3b605ca6171\transformed\webkit-1.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.13.0] D:\SDK\.gradle\caches\8.11.1\transforms\724fad76f947ec0ad44ed3b605ca6171\transformed\webkit-1.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.0] D:\SDK\.gradle\caches\8.11.1\transforms\09378dcba741b68faa4989a5c91d6bd5\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.0] D:\SDK\.gradle\caches\8.11.1\transforms\09378dcba741b68faa4989a5c91d6bd5\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.7.0] D:\SDK\.gradle\caches\8.11.1\transforms\f3e3f56f2c306141a0feb6c9d27d1c59\transformed\media-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.media:media:1.7.0] D:\SDK\.gradle\caches\8.11.1\transforms\f3e3f56f2c306141a0feb6c9d27d1c59\transformed\media-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.2] D:\SDK\.gradle\caches\8.11.1\transforms\0b493fc5385f598ec7f47c0a10bc88bb\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.2] D:\SDK\.gradle\caches\8.11.1\transforms\0b493fc5385f598ec7f47c0a10bc88bb\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [io.coil-kt:coil-compose:2.7.0] D:\SDK\.gradle\caches\8.11.1\transforms\bfc65051bc08e535a643cb9fc33369ca\transformed\coil-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose:2.7.0] D:\SDK\.gradle\caches\8.11.1\transforms\bfc65051bc08e535a643cb9fc33369ca\transformed\coil-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose-base:2.7.0] D:\SDK\.gradle\caches\8.11.1\transforms\ac016a92d69f89e5c630a7c82441b326\transformed\coil-compose-base-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose-base:2.7.0] D:\SDK\.gradle\caches\8.11.1\transforms\ac016a92d69f89e5c630a7c82441b326\transformed\coil-compose-base-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\3bf0e9913a8fb17f961a2eb6334c8d97\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\3bf0e9913a8fb17f961a2eb6334c8d97\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\6c7764df04e0b2bd0730752b88e2e188\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\6c7764df04e0b2bd0730752b88e2e188\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\70b187968232ecc7b8659c6fe80053ca\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\70b187968232ecc7b8659c6fe80053ca\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\789d3d6173c0badc34fc3d98b50903ea\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\789d3d6173c0badc34fc3d98b50903ea\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\b61e8830e57a1489b17504c9ec04b5e9\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\b61e8830e57a1489b17504c9ec04b5e9\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\2425a4573e4fb65053b44e46c3806d87\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\2425a4573e4fb65053b44e46c3806d87\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\8190cc1e7bc5bc94d787c73c22ca0668\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\8190cc1e7bc5bc94d787c73c22ca0668\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\ef68c43c52b3cada58885f55f8ffe7c7\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\ef68c43c52b3cada58885f55f8ffe7c7\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\ad97e69502215fb0c29b226859151671\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\ad97e69502215fb0c29b226859151671\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\48a97bdb0df5c58752dd8255a3b8d1d0\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\48a97bdb0df5c58752dd8255a3b8d1d0\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\2ae293dfbd9b42bead7aeec5e2de7593\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\2ae293dfbd9b42bead7aeec5e2de7593\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\c81dacf843e38c0dfbcb23d834cd118d\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\c81dacf843e38c0dfbcb23d834cd118d\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\083c6a44399ed0680db45eea281a3bef\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\083c6a44399ed0680db45eea281a3bef\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\abbd8e9524afba48716f5430cec85e10\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\abbd8e9524afba48716f5430cec85e10\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.loader:loader:1.0.0] D:\SDK\.gradle\caches\8.11.1\transforms\5550ae8a8200d2af715c6be9143224d4\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] D:\SDK\.gradle\caches\8.11.1\transforms\5550ae8a8200d2af715c6be9143224d4\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] D:\SDK\.gradle\caches\8.11.1\transforms\ee99a8d888896a22624ad6be6b8b1cbd\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] D:\SDK\.gradle\caches\8.11.1\transforms\ee99a8d888896a22624ad6be6b8b1cbd\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] D:\SDK\.gradle\caches\8.11.1\transforms\18231bff7706857c617c62fc8e18ec41\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] D:\SDK\.gradle\caches\8.11.1\transforms\18231bff7706857c617c62fc8e18ec41\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.0.0] D:\SDK\.gradle\caches\8.11.1\transforms\93dc1c1ea6d41c7617ce8a3b9d9b7dea\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] D:\SDK\.gradle\caches\8.11.1\transforms\93dc1c1ea6d41c7617ce8a3b9d9b7dea\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.graphics:graphics-path:1.0.1] D:\SDK\.gradle\caches\8.11.1\transforms\b8e7be5ea4122c1ea04c523381b9f6f5\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] D:\SDK\.gradle\caches\8.11.1\transforms\b8e7be5ea4122c1ea04c523381b9f6f5\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil:2.7.0] D:\SDK\.gradle\caches\8.11.1\transforms\95b29f37ca2c262e69837e65662ec5ad\transformed\coil-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil:2.7.0] D:\SDK\.gradle\caches\8.11.1\transforms\95b29f37ca2c262e69837e65662ec5ad\transformed\coil-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-base:2.7.0] D:\SDK\.gradle\caches\8.11.1\transforms\ae738502719aa702fcbb63f9de86724f\transformed\coil-base-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-base:2.7.0] D:\SDK\.gradle\caches\8.11.1\transforms\ae738502719aa702fcbb63f9de86724f\transformed\coil-base-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] D:\SDK\.gradle\caches\8.11.1\transforms\b019d1ca88646cc0c0d931e66d340927\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] D:\SDK\.gradle\caches\8.11.1\transforms\b019d1ca88646cc0c0d931e66d340927\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] D:\SDK\.gradle\caches\8.11.1\transforms\59cdc15dce50dfc116303a77e3b71b21\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] D:\SDK\.gradle\caches\8.11.1\transforms\59cdc15dce50dfc116303a77e3b71b21\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] D:\SDK\.gradle\caches\8.11.1\transforms\d5110ef54da3db3513c31f9d75a6193f\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] D:\SDK\.gradle\caches\8.11.1\transforms\d5110ef54da3db3513c31f9d75a6193f\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\9b142462af8a97f10019a2008da92615\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\9b142462af8a97f10019a2008da92615\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\955c05c442e591726e4833ad1e0fc303\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\955c05c442e591726e4833ad1e0fc303\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\3707669c1e417c1f43de410084b2b4ed\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\3707669c1e417c1f43de410084b2b4ed\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\321c24468f4e838cb4c74bf09a588ece\transformed\lifecycle-livedata-core-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\321c24468f4e838cb4c74bf09a588ece\transformed\lifecycle-livedata-core-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\4b630f5f904e2a49a93895faae1f36a6\transformed\lifecycle-livedata-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\4b630f5f904e2a49a93895faae1f36a6\transformed\lifecycle-livedata-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\f5179b12cdbb79e921dfb20a3d0ff96e\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\f5179b12cdbb79e921dfb20a3d0ff96e\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\c2e1113140a79fb679da08a5dd3d16df\transformed\lifecycle-viewmodel-2.9.0\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\c2e1113140a79fb679da08a5dd3d16df\transformed\lifecycle-viewmodel-2.9.0\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\44c4165ba6d998610d7f987a40811eff\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\44c4165ba6d998610d7f987a40811eff\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] D:\SDK\.gradle\caches\8.11.1\transforms\cf78124e35ac9031d7cf108dc980f801\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] D:\SDK\.gradle\caches\8.11.1\transforms\cf78124e35ac9031d7cf108dc980f801\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-compose-android:1.3.0] D:\SDK\.gradle\caches\8.11.1\transforms\e606dea8fe633ef009fa31aa48ffd984\transformed\savedstate-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-compose-android:1.3.0] D:\SDK\.gradle\caches\8.11.1\transforms\e606dea8fe633ef009fa31aa48ffd984\transformed\savedstate-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] D:\SDK\.gradle\caches\8.11.1\transforms\7fcb9610de1d6578fc689f0d62ec1e8a\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] D:\SDK\.gradle\caches\8.11.1\transforms\7fcb9610de1d6578fc689f0d62ec1e8a\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\ecf6ab9b3b3e13c2912b30b1e461ce38\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\ecf6ab9b3b3e13c2912b30b1e461ce38\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\abd98ac6293ee055716cc9222dad2f40\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\abd98ac6293ee055716cc9222dad2f40\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\261d870098534ec2ece245cefc76d24a\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\261d870098534ec2ece245cefc76d24a\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\6eea3a4cd6d218636d040f6f3a8045e0\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\6eea3a4cd6d218636d040f6f3a8045e0\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\42ebb0b5d26725100a529684ef788d65\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\42ebb0b5d26725100a529684ef788d65\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\5dfa590fb18cb4dd61e989e6f16547b4\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\5dfa590fb18cb4dd61e989e6f16547b4\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] D:\SDK\.gradle\caches\8.11.1\transforms\8e8b716af17a48ee1907330c54e4aa47\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] D:\SDK\.gradle\caches\8.11.1\transforms\8e8b716af17a48ee1907330c54e4aa47\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.8] D:\SDK\.gradle\caches\8.11.1\transforms\b5f1b561dbd21c0a994a42954cf70102\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.8] D:\SDK\.gradle\caches\8.11.1\transforms\b5f1b561dbd21c0a994a42954cf70102\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.8] D:\SDK\.gradle\caches\8.11.1\transforms\1adbcb0f56cd263c2b52b7bf044645bc\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.8] D:\SDK\.gradle\caches\8.11.1\transforms\1adbcb0f56cd263c2b52b7bf044645bc\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\406d1819cde1682167f3af9259cf68c0\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\406d1819cde1682167f3af9259cf68c0\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\7fc30fe9ba0c67d0c79e5cbdf2ea58c6\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\7fc30fe9ba0c67d0c79e5cbdf2ea58c6\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] D:\SDK\.gradle\caches\8.11.1\transforms\e8e3d02eb8e4113eee298889264c6369\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] D:\SDK\.gradle\caches\8.11.1\transforms\e8e3d02eb8e4113eee298889264c6369\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] D:\SDK\.gradle\caches\8.11.1\transforms\1d766e872d0eaef441d33d115aac128f\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] D:\SDK\.gradle\caches\8.11.1\transforms\1d766e872d0eaef441d33d115aac128f\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] D:\SDK\.gradle\caches\8.11.1\transforms\84f8e81563003932e4c283c58162c76a\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] D:\SDK\.gradle\caches\8.11.1\transforms\84f8e81563003932e4c283c58162c76a\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] D:\SDK\.gradle\caches\8.11.1\transforms\fe28f0070187b5cca4efcaab56677fb2\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] D:\SDK\.gradle\caches\8.11.1\transforms\fe28f0070187b5cca4efcaab56677fb2\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\60348645bb2a9696e6863e63533d52cb\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\60348645bb2a9696e6863e63533d52cb\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.room:room-runtime-android:2.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\97e337fecb0b99825c65abd4fbf1529f\transformed\room-runtime-release\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime-android:2.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\97e337fecb0b99825c65abd4fbf1529f\transformed\room-runtime-release\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\7d3d3a165b62359862100901e4fd7150\transformed\room-ktx-2.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\7d3d3a165b62359862100901e4fd7150\transformed\room-ktx-2.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.7] D:\SDK\.gradle\caches\8.11.1\transforms\92c34caaf89edbf2325dcfad6496f5ce\transformed\datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.7] D:\SDK\.gradle\caches\8.11.1\transforms\92c34caaf89edbf2325dcfad6496f5ce\transformed\datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-preferences-core-android:1.1.7] D:\SDK\.gradle\caches\8.11.1\transforms\b261a8f50d7174e3fe130fa4cea3d906\transformed\datastore-preferences-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-preferences-core-android:1.1.7] D:\SDK\.gradle\caches\8.11.1\transforms\b261a8f50d7174e3fe130fa4cea3d906\transformed\datastore-preferences-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-android:1.1.7] D:\SDK\.gradle\caches\8.11.1\transforms\ad4964ac6d7b7e560e9cd254c30db483\transformed\datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.7] D:\SDK\.gradle\caches\8.11.1\transforms\ad4964ac6d7b7e560e9cd254c30db483\transformed\datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.7] D:\SDK\.gradle\caches\8.11.1\transforms\048c82e7f709552935e5d9779ac37c87\transformed\datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.7] D:\SDK\.gradle\caches\8.11.1\transforms\048c82e7f709552935e5d9779ac37c87\transformed\datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\d075d47f2575f03936e0044a623e9a1a\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\d075d47f2575f03936e0044a623e9a1a\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\0716be2605c314055d993f64cf03c3b8\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\0716be2605c314055d993f64cf03c3b8\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-splashscreen:1.0.1] D:\SDK\.gradle\caches\8.11.1\transforms\80630b05ea7fb0c577cb3dfcc6eeb05c\transformed\core-splashscreen-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-splashscreen:1.0.1] D:\SDK\.gradle\caches\8.11.1\transforms\80630b05ea7fb0c577cb3dfcc6eeb05c\transformed\core-splashscreen-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.annotation:annotation-experimental:1.4.1] D:\SDK\.gradle\caches\8.11.1\transforms\ce94e3873b30f034810697cfa145c180\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] D:\SDK\.gradle\caches\8.11.1\transforms\ce94e3873b30f034810697cfa145c180\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] D:\SDK\.gradle\caches\8.11.1\transforms\1fa1c77ec5ce78b692b7b2587ffef552\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] D:\SDK\.gradle\caches\8.11.1\transforms\1fa1c77ec5ce78b692b7b2587ffef552\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.sqlite:sqlite-framework-android:2.5.0] D:\SDK\.gradle\caches\8.11.1\transforms\38826b023e6728b30ea72114e2d23c20\transformed\sqlite-framework-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework-android:2.5.0] D:\SDK\.gradle\caches\8.11.1\transforms\38826b023e6728b30ea72114e2d23c20\transformed\sqlite-framework-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-android:2.5.0] D:\SDK\.gradle\caches\8.11.1\transforms\0efaa518be26c1908c44770a4ec891b1\transformed\sqlite-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-android:2.5.0] D:\SDK\.gradle\caches\8.11.1\transforms\0efaa518be26c1908c44770a4ec891b1\transformed\sqlite-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\SDK\.gradle\caches\8.11.1\transforms\77858e9412f8238d19f589fbbf47cb23\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\SDK\.gradle\caches\8.11.1\transforms\77858e9412f8238d19f589fbbf47cb23\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] D:\SDK\.gradle\caches\8.11.1\transforms\0231d5e9d647674c89ad8519aff0afea\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] D:\SDK\.gradle\caches\8.11.1\transforms\0231d5e9d647674c89ad8519aff0afea\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\SDK\.gradle\caches\8.11.1\transforms\61e78a9aa048918f3a91576673b36af5\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\SDK\.gradle\caches\8.11.1\transforms\61e78a9aa048918f3a91576673b36af5\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] D:\SDK\.gradle\caches\8.11.1\transforms\d2a1f2c131f86c497800fc34dca20031\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] D:\SDK\.gradle\caches\8.11.1\transforms\d2a1f2c131f86c497800fc34dca20031\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] D:\SDK\.gradle\caches\8.11.1\transforms\cc3670d63789cc91641fc96b23b7b50b\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] D:\SDK\.gradle\caches\8.11.1\transforms\cc3670d63789cc91641fc96b23b7b50b\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.7] D:\SDK\.gradle\caches\8.11.1\transforms\4f61a40e295383079b01dcda92b65b2e\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] D:\SDK\.gradle\caches\8.11.1\transforms\4f61a40e295383079b01dcda92b65b2e\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.dagger:dagger-lint-aar:2.51.1] D:\SDK\.gradle\caches\8.11.1\transforms\645b615bf028d29998195e813b93eb79\transformed\dagger-lint-aar-2.51.1\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:dagger-lint-aar:2.51.1] D:\SDK\.gradle\caches\8.11.1\transforms\645b615bf028d29998195e813b93eb79\transformed\dagger-lint-aar-2.51.1\AndroidManifest.xml:18:3-42
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\StudioProject\shipin\app\src\main\AndroidManifest.xml
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\083c6a44399ed0680db45eea281a3bef\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\083c6a44399ed0680db45eea281a3bef\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\083c6a44399ed0680db45eea281a3bef\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\9b142462af8a97f10019a2008da92615\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\3707669c1e417c1f43de410084b2b4ed\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\3707669c1e417c1f43de410084b2b4ed\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\SDK\.gradle\caches\8.11.1\transforms\61e78a9aa048918f3a91576673b36af5\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\SDK\.gradle\caches\8.11.1\transforms\61e78a9aa048918f3a91576673b36af5\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\9b142462af8a97f10019a2008da92615\transformed\emoji2-1.4.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\9b142462af8a97f10019a2008da92615\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\9b142462af8a97f10019a2008da92615\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\9b142462af8a97f10019a2008da92615\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\9b142462af8a97f10019a2008da92615\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\9b142462af8a97f10019a2008da92615\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\9b142462af8a97f10019a2008da92615\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\955c05c442e591726e4833ad1e0fc303\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\955c05c442e591726e4833ad1e0fc303\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\955c05c442e591726e4833ad1e0fc303\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
permission#com.libretv.android.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\955c05c442e591726e4833ad1e0fc303\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\955c05c442e591726e4833ad1e0fc303\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\955c05c442e591726e4833ad1e0fc303\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\955c05c442e591726e4833ad1e0fc303\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\955c05c442e591726e4833ad1e0fc303\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
uses-permission#com.libretv.android.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\955c05c442e591726e4833ad1e0fc303\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] D:\SDK\.gradle\caches\8.11.1\transforms\955c05c442e591726e4833ad1e0fc303\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\3707669c1e417c1f43de410084b2b4ed\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\3707669c1e417c1f43de410084b2b4ed\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\SDK\.gradle\caches\8.11.1\transforms\3707669c1e417c1f43de410084b2b4ed\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\7fc30fe9ba0c67d0c79e5cbdf2ea58c6\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:23:9-26:79
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\7fc30fe9ba0c67d0c79e5cbdf2ea58c6\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:25:13-36
	android:theme
		ADDED from [androidx.compose.ui:ui-test-manifest:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\7fc30fe9ba0c67d0c79e5cbdf2ea58c6\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:26:13-76
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.8.2] D:\SDK\.gradle\caches\8.11.1\transforms\7fc30fe9ba0c67d0c79e5cbdf2ea58c6\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:24:13-63
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime-android:2.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\97e337fecb0b99825c65abd4fbf1529f\transformed\room-runtime-release\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime-android:2.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\97e337fecb0b99825c65abd4fbf1529f\transformed\room-runtime-release\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime-android:2.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\97e337fecb0b99825c65abd4fbf1529f\transformed\room-runtime-release\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime-android:2.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\97e337fecb0b99825c65abd4fbf1529f\transformed\room-runtime-release\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime-android:2.7.1] D:\SDK\.gradle\caches\8.11.1\transforms\97e337fecb0b99825c65abd4fbf1529f\transformed\room-runtime-release\AndroidManifest.xml:25:13-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\SDK\.gradle\caches\8.11.1\transforms\adfe1f85f2a01b7ea4fd84049ab7bd42\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
