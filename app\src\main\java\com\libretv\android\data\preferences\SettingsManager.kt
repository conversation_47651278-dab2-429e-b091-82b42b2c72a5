package com.libretv.android.data.preferences

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.*
import androidx.datastore.preferences.preferencesDataStore
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 设置管理器
 * 兼容LibreTV的设置功能
 */
@Singleton
class SettingsManager @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    private val Context.dataStore: DataStore<Preferences> by preferencesDataStore(name = "settings")
    
    companion object {
        // 基础设置
        private val PASSWORD_ENABLED = booleanPreferencesKey("password_enabled")
        private val PASSWORD_HASH = stringPreferencesKey("password_hash")
        private val ADULT_CONTENT_ENABLED = booleanPreferencesKey("adult_content_enabled")
        private val AUTO_PLAY_NEXT = booleanPreferencesKey("auto_play_next")
        private val REMEMBER_PROGRESS = booleanPreferencesKey("remember_progress")
        
        // 播放器设置
        private val PLAYER_AUTO_FULLSCREEN = booleanPreferencesKey("player_auto_fullscreen")
        private val PLAYER_VOLUME = floatPreferencesKey("player_volume")
        private val PLAYER_SPEED = floatPreferencesKey("player_speed")
        private val PLAYER_BRIGHTNESS = floatPreferencesKey("player_brightness")
        
        // 网络设置
        private val USE_PROXY = booleanPreferencesKey("use_proxy")
        private val PROXY_URL = stringPreferencesKey("proxy_url")
        private val TIMEOUT_SECONDS = intPreferencesKey("timeout_seconds")
        
        // 界面设置
        private val THEME_MODE = stringPreferencesKey("theme_mode") // "light", "dark", "system"
        private val GRID_COLUMNS = intPreferencesKey("grid_columns")
        private val SHOW_ADULT_WARNING = booleanPreferencesKey("show_adult_warning")
        
        // 缓存设置
        private val CACHE_ENABLED = booleanPreferencesKey("cache_enabled")
        private val CACHE_SIZE_MB = intPreferencesKey("cache_size_mb")
        private val AUTO_CLEAN_CACHE = booleanPreferencesKey("auto_clean_cache")
        
        // 其他设置
        private val FIRST_LAUNCH = booleanPreferencesKey("first_launch")
        private val LAST_VERSION_CODE = intPreferencesKey("last_version_code")
    }
    
    // 基础设置
    val isPasswordEnabled: Flow<Boolean> = context.dataStore.data.map { it[PASSWORD_ENABLED] ?: false }
    val passwordHash: Flow<String> = context.dataStore.data.map { it[PASSWORD_HASH] ?: "" }
    val isAdultContentEnabled: Flow<Boolean> = context.dataStore.data.map { it[ADULT_CONTENT_ENABLED] ?: false }
    val isAutoPlayNext: Flow<Boolean> = context.dataStore.data.map { it[AUTO_PLAY_NEXT] ?: true }
    val isRememberProgress: Flow<Boolean> = context.dataStore.data.map { it[REMEMBER_PROGRESS] ?: true }
    
    // 播放器设置
    val isPlayerAutoFullscreen: Flow<Boolean> = context.dataStore.data.map { it[PLAYER_AUTO_FULLSCREEN] ?: false }
    val playerVolume: Flow<Float> = context.dataStore.data.map { it[PLAYER_VOLUME] ?: 1.0f }
    val playerSpeed: Flow<Float> = context.dataStore.data.map { it[PLAYER_SPEED] ?: 1.0f }
    val playerBrightness: Flow<Float> = context.dataStore.data.map { it[PLAYER_BRIGHTNESS] ?: 0.5f }
    
    // 网络设置
    val isUseProxy: Flow<Boolean> = context.dataStore.data.map { it[USE_PROXY] ?: false }
    val proxyUrl: Flow<String> = context.dataStore.data.map { it[PROXY_URL] ?: "" }
    val timeoutSeconds: Flow<Int> = context.dataStore.data.map { it[TIMEOUT_SECONDS] ?: 30 }
    
    // 界面设置
    val themeMode: Flow<String> = context.dataStore.data.map { it[THEME_MODE] ?: "system" }
    val gridColumns: Flow<Int> = context.dataStore.data.map { it[GRID_COLUMNS] ?: 2 }
    val showAdultWarning: Flow<Boolean> = context.dataStore.data.map { it[SHOW_ADULT_WARNING] ?: true }
    
    // 缓存设置
    val isCacheEnabled: Flow<Boolean> = context.dataStore.data.map { it[CACHE_ENABLED] ?: true }
    val cacheSizeMB: Flow<Int> = context.dataStore.data.map { it[CACHE_SIZE_MB] ?: 500 }
    val isAutoCleanCache: Flow<Boolean> = context.dataStore.data.map { it[AUTO_CLEAN_CACHE] ?: true }
    
    // 其他设置
    val isFirstLaunch: Flow<Boolean> = context.dataStore.data.map { it[FIRST_LAUNCH] ?: true }
    val lastVersionCode: Flow<Int> = context.dataStore.data.map { it[LAST_VERSION_CODE] ?: 0 }
    
    // 设置更新方法
    suspend fun setPasswordEnabled(enabled: Boolean) {
        context.dataStore.edit { it[PASSWORD_ENABLED] = enabled }
    }
    
    suspend fun setPasswordHash(hash: String) {
        context.dataStore.edit { it[PASSWORD_HASH] = hash }
    }
    
    suspend fun setAdultContentEnabled(enabled: Boolean) {
        context.dataStore.edit { it[ADULT_CONTENT_ENABLED] = enabled }
    }
    
    suspend fun setAutoPlayNext(enabled: Boolean) {
        context.dataStore.edit { it[AUTO_PLAY_NEXT] = enabled }
    }
    
    suspend fun setRememberProgress(enabled: Boolean) {
        context.dataStore.edit { it[REMEMBER_PROGRESS] = enabled }
    }
    
    suspend fun setPlayerAutoFullscreen(enabled: Boolean) {
        context.dataStore.edit { it[PLAYER_AUTO_FULLSCREEN] = enabled }
    }
    
    suspend fun setPlayerVolume(volume: Float) {
        context.dataStore.edit { it[PLAYER_VOLUME] = volume }
    }
    
    suspend fun setPlayerSpeed(speed: Float) {
        context.dataStore.edit { it[PLAYER_SPEED] = speed }
    }
    
    suspend fun setPlayerBrightness(brightness: Float) {
        context.dataStore.edit { it[PLAYER_BRIGHTNESS] = brightness }
    }
    
    suspend fun setUseProxy(enabled: Boolean) {
        context.dataStore.edit { it[USE_PROXY] = enabled }
    }
    
    suspend fun setProxyUrl(url: String) {
        context.dataStore.edit { it[PROXY_URL] = url }
    }
    
    suspend fun setTimeoutSeconds(seconds: Int) {
        context.dataStore.edit { it[TIMEOUT_SECONDS] = seconds }
    }
    
    suspend fun setThemeMode(mode: String) {
        context.dataStore.edit { it[THEME_MODE] = mode }
    }
    
    suspend fun setGridColumns(columns: Int) {
        context.dataStore.edit { it[GRID_COLUMNS] = columns }
    }
    
    suspend fun setShowAdultWarning(show: Boolean) {
        context.dataStore.edit { it[SHOW_ADULT_WARNING] = show }
    }
    
    suspend fun setCacheEnabled(enabled: Boolean) {
        context.dataStore.edit { it[CACHE_ENABLED] = enabled }
    }
    
    suspend fun setCacheSizeMB(sizeMB: Int) {
        context.dataStore.edit { it[CACHE_SIZE_MB] = sizeMB }
    }
    
    suspend fun setAutoCleanCache(enabled: Boolean) {
        context.dataStore.edit { it[AUTO_CLEAN_CACHE] = enabled }
    }
    
    suspend fun setFirstLaunch(isFirst: Boolean) {
        context.dataStore.edit { it[FIRST_LAUNCH] = isFirst }
    }
    
    suspend fun setLastVersionCode(versionCode: Int) {
        context.dataStore.edit { it[LAST_VERSION_CODE] = versionCode }
    }
    
    /**
     * 清除所有设置
     */
    suspend fun clearAllSettings() {
        context.dataStore.edit { it.clear() }
    }
}
