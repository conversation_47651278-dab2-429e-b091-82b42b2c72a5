{"logs": [{"outputFile": "com.libretv.android.app-mergeDebugResources-2:/values-b+sr+Latn/values-b+sr+Latn.xml", "map": [{"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\6b27351f1a96970e2ba142187de324d4\\transformed\\media3-ui-1.7.1\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,556,817,899,982,1064,1153,1244,1314,1381,1475,1570,1638,1702,1765,1837,1946,2060,2171,2247,2335,2409,2480,2572,2665,2732,2797,2850,2908,2956,3017,3083,3147,3210,3275,3339,3400,3466,3518,3580,3656,3732,3788", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "17,12,12,81,82,81,88,90,69,66,93,94,67,63,62,71,108,113,110,75,87,73,70,91,92,66,64,52,57,47,60,65,63,62,64,63,60,65,51,61,75,75,55,67", "endOffsets": "282,551,812,894,977,1059,1148,1239,1309,1376,1470,1565,1633,1697,1760,1832,1941,2055,2166,2242,2330,2404,2475,2567,2660,2727,2792,2845,2903,2951,3012,3078,3142,3205,3270,3334,3395,3461,3513,3575,3651,3727,3783,3851"}, "to": {"startLines": "2,11,16,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,337,606,3791,3873,3956,4038,4127,4218,4288,4355,4449,4544,4612,4676,4739,4811,4920,5034,5145,5221,5309,5383,5454,5546,5639,5706,6443,6496,6554,6602,6663,6729,6793,6856,6921,6985,7046,7112,7164,7226,7302,7378,7434", "endLines": "10,15,20,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101", "endColumns": "17,12,12,81,82,81,88,90,69,66,93,94,67,63,62,71,108,113,110,75,87,73,70,91,92,66,64,52,57,47,60,65,63,62,64,63,60,65,51,61,75,75,55,67", "endOffsets": "332,601,862,3868,3951,4033,4122,4213,4283,4350,4444,4539,4607,4671,4734,4806,4915,5029,5140,5216,5304,5378,5449,5541,5634,5701,5766,6491,6549,6597,6658,6724,6788,6851,6916,6980,7041,7107,7159,7221,7297,7373,7429,7497"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\0b493fc5385f598ec7f47c0a10bc88bb\\transformed\\material3-release\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,289,417,534,633,727,838,974,1094,1236,1321,1421,1516,1614,1730,1855,1960,2101,2241,2374,2554,2679,2799,2924,3046,3142,3240,3358,3488,3588,3690,3799,3941,4090,4199,4302,4379,4478,4576,4685,4774,4860,4967,5047,5130,5227,5330,5423,5521,5608,5716,5813,5915,6048,6128,6237", "endColumns": "116,116,127,116,98,93,110,135,119,141,84,99,94,97,115,124,104,140,139,132,179,124,119,124,121,95,97,117,129,99,101,108,141,148,108,102,76,98,97,108,88,85,106,79,82,96,102,92,97,86,107,96,101,132,79,108,98", "endOffsets": "167,284,412,529,628,722,833,969,1089,1231,1316,1416,1511,1609,1725,1850,1955,2096,2236,2369,2549,2674,2794,2919,3041,3137,3235,3353,3483,3583,3685,3794,3936,4085,4194,4297,4374,4473,4571,4680,4769,4855,4962,5042,5125,5222,5325,5418,5516,5603,5711,5808,5910,6043,6123,6232,6331"}, "to": {"startLines": "104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7670,7787,7904,8032,8149,8248,8342,8453,8589,8709,8851,8936,9036,9131,9229,9345,9470,9575,9716,9856,9989,10169,10294,10414,10539,10661,10757,10855,10973,11103,11203,11305,11414,11556,11705,11814,11917,11994,12093,12191,12300,12389,12475,12582,12662,12745,12842,12945,13038,13136,13223,13331,13428,13530,13663,13743,13852", "endColumns": "116,116,127,116,98,93,110,135,119,141,84,99,94,97,115,124,104,140,139,132,179,124,119,124,121,95,97,117,129,99,101,108,141,148,108,102,76,98,97,108,88,85,106,79,82,96,102,92,97,86,107,96,101,132,79,108,98", "endOffsets": "7782,7899,8027,8144,8243,8337,8448,8584,8704,8846,8931,9031,9126,9224,9340,9465,9570,9711,9851,9984,10164,10289,10409,10534,10656,10752,10850,10968,11098,11198,11300,11409,11551,11700,11809,11912,11989,12088,12186,12295,12384,12470,12577,12657,12740,12837,12940,13033,13131,13218,13326,13423,13525,13658,13738,13847,13946"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\2425a4573e4fb65053b44e46c3806d87\\transformed\\foundation-release\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,145,235", "endColumns": "89,89,91", "endOffsets": "140,230,322"}, "to": {"startLines": "21,181,182", "startColumns": "4,4,4", "startOffsets": "867,15656,15746", "endColumns": "89,89,91", "endOffsets": "952,15741,15833"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\955c05c442e591726e4833ad1e0fc303\\transformed\\core-1.16.0\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "22,23,24,25,26,27,28,177", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "957,1055,1157,1254,1358,1462,1567,15288", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "1050,1152,1249,1353,1457,1562,1678,15384"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\26497276d619c831fa389bb2f1588ec8\\transformed\\media3-exoplayer-1.7.1\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,191,256,328,407,480,568,652", "endColumns": "74,60,64,71,78,72,87,83,74", "endOffsets": "125,186,251,323,402,475,563,647,722"}, "to": {"startLines": "76,77,78,79,80,81,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "5771,5846,5907,5972,6044,6123,6196,6284,6368", "endColumns": "74,60,64,71,78,72,87,83,74", "endOffsets": "5841,5902,5967,6039,6118,6191,6279,6363,6438"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\406d1819cde1682167f3af9259cf68c0\\transformed\\ui-release\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "196,293,380,477,578,664,741,832,924,1009,1089,1174,1247,1337,1414,1493,1570,1649,1719", "endColumns": "96,86,96,100,85,76,90,91,84,79,84,72,89,76,78,76,78,69,117", "endOffsets": "288,375,472,573,659,736,827,919,1004,1084,1169,1242,1332,1409,1488,1565,1644,1714,1832"}, "to": {"startLines": "29,30,31,33,34,102,103,168,169,170,171,172,173,174,175,176,178,179,180", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1683,1780,1867,2041,2142,7502,7579,14550,14642,14727,14807,14892,14965,15055,15132,15211,15389,15468,15538", "endColumns": "96,86,96,100,85,76,90,91,84,79,84,72,89,76,78,76,78,69,117", "endOffsets": "1775,1862,1959,2137,2223,7574,7665,14637,14722,14802,14887,14960,15050,15127,15206,15283,15463,15533,15651"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\3bf0e9913a8fb17f961a2eb6334c8d97\\transformed\\material-release\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "93", "endOffsets": "144"}, "to": {"startLines": "161", "startColumns": "4", "startOffsets": "13951", "endColumns": "93", "endOffsets": "14040"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\4c56c0fb8a9fdb1c153785f7a46f8077\\transformed\\media3-session-1.7.1\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,244,319,412,504,602,698,787,894,983,1048,1153,1244,1338,1417,1513,1599,1695,1766,1833,1915,2001,2098", "endColumns": "76,111,74,92,91,97,95,88,106,88,64,104,90,93,78,95,85,95,70,66,81,85,96,101", "endOffsets": "127,239,314,407,499,597,693,782,889,978,1043,1148,1239,1333,1412,1508,1594,1690,1761,1828,1910,1996,2093,2195"}, "to": {"startLines": "32,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,162,163,164,165,166,167", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1964,2228,2340,2415,2508,2600,2698,2794,2883,2990,3079,3144,3249,3340,3434,3513,3609,3695,14045,14116,14183,14265,14351,14448", "endColumns": "76,111,74,92,91,97,95,88,106,88,64,104,90,93,78,95,85,95,70,66,81,85,96,101", "endOffsets": "2036,2335,2410,2503,2595,2693,2789,2878,2985,3074,3139,3244,3335,3429,3508,3604,3690,3786,14111,14178,14260,14346,14443,14545"}}]}]}