package com.libretv.android.presentation.screens.home

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.libretv.android.data.model.WatchHistory
import com.libretv.android.data.network.DoubanSubject
import com.libretv.android.data.repository.VideoRepository
import com.libretv.android.data.repository.WatchHistoryRepository
import com.libretv.android.data.network.DoubanApiService
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 首页ViewModel
 */
@HiltViewModel
class HomeViewModel @Inject constructor(
    private val videoRepository: VideoRepository,
    private val watchHistoryRepository: WatchHistoryRepository,
    private val doubanApiService: DoubanApiService
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(HomeUiState())
    val uiState: StateFlow<HomeUiState> = _uiState.asStateFlow()
    
    init {
        loadHomeData()
    }
    
    private fun loadHomeData() {
        viewModelScope.launch {
            _uiState.update { it.copy(isLoading = true, error = null) }
            
            try {
                // 加载最近观看
                watchHistoryRepository.getRecentWatchHistory(10)
                    .collect { recentWatched ->
                        _uiState.update { 
                            it.copy(recentWatched = recentWatched)
                        }
                    }
                
                // 加载豆瓣推荐
                loadDoubanRecommendations()
                
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(
                        isLoading = false,
                        error = "加载失败: ${e.message}"
                    )
                }
            }
        }
    }
    
    private suspend fun loadDoubanRecommendations() {
        try {
            // 获取豆瓣热门电影
            val moviesResponse = doubanApiService.getHotMovies()
            if (moviesResponse.isSuccessful) {
                val movies = moviesResponse.body()?.subjects ?: emptyList()
                _uiState.update { 
                    it.copy(
                        doubanRecommendations = movies.take(10),
                        isLoading = false
                    )
                }
            }
        } catch (e: Exception) {
            // 豆瓣API失败不影响其他功能
            android.util.Log.w("HomeViewModel", "Failed to load Douban recommendations: ${e.message}")
            _uiState.update { it.copy(isLoading = false) }
        }
    }
    
    fun searchDoubanMovie(title: String) {
        viewModelScope.launch {
            try {
                val result = videoRepository.searchVideos(title)
                result.onSuccess { videos ->
                    // TODO: 导航到搜索结果页面
                    android.util.Log.d("HomeViewModel", "Found ${videos.size} videos for: $title")
                }.onFailure { error ->
                    _uiState.update { 
                        it.copy(error = "搜索失败: ${error.message}")
                    }
                }
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(error = "搜索失败: ${e.message}")
                }
            }
        }
    }
    
    fun refresh() {
        loadHomeData()
    }
    
    fun clearError() {
        _uiState.update { it.copy(error = null) }
    }
}

/**
 * 首页UI状态
 */
data class HomeUiState(
    val isLoading: Boolean = false,
    val recentWatched: List<WatchHistory> = emptyList(),
    val doubanRecommendations: List<DoubanSubject> = emptyList(),
    val error: String? = null
)
