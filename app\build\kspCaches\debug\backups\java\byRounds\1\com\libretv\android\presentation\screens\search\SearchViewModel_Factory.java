package com.libretv.android.presentation.screens.search;

import com.libretv.android.data.repository.SearchRepository;
import com.libretv.android.data.repository.VideoRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast"
})
public final class SearchViewModel_Factory implements Factory<SearchViewModel> {
  private final Provider<VideoRepository> videoRepositoryProvider;

  private final Provider<SearchRepository> searchRepositoryProvider;

  public SearchViewModel_Factory(Provider<VideoRepository> videoRepositoryProvider,
      Provider<SearchRepository> searchRepositoryProvider) {
    this.videoRepositoryProvider = videoRepositoryProvider;
    this.searchRepositoryProvider = searchRepositoryProvider;
  }

  @Override
  public SearchViewModel get() {
    return newInstance(videoRepositoryProvider.get(), searchRepositoryProvider.get());
  }

  public static SearchViewModel_Factory create(Provider<VideoRepository> videoRepositoryProvider,
      Provider<SearchRepository> searchRepositoryProvider) {
    return new SearchViewModel_Factory(videoRepositoryProvider, searchRepositoryProvider);
  }

  public static SearchViewModel newInstance(VideoRepository videoRepository,
      SearchRepository searchRepository) {
    return new SearchViewModel(videoRepository, searchRepository);
  }
}
