package com.libretv.android.data.network

import com.libretv.android.data.model.ApiResponse
import com.libretv.android.data.model.VideoDetailInfo
import com.libretv.android.data.model.VideoInfo
import retrofit2.Response
import retrofit2.http.GET
import retrofit2.http.Query
import retrofit2.http.Url

/**
 * API服务接口
 * 兼容LibreTV的苹果CMS V10 API格式
 */
interface ApiService {
    
    /**
     * 搜索视频
     * @param baseUrl API基础URL
     * @param action 操作类型，固定为"videolist"
     * @param keyword 搜索关键词
     * @param page 页码
     */
    @GET
    suspend fun searchVideos(
        @Url baseUrl: String,
        @Query("ac") action: String = "videolist",
        @Query("wd") keyword: String,
        @Query("pg") page: Int = 1
    ): Response<ApiResponse<VideoInfo>>
    
    /**
     * 获取视频详情
     * @param baseUrl API基础URL
     * @param action 操作类型，固定为"detail"
     * @param ids 视频ID
     */
    @GET
    suspend fun getVideoDetail(
        @Url baseUrl: String,
        @Query("ac") action: String = "detail",
        @Query("ids") ids: String
    ): Response<ApiResponse<VideoInfo>>
    
    /**
     * 代理请求 - 用于处理跨域和m3u8解析
     * @param url 完整的代理URL
     */
    @GET
    suspend fun proxyRequest(@Url url: String): Response<String>
}

/**
 * 内部API服务 - 用于本地代理功能
 */
interface InternalApiService {
    
    /**
     * 聚合搜索
     */
    @GET("/api/search")
    suspend fun aggregatedSearch(
        @Query("wd") keyword: String,
        @Query("source") source: String = "aggregated",
        @Query("customApi") customApi: String? = null
    ): Response<ApiResponse<VideoInfo>>
    
    /**
     * 获取视频详情
     */
    @GET("/api/detail")
    suspend fun getDetail(
        @Query("id") id: String,
        @Query("source") source: String,
        @Query("customApi") customApi: String? = null,
        @Query("customDetail") customDetail: String? = null
    ): Response<ApiResponse<VideoDetailInfo>>
    
    /**
     * 代理请求
     */
    @GET("/proxy/{encodedUrl}")
    suspend fun proxy(@retrofit2.http.Path("encodedUrl") encodedUrl: String): Response<String>
}

/**
 * 豆瓣API服务
 */
interface DoubanApiService {
    
    /**
     * 获取豆瓣热门电影
     */
    @GET("https://movie.douban.com/j/search_subjects")
    suspend fun getHotMovies(
        @Query("type") type: String = "movie",
        @Query("tag") tag: String = "热门",
        @Query("sort") sort: String = "recommend",
        @Query("page_limit") limit: Int = 20,
        @Query("page_start") start: Int = 0
    ): Response<DoubanResponse>
    
    /**
     * 获取豆瓣热门电视剧
     */
    @GET("https://movie.douban.com/j/search_subjects")
    suspend fun getHotTvShows(
        @Query("type") type: String = "tv",
        @Query("tag") tag: String = "热门",
        @Query("sort") sort: String = "recommend",
        @Query("page_limit") limit: Int = 20,
        @Query("page_start") start: Int = 0
    ): Response<DoubanResponse>
}

/**
 * 豆瓣响应数据模型
 */
data class DoubanResponse(
    val subjects: List<DoubanSubject>
)

data class DoubanSubject(
    val id: String,
    val title: String,
    val cover: String,
    val rate: String,
    val url: String,
    val year: String? = null,
    val directors: List<String> = emptyList(),
    val casts: List<String> = emptyList(),
    val genres: List<String> = emptyList()
)
