{"build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\libretv\\android\\presentation\\screens\\home\\HomeViewModel_HiltModules_KeyModule_ProvideFactory.java": ["<init>:com.libretv.android.presentation.screens.home.HomeViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder", "<init>:com.libretv.android.presentation.screens.home.HomeViewModel_HiltModules_KeyModule_ProvideFactory", "provide:com.libretv.android.presentation.screens.home.HomeViewModel_HiltModules_KeyModule_ProvideFactory", "get:com.libretv.android.presentation.screens.home.HomeViewModel_HiltModules_KeyModule_ProvideFactory", "create:com.libretv.android.presentation.screens.home.HomeViewModel_HiltModules_KeyModule_ProvideFactory", "HomeViewModel_HiltModules_KeyModule_ProvideFactory:com.libretv.android.presentation.screens.home"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\presentation\\screens\\settings\\SettingsViewModel_Factory.java": ["create:com.libretv.android.presentation.screens.settings.SettingsViewModel_Factory", "get:com.libretv.android.presentation.screens.settings.SettingsViewModel_Factory", "newInstance:com.libretv.android.presentation.screens.settings.SettingsViewModel_Factory", "SettingsViewModel_Factory:com.libretv.android.presentation.screens.settings", "<init>:com.libretv.android.presentation.screens.settings.SettingsViewModel_Factory"], "src\\main\\java\\com\\libretv\\android\\presentation\\screens\\home\\HomeViewModel.kt": ["refresh:com.libretv.android.presentation.screens.home.HomeViewModel", "clearError:com.libretv.android.presentation.screens.home.HomeViewModel", "HomeUiState:com.libretv.android.presentation.screens.home", "recentWatched:com.libretv.android.presentation.screens.home.HomeUiState", "isLoading:com.libretv.android.presentation.screens.home.HomeUiState", "uiState:com.libretv.android.presentation.screens.home.HomeViewModel", "doubanRecommendations:com.libretv.android.presentation.screens.home.HomeUiState", "searchDoubanMovie:com.libretv.android.presentation.screens.home.HomeViewModel", "error:com.libretv.android.presentation.screens.home.HomeUiState", "HomeViewModel:com.libretv.android.presentation.screens.home"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\di\\NetworkModule_ProvideJsonFactory.java": ["<init>:com.libretv.android.di.NetworkModule_ProvideJsonFactory.InstanceHolder", "get:com.libretv.android.di.NetworkModule_ProvideJsonFactory", "provideJson:com.libretv.android.di.NetworkModule_ProvideJsonFactory", "<init>:com.libretv.android.di.NetworkModule_ProvideJsonFactory", "NetworkModule_ProvideJsonFactory:com.libretv.android.di", "create:com.libretv.android.di.NetworkModule_ProvideJsonFactory"], "src\\main\\java\\com\\example\\shipin\\ui\\theme\\Type.kt": ["Typography:com.example.shipin.ui.theme"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\presentation\\screens\\search\\SearchViewModel_HiltModules.java": ["provide:com.libretv.android.presentation.screens.search.SearchViewModel_HiltModules.KeyModule", "binds:com.libretv.android.presentation.screens.search.SearchViewModel_HiltModules.BindsModule", "BindsModule:com.libretv.android.presentation.screens.search.SearchViewModel_HiltModules", "SearchViewModel_HiltModules:com.libretv.android.presentation.screens.search", "KeyModule:com.libretv.android.presentation.screens.search.SearchViewModel_HiltModules"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\di\\NetworkModule_ProvideApiRetrofitFactory.java": ["get:com.libretv.android.di.NetworkModule_ProvideApiRetrofitFactory", "create:com.libretv.android.di.NetworkModule_ProvideApiRetrofitFactory", "NetworkModule_ProvideApiRetrofitFactory:com.libretv.android.di", "provideApiRetrofit:com.libretv.android.di.NetworkModule_ProvideApiRetrofitFactory", "<init>:com.libretv.android.di.NetworkModule_ProvideApiRetrofitFactory"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\di\\NetworkModule_ProvideDoubanRetrofitFactory.java": ["<init>:com.libretv.android.di.NetworkModule_ProvideDoubanRetrofitFactory", "create:com.libretv.android.di.NetworkModule_ProvideDoubanRetrofitFactory", "NetworkModule_ProvideDoubanRetrofitFactory:com.libretv.android.di", "provideDoubanRetrofit:com.libretv.android.di.NetworkModule_ProvideDoubanRetrofitFactory", "get:com.libretv.android.di.NetworkModule_ProvideDoubanRetrofitFactory"], "src\\main\\java\\com\\libretv\\android\\data\\repository\\ApiSourceRepository.kt": ["updateEnabledStatus:com.libretv.android.data.repository.ApiSourceRepository", "ApiSourceRepository:com.libretv.android.data.repository", "getEnabledApiSources:com.libretv.android.data.repository.ApiSourceRepository", "getAdultApiSources:com.libretv.android.data.repository.ApiSourceRepository", "getAllApiSources:com.libretv.android.data.repository.ApiSourceRepository", "addCustomApiSource:com.libretv.android.data.repository.ApiSourceRepository", "getNormalApiSources:com.libretv.android.data.repository.ApiSourceRepository", "getCustomApiSources:com.libretv.android.data.repository.ApiSourceRepository", "updateApiSource:com.libretv.android.data.repository.ApiSourceRepository", "deleteApiSource:com.libretv.android.data.repository.ApiSourceRepository", "resetToDefault:com.libretv.android.data.repository.ApiSourceRepository", "updateOrder:com.libretv.android.data.repository.ApiSourceRepository", "getApiSourceByCode:com.libretv.android.data.repository.ApiSourceRepository", "initializeDefaultSources:com.libretv.android.data.repository.ApiSourceRepository", "deleteAllCustomSources:com.libretv.android.data.repository.ApiSourceRepository", "addApiSource:com.libretv.android.data.repository.ApiSourceRepository"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_libretv_android_presentation_screens_settings_SettingsViewModel_HiltModules_KeyModule.java": ["<init>:hilt_aggregated_deps._com_libretv_android_presentation_screens_settings_SettingsViewModel_HiltModules_KeyModule", "_com_libretv_android_presentation_screens_settings_SettingsViewModel_HiltModules_KeyModule:hilt_aggregated_deps"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_libretv_android_presentation_screens_search_SearchViewModel_HiltModules_BindsModule.java": ["<init>:hilt_aggregated_deps._com_libretv_android_presentation_screens_search_SearchViewModel_HiltModules_BindsModule", "_com_libretv_android_presentation_screens_search_SearchViewModel_HiltModules_BindsModule:hilt_aggregated_deps"], "src\\main\\java\\com\\libretv\\android\\data\\repository\\WatchHistoryRepository.kt": ["getRecentWatchHistory:com.libretv.android.data.repository.WatchHistoryRepository", "updateCurrentEpisode:com.libretv.android.data.repository.WatchHistoryRepository", "cleanOldHistory:com.libretv.android.data.repository.WatchHistoryRepository", "WatchHistoryRepository:com.libretv.android.data.repository", "getAllWatchHistory:com.libretv.android.data.repository.WatchHistoryRepository", "updateWatchProgress:com.libretv.android.data.repository.WatchHistoryRepository", "deleteWatchHistory:com.libretv.android.data.repository.WatchHistoryRepository", "addOrUpdateWatchHistory:com.libretv.android.data.repository.WatchHistoryRepository", "clearAllHistory:com.libretv.android.data.repository.WatchHistoryRepository", "getWatchHistory:com.libretv.android.data.repository.WatchHistoryRepository"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_libretv_android_presentation_screens_home_HomeViewModel_HiltModules_KeyModule.java": ["<init>:hilt_aggregated_deps._com_libretv_android_presentation_screens_home_HomeViewModel_HiltModules_KeyModule", "_com_libretv_android_presentation_screens_home_HomeViewModel_HiltModules_KeyModule:hilt_aggregated_deps"], "src\\main\\java\\com\\libretv\\android\\data\\database\\ApiSourceDao.kt": ["getAdultApiSources:com.libretv.android.data.database.ApiSourceDao", "updateOrder:com.libretv.android.data.database.ApiSourceDao", "insertApiSource:com.libretv.android.data.database.ApiSourceDao", "getCustomApiSources:com.libretv.android.data.database.ApiSourceDao", "updateEnabledStatus:com.libretv.android.data.database.ApiSourceDao", "insertApiSources:com.libretv.android.data.database.ApiSourceDao", "getEnabledApiSources:com.libretv.android.data.database.ApiSourceDao", "getAllApiSources:com.libretv.android.data.database.ApiSourceDao", "deleteApiSource:com.libretv.android.data.database.ApiSourceDao", "ApiSourceDao:com.libretv.android.data.database", "getApiSourceByCode:com.libretv.android.data.database.ApiSourceDao", "getNormalApiSources:com.libretv.android.data.database.ApiSourceDao", "updateApiSource:com.libretv.android.data.database.ApiSourceDao", "deleteAllCustomSources:com.libretv.android.data.database.ApiSourceDao"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\libretv\\android\\presentation\\screens\\search\\SearchViewModel_HiltModules_KeyModule_ProvideFactory.java": ["<init>:com.libretv.android.presentation.screens.search.SearchViewModel_HiltModules_KeyModule_ProvideFactory", "<init>:com.libretv.android.presentation.screens.search.SearchViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder", "create:com.libretv.android.presentation.screens.search.SearchViewModel_HiltModules_KeyModule_ProvideFactory", "provide:com.libretv.android.presentation.screens.search.SearchViewModel_HiltModules_KeyModule_ProvideFactory", "SearchViewModel_HiltModules_KeyModule_ProvideFactory:com.libretv.android.presentation.screens.search", "get:com.libretv.android.presentation.screens.search.SearchViewModel_HiltModules_KeyModule_ProvideFactory"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\di\\DatabaseModule_ProvideWatchHistoryDaoFactory.java": ["DatabaseModule_ProvideWatchHistoryDaoFactory:com.libretv.android.di", "<init>:com.libretv.android.di.DatabaseModule_ProvideWatchHistoryDaoFactory", "provideWatchHistoryDao:com.libretv.android.di.DatabaseModule_ProvideWatchHistoryDaoFactory", "get:com.libretv.android.di.DatabaseModule_ProvideWatchHistoryDaoFactory", "create:com.libretv.android.di.DatabaseModule_ProvideWatchHistoryDaoFactory"], "src\\main\\java\\com\\example\\shipin\\ui\\theme\\Color.kt": ["Pink80:com.example.shipin.ui.theme", "PurpleGrey80:com.example.shipin.ui.theme", "Purple40:com.example.shipin.ui.theme", "Pink40:com.example.shipin.ui.theme", "Purple80:com.example.shipin.ui.theme", "PurpleGrey40:com.example.shipin.ui.theme"], "src\\main\\java\\com\\libretv\\android\\data\\database\\VideoDao.kt": ["updateWatchProgress:com.libretv.android.data.database.VideoDao", "VideoDao:com.libretv.android.data.database", "updateVideo:com.libretv.android.data.database.VideoDao", "searchVideos:com.libretv.android.data.database.VideoDao", "getVideoById:com.libretv.android.data.database.VideoDao", "deleteVideo:com.libretv.android.data.database.VideoDao", "updateFavoriteStatus:com.libretv.android.data.database.VideoDao", "insertVideo:com.libretv.android.data.database.VideoDao", "getFavoriteVideos:com.libretv.android.data.database.VideoDao", "insertVideos:com.libretv.android.data.database.VideoDao", "deleteOldVideos:com.libretv.android.data.database.VideoDao"], "src\\main\\java\\com\\libretv\\android\\data\\network\\NetworkConfig.kt": ["DEFAULT_API_SOURCES:com.libretv.android.data.network.NetworkConfig", "ApiSourceConfig:com.libretv.android.data.network", "name:com.libretv.android.data.network.ApiSourceConfig", "api:com.libretv.android.data.network.ApiSourceConfig", "CONNECT_TIMEOUT:com.libretv.android.data.network.NetworkConfig", "UserAgentInterceptor:com.libretv.android.data.network", "READ_TIMEOUT:com.libretv.android.data.network.NetworkConfig", "intercept:com.libretv.android.data.network.ErrorHandlingInterceptor", "detail:com.libretv.android.data.network.ApiSourceConfig", "WRITE_TIMEOUT:com.libretv.android.data.network.NetworkConfig", "<init>:com.libretv.android.data.network.ErrorHandlingInterceptor", "PROXY_URL_PREFIX:com.libretv.android.data.network.NetworkConfig", "NetworkConfig:com.libretv.android.data.network", "intercept:com.libretv.android.data.network.UserAgentInterceptor", "<init>:com.libretv.android.data.network.UserAgentInterceptor", "adult:com.libretv.android.data.network.ApiSourceConfig", "<init>:com.libretv.android.data.network.NetworkConfig", "USER_AGENT:com.libretv.android.data.network.NetworkConfig", "ErrorHandlingInterceptor:com.libretv.android.data.network"], "src\\main\\java\\com\\libretv\\android\\presentation\\theme\\LibreTVTheme.kt": ["LibreTVTheme:com.libretv.android.presentation.theme"], "build\\generated\\ksp\\debug\\kotlin\\com\\libretv\\android\\data\\database\\ApiSourceDao_Impl.kt": ["getEnabledApiSources:com.libretv.android.data.database.ApiSourceDao_Impl", "updateEnabledStatus:com.libretv.android.data.database.ApiSourceDao_Impl", "Companion:com.libretv.android.data.database.ApiSourceDao_Impl", "getAllApiSources:com.libretv.android.data.database.ApiSourceDao_Impl", "insertApiSource:com.libretv.android.data.database.ApiSourceDao_Impl", "getCustomApiSources:com.libretv.android.data.database.ApiSourceDao_Impl", "getApiSourceByCode:com.libretv.android.data.database.ApiSourceDao_Impl", "deleteApiSource:com.libretv.android.data.database.ApiSourceDao_Impl", "deleteAllCustomSources:com.libretv.android.data.database.ApiSourceDao_Impl", "<init>:com.libretv.android.data.database.ApiSourceDao_Impl.Companion", "insertApiSources:com.libretv.android.data.database.ApiSourceDao_Impl", "getRequiredConverters:com.libretv.android.data.database.ApiSourceDao_Impl.Companion", "updateOrder:com.libretv.android.data.database.ApiSourceDao_Impl", "ApiSourceDao_Impl:com.libretv.android.data.database", "updateApiSource:com.libretv.android.data.database.ApiSourceDao_Impl", "getNormalApiSources:com.libretv.android.data.database.ApiSourceDao_Impl", "getAdultApiSources:com.libretv.android.data.database.ApiSourceDao_Impl"], "src\\main\\java\\com\\libretv\\android\\di\\DatabaseModule.kt": ["provideWatchHistoryDao:com.libretv.android.di.DatabaseModule", "<init>:com.libretv.android.di.DatabaseModule", "provideLibreTVDatabase:com.libretv.android.di.DatabaseModule", "provideVideoDao:com.libretv.android.di.DatabaseModule", "provideApiSourceDao:com.libretv.android.di.DatabaseModule", "DatabaseModule:com.libretv.android.di", "provideSearchHistoryDao:com.libretv.android.di.DatabaseModule"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\di\\DatabaseModule_ProvideLibreTVDatabaseFactory.java": ["DatabaseModule_ProvideLibreTVDatabaseFactory:com.libretv.android.di", "get:com.libretv.android.di.DatabaseModule_ProvideLibreTVDatabaseFactory", "create:com.libretv.android.di.DatabaseModule_ProvideLibreTVDatabaseFactory", "<init>:com.libretv.android.di.DatabaseModule_ProvideLibreTVDatabaseFactory", "provideLibreTVDatabase:com.libretv.android.di.DatabaseModule_ProvideLibreTVDatabaseFactory"], "src\\main\\java\\com\\libretv\\android\\presentation\\components\\VideoCard.kt": ["VideoCard:com.libretv.android.presentation.components", "DoubanMovieCard:com.libretv.android.presentation.components"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_libretv_android_presentation_screens_history_HistoryViewModel_HiltModules_BindsModule.java": ["<init>:hilt_aggregated_deps._com_libretv_android_presentation_screens_history_HistoryViewModel_HiltModules_BindsModule", "_com_libretv_android_presentation_screens_history_HistoryViewModel_HiltModules_BindsModule:hilt_aggregated_deps"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\presentation\\screens\\search\\SearchViewModel_Factory.java": ["get:com.libretv.android.presentation.screens.search.SearchViewModel_Factory", "create:com.libretv.android.presentation.screens.search.SearchViewModel_Factory", "newInstance:com.libretv.android.presentation.screens.search.SearchViewModel_Factory", "<init>:com.libretv.android.presentation.screens.search.SearchViewModel_Factory", "SearchViewModel_Factory:com.libretv.android.presentation.screens.search"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\di\\NetworkModule_ProvideGsonFactory.java": ["<init>:com.libretv.android.di.NetworkModule_ProvideGsonFactory.InstanceHolder", "provideGson:com.libretv.android.di.NetworkModule_ProvideGsonFactory", "get:com.libretv.android.di.NetworkModule_ProvideGsonFactory", "<init>:com.libretv.android.di.NetworkModule_ProvideGsonFactory", "NetworkModule_ProvideGsonFactory:com.libretv.android.di", "create:com.libretv.android.di.NetworkModule_ProvideGsonFactory"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\di\\DatabaseModule_ProvideApiSourceDaoFactory.java": ["get:com.libretv.android.di.DatabaseModule_ProvideApiSourceDaoFactory", "provideApiSourceDao:com.libretv.android.di.DatabaseModule_ProvideApiSourceDaoFactory", "DatabaseModule_ProvideApiSourceDaoFactory:com.libretv.android.di", "create:com.libretv.android.di.DatabaseModule_ProvideApiSourceDaoFactory", "<init>:com.libretv.android.di.DatabaseModule_ProvideApiSourceDaoFactory"], "src\\main\\java\\com\\libretv\\android\\presentation\\theme\\Type.kt": ["Typography:com.libretv.android.presentation.theme"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\data\\repository\\WatchHistoryRepository_Factory.java": ["get:com.libretv.android.data.repository.WatchHistoryRepository_Factory", "newInstance:com.libretv.android.data.repository.WatchHistoryRepository_Factory", "WatchHistoryRepository_Factory:com.libretv.android.data.repository", "create:com.libretv.android.data.repository.WatchHistoryRepository_Factory", "<init>:com.libretv.android.data.repository.WatchHistoryRepository_Factory"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\di\\NetworkModule_ProvideApiServiceFactory.java": ["get:com.libretv.android.di.NetworkModule_ProvideApiServiceFactory", "<init>:com.libretv.android.di.NetworkModule_ProvideApiServiceFactory", "provideApiService:com.libretv.android.di.NetworkModule_ProvideApiServiceFactory", "NetworkModule_ProvideApiServiceFactory:com.libretv.android.di", "create:com.libretv.android.di.NetworkModule_ProvideApiServiceFactory"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_libretv_android_LibreTVApplication_GeneratedInjector.java": ["_com_libretv_android_LibreTVApplication_GeneratedInjector:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_libretv_android_LibreTVApplication_GeneratedInjector"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_libretv_android_presentation_screens_settings_SettingsViewModel_HiltModules_BindsModule.java": ["<init>:hilt_aggregated_deps._com_libretv_android_presentation_screens_settings_SettingsViewModel_HiltModules_BindsModule", "_com_libretv_android_presentation_screens_settings_SettingsViewModel_HiltModules_BindsModule:hilt_aggregated_deps"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\libretv\\android\\presentation\\screens\\settings\\SettingsViewModel_HiltModules_KeyModule_ProvideFactory.java": ["get:com.libretv.android.presentation.screens.settings.SettingsViewModel_HiltModules_KeyModule_ProvideFactory", "provide:com.libretv.android.presentation.screens.settings.SettingsViewModel_HiltModules_KeyModule_ProvideFactory", "<init>:com.libretv.android.presentation.screens.settings.SettingsViewModel_HiltModules_KeyModule_ProvideFactory", "SettingsViewModel_HiltModules_KeyModule_ProvideFactory:com.libretv.android.presentation.screens.settings", "create:com.libretv.android.presentation.screens.settings.SettingsViewModel_HiltModules_KeyModule_ProvideFactory", "<init>:com.libretv.android.presentation.screens.settings.SettingsViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder"], "src\\main\\java\\com\\libretv\\android\\data\\database\\SearchHistoryDao.kt": ["insertSearch:com.libretv.android.data.database.SearchHistoryDao", "deleteOldHistory:com.libretv.android.data.database.SearchHistoryDao", "getHistoryCount:com.libretv.android.data.database.SearchHistoryDao", "deleteOldestEntries:com.libretv.android.data.database.SearchHistoryDao", "getRecentSearches:com.libretv.android.data.database.SearchHistoryDao", "clearAllHistory:com.libretv.android.data.database.SearchHistoryDao", "searchHistory:com.libretv.android.data.database.SearchHistoryDao", "SearchHistoryDao:com.libretv.android.data.database", "deleteSearch:com.libretv.android.data.database.SearchHistoryDao"], "src\\main\\java\\com\\libretv\\android\\presentation\\screens\\history\\HistoryScreen.kt": ["HistoryScreen:com.libretv.android.presentation.screens.history"], "src\\main\\java\\com\\libretv\\android\\data\\repository\\VideoRepository.kt": ["searchVideos:com.libretv.android.data.repository.VideoRepository", "updateWatchProgress:com.libretv.android.data.repository.VideoRepository", "VideoRepository:com.libretv.android.data.repository", "updateFavoriteStatus:com.libretv.android.data.repository.VideoRepository", "getVideoEpisodes:com.libretv.android.data.repository.VideoRepository", "getVideoDetail:com.libretv.android.data.repository.VideoRepository", "getFavoriteVideos:com.libretv.android.data.repository.VideoRepository", "cleanOldCache:com.libretv.android.data.repository.VideoRepository", "searchLocalVideos:com.libretv.android.data.repository.VideoRepository"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\data\\repository\\ApiSourceRepository_Factory.java": ["create:com.libretv.android.data.repository.ApiSourceRepository_Factory", "get:com.libretv.android.data.repository.ApiSourceRepository_Factory", "newInstance:com.libretv.android.data.repository.ApiSourceRepository_Factory", "<init>:com.libretv.android.data.repository.ApiSourceRepository_Factory", "ApiSourceRepository_Factory:com.libretv.android.data.repository"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\presentation\\screens\\history\\HistoryViewModel_Factory.java": ["create:com.libretv.android.presentation.screens.history.HistoryViewModel_Factory", "get:com.libretv.android.presentation.screens.history.HistoryViewModel_Factory", "<init>:com.libretv.android.presentation.screens.history.HistoryViewModel_Factory", "HistoryViewModel_Factory:com.libretv.android.presentation.screens.history", "newInstance:com.libretv.android.presentation.screens.history.HistoryViewModel_Factory"], "build\\generated\\ksp\\debug\\kotlin\\com\\libretv\\android\\data\\database\\VideoDao_Impl.kt": ["getRequiredConverters:com.libretv.android.data.database.VideoDao_Impl.Companion", "<init>:com.libretv.android.data.database.VideoDao_Impl.Companion", "updateVideo:com.libretv.android.data.database.VideoDao_Impl", "insertVideos:com.libretv.android.data.database.VideoDao_Impl", "insertVideo:com.libretv.android.data.database.VideoDao_Impl", "getVideoById:com.libretv.android.data.database.VideoDao_Impl", "updateFavoriteStatus:com.libretv.android.data.database.VideoDao_Impl", "VideoDao_Impl:com.libretv.android.data.database", "deleteOldVideos:com.libretv.android.data.database.VideoDao_Impl", "updateWatchProgress:com.libretv.android.data.database.VideoDao_Impl", "deleteVideo:com.libretv.android.data.database.VideoDao_Impl", "getFavoriteVideos:com.libretv.android.data.database.VideoDao_Impl", "searchVideos:com.libretv.android.data.database.VideoDao_Impl", "Companion:com.libretv.android.data.database.VideoDao_Impl"], "build\\generated\\ksp\\debug\\kotlin\\com\\libretv\\android\\data\\database\\LibreTVDatabase_Impl.kt": ["apiSourceDao:com.libretv.android.data.database.LibreTVDatabase_Impl", "createOpenDelegate:com.libretv.android.data.database.LibreTVDatabase_Impl", "videoDao:com.libretv.android.data.database.LibreTVDatabase_Impl", "createAutoMigrations:com.libretv.android.data.database.LibreTVDatabase_Impl", "clearAllTables:com.libretv.android.data.database.LibreTVDatabase_Impl", "getRequiredAutoMigrationSpecClasses:com.libretv.android.data.database.LibreTVDatabase_Impl", "watchHistoryDao:com.libretv.android.data.database.LibreTVDatabase_Impl", "getRequiredTypeConverterClasses:com.libretv.android.data.database.LibreTVDatabase_Impl", "LibreTVDatabase_Impl:com.libretv.android.data.database", "createInvalidationTracker:com.libretv.android.data.database.LibreTVDatabase_Impl", "searchHistoryDao:com.libretv.android.data.database.LibreTVDatabase_Impl", "<init>:com.libretv.android.data.database.LibreTVDatabase_Impl"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\di\\DatabaseModule_ProvideSearchHistoryDaoFactory.java": ["DatabaseModule_ProvideSearchHistoryDaoFactory:com.libretv.android.di", "get:com.libretv.android.di.DatabaseModule_ProvideSearchHistoryDaoFactory", "provideSearchHistoryDao:com.libretv.android.di.DatabaseModule_ProvideSearchHistoryDaoFactory", "<init>:com.libretv.android.di.DatabaseModule_ProvideSearchHistoryDaoFactory", "create:com.libretv.android.di.DatabaseModule_ProvideSearchHistoryDaoFactory"], "src\\main\\java\\com\\libretv\\android\\presentation\\navigation\\LibreTVNavigation.kt": ["<init>:com.libretv.android.presentation.navigation.Screen.Search", "Screen:com.libretv.android.presentation.navigation", "Search:com.libretv.android.presentation.navigation.Screen", "<init>:com.libretv.android.presentation.navigation.Screen.Settings", "History:com.libretv.android.presentation.navigation.Screen", "Home:com.libretv.android.presentation.navigation.Screen", "Settings:com.libretv.android.presentation.navigation.Screen", "LibreTVNavigation:com.libretv.android.presentation.navigation", "icon:com.libretv.android.presentation.navigation.Screen", "bottomNavItems:com.libretv.android.presentation.navigation", "route:com.libretv.android.presentation.navigation.Screen", "title:com.libretv.android.presentation.navigation.Screen", "<init>:com.libretv.android.presentation.navigation.Screen.Home", "<init>:com.libretv.android.presentation.navigation.Screen.History"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\di\\NetworkModule_ProvideHttpLoggingInterceptorFactory.java": ["create:com.libretv.android.di.NetworkModule_ProvideHttpLoggingInterceptorFactory", "NetworkModule_ProvideHttpLoggingInterceptorFactory:com.libretv.android.di", "<init>:com.libretv.android.di.NetworkModule_ProvideHttpLoggingInterceptorFactory", "<init>:com.libretv.android.di.NetworkModule_ProvideHttpLoggingInterceptorFactory.InstanceHolder", "provideHttpLoggingInterceptor:com.libretv.android.di.NetworkModule_ProvideHttpLoggingInterceptorFactory", "get:com.libretv.android.di.NetworkModule_ProvideHttpLoggingInterceptorFactory"], "build\\generated\\ksp\\debug\\kotlin\\com\\libretv\\android\\data\\database\\WatchHistoryDao_Impl.kt": ["WatchHistoryDao_Impl:com.libretv.android.data.database", "clearAllHistory:com.libretv.android.data.database.WatchHistoryDao_Impl", "getWatchHistory:com.libretv.android.data.database.WatchHistoryDao_Impl", "insertWatchHistory:com.libretv.android.data.database.WatchHistoryDao_Impl", "updateEpisode:com.libretv.android.data.database.WatchHistoryDao_Impl", "getWatchHistoryById:com.libretv.android.data.database.WatchHistoryDao_Impl", "updateProgress:com.libretv.android.data.database.WatchHistoryDao_Impl", "getRecentWatchHistory:com.libretv.android.data.database.WatchHistoryDao_Impl", "updateWatchHistory:com.libretv.android.data.database.WatchHistoryDao_Impl", "Companion:com.libretv.android.data.database.WatchHistoryDao_Impl", "deleteWatchHistory:com.libretv.android.data.database.WatchHistoryDao_Impl", "getAllWatchHistory:com.libretv.android.data.database.WatchHistoryDao_Impl", "getRequiredConverters:com.libretv.android.data.database.WatchHistoryDao_Impl.Companion", "deleteOldHistory:com.libretv.android.data.database.WatchHistoryDao_Impl", "<init>:com.libretv.android.data.database.WatchHistoryDao_Impl.Companion"], "build\\generated\\ksp\\debug\\kotlin\\com\\libretv\\android\\data\\database\\SearchHistoryDao_Impl.kt": ["deleteSearch:com.libretv.android.data.database.SearchHistoryDao_Impl", "searchHistory:com.libretv.android.data.database.SearchHistoryDao_Impl", "Companion:com.libretv.android.data.database.SearchHistoryDao_Impl", "SearchHistoryDao_Impl:com.libretv.android.data.database", "deleteOldestEntries:com.libretv.android.data.database.SearchHistoryDao_Impl", "insertSearch:com.libretv.android.data.database.SearchHistoryDao_Impl", "getHistoryCount:com.libretv.android.data.database.SearchHistoryDao_Impl", "deleteOldHistory:com.libretv.android.data.database.SearchHistoryDao_Impl", "getRequiredConverters:com.libretv.android.data.database.SearchHistoryDao_Impl.Companion", "getRecentSearches:com.libretv.android.data.database.SearchHistoryDao_Impl", "clearAllHistory:com.libretv.android.data.database.SearchHistoryDao_Impl", "<init>:com.libretv.android.data.database.SearchHistoryDao_Impl.Companion"], "src\\main\\java\\com\\libretv\\android\\presentation\\screens\\home\\HomeScreen.kt": ["HomeScreen:com.libretv.android.presentation.screens.home"], "src\\main\\java\\com\\example\\shipin\\MainActivity.kt": ["<init>:com.libretv.android.presentation.MainActivity", "LibreTVApp:com.libretv.android.presentation", "MainActivity:com.libretv.android.presentation", "onCreate:com.libretv.android.presentation.MainActivity"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\data\\preferences\\SettingsManager_Factory.java": ["get:com.libretv.android.data.preferences.SettingsManager_Factory", "SettingsManager_Factory:com.libretv.android.data.preferences", "newInstance:com.libretv.android.data.preferences.SettingsManager_Factory", "create:com.libretv.android.data.preferences.SettingsManager_Factory", "<init>:com.libretv.android.data.preferences.SettingsManager_Factory"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\hilt_aggregated_deps\\_com_libretv_android_di_DatabaseModule.java": ["<init>:hilt_aggregated_deps._com_libretv_android_di_DatabaseModule", "_com_libretv_android_di_DatabaseModule:hilt_aggregated_deps"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\presentation\\screens\\home\\HomeViewModel_HiltModules.java": ["binds:com.libretv.android.presentation.screens.home.HomeViewModel_HiltModules.BindsModule", "provide:com.libretv.android.presentation.screens.home.HomeViewModel_HiltModules.KeyModule", "HomeViewModel_HiltModules:com.libretv.android.presentation.screens.home", "BindsModule:com.libretv.android.presentation.screens.home.HomeViewModel_HiltModules", "KeyModule:com.libretv.android.presentation.screens.home.HomeViewModel_HiltModules"], "src\\main\\java\\com\\libretv\\android\\presentation\\screens\\history\\HistoryViewModel.kt": ["refresh:com.libretv.android.presentation.screens.history.HistoryViewModel", "watchHistory:com.libretv.android.presentation.screens.history.HistoryUiState", "deleteHistory:com.libretv.android.presentation.screens.history.HistoryViewModel", "isLoading:com.libretv.android.presentation.screens.history.HistoryUiState", "HistoryViewModel:com.libretv.android.presentation.screens.history", "HistoryUiState:com.libretv.android.presentation.screens.history", "uiState:com.libretv.android.presentation.screens.history.HistoryViewModel", "clearAllHistory:com.libretv.android.presentation.screens.history.HistoryViewModel"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\di\\NetworkModule_ProvideDoubanApiServiceFactory.java": ["create:com.libretv.android.di.NetworkModule_ProvideDoubanApiServiceFactory", "<init>:com.libretv.android.di.NetworkModule_ProvideDoubanApiServiceFactory", "NetworkModule_ProvideDoubanApiServiceFactory:com.libretv.android.di", "get:com.libretv.android.di.NetworkModule_ProvideDoubanApiServiceFactory", "provideDoubanApiService:com.libretv.android.di.NetworkModule_ProvideDoubanApiServiceFactory"], "src\\main\\java\\com\\libretv\\android\\presentation\\screens\\search\\SearchViewModel.kt": ["SearchUiState:com.libretv.android.presentation.screens.search", "isLoading:com.libretv.android.presentation.screens.search.SearchUiState", "clearError:com.libretv.android.presentation.screens.search.SearchViewModel", "searchHistory:com.libretv.android.presentation.screens.search.SearchUiState", "searchResults:com.libretv.android.presentation.screens.search.SearchUiState", "query:com.libretv.android.presentation.screens.search.SearchUiState", "error:com.libretv.android.presentation.screens.search.SearchUiState", "updateQuery:com.libretv.android.presentation.screens.search.SearchViewModel", "uiState:com.libretv.android.presentation.screens.search.SearchViewModel", "search:com.libretv.android.presentation.screens.search.SearchViewModel", "SearchViewModel:com.libretv.android.presentation.screens.search", "clearSearchResults:com.libretv.android.presentation.screens.search.SearchViewModel"], "src\\main\\java\\com\\libretv\\android\\presentation\\screens\\search\\SearchScreen.kt": ["SearchScreen:com.libretv.android.presentation.screens.search"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\libretv\\android\\presentation\\screens\\history\\HistoryViewModel_HiltModules_KeyModule_ProvideFactory.java": ["HistoryViewModel_HiltModules_KeyModule_ProvideFactory:com.libretv.android.presentation.screens.history", "get:com.libretv.android.presentation.screens.history.HistoryViewModel_HiltModules_KeyModule_ProvideFactory", "create:com.libretv.android.presentation.screens.history.HistoryViewModel_HiltModules_KeyModule_ProvideFactory", "provide:com.libretv.android.presentation.screens.history.HistoryViewModel_HiltModules_KeyModule_ProvideFactory", "<init>:com.libretv.android.presentation.screens.history.HistoryViewModel_HiltModules_KeyModule_ProvideFactory", "<init>:com.libretv.android.presentation.screens.history.HistoryViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\di\\NetworkModule_ProvideInternalRetrofitFactory.java": ["<init>:com.libretv.android.di.NetworkModule_ProvideInternalRetrofitFactory", "provideInternalRetrofit:com.libretv.android.di.NetworkModule_ProvideInternalRetrofitFactory", "create:com.libretv.android.di.NetworkModule_ProvideInternalRetrofitFactory", "NetworkModule_ProvideInternalRetrofitFactory:com.libretv.android.di", "get:com.libretv.android.di.NetworkModule_ProvideInternalRetrofitFactory"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\presentation\\screens\\settings\\SettingsViewModel_HiltModules.java": ["BindsModule:com.libretv.android.presentation.screens.settings.SettingsViewModel_HiltModules", "KeyModule:com.libretv.android.presentation.screens.settings.SettingsViewModel_HiltModules", "binds:com.libretv.android.presentation.screens.settings.SettingsViewModel_HiltModules.BindsModule", "SettingsViewModel_HiltModules:com.libretv.android.presentation.screens.settings", "provide:com.libretv.android.presentation.screens.settings.SettingsViewModel_HiltModules.KeyModule"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\presentation\\screens\\history\\HistoryViewModel_HiltModules.java": ["BindsModule:com.libretv.android.presentation.screens.history.HistoryViewModel_HiltModules", "KeyModule:com.libretv.android.presentation.screens.history.HistoryViewModel_HiltModules", "binds:com.libretv.android.presentation.screens.history.HistoryViewModel_HiltModules.BindsModule", "HistoryViewModel_HiltModules:com.libretv.android.presentation.screens.history", "provide:com.libretv.android.presentation.screens.history.HistoryViewModel_HiltModules.KeyModule"], "build\\generated\\source\\buildConfig\\debug\\com\\libretv\\android\\BuildConfig.java": ["DEBUG:com.libretv.android.BuildConfig", "<init>:com.libretv.android.BuildConfig", "VERSION_NAME:com.libretv.android.BuildConfig", "VERSION_CODE:com.libretv.android.BuildConfig", "BUILD_TYPE:com.libretv.android.BuildConfig", "BuildConfig:com.libretv.android", "APPLICATION_ID:com.libretv.android.BuildConfig"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\di\\NetworkModule_ProvideInternalApiServiceFactory.java": ["create:com.libretv.android.di.NetworkModule_ProvideInternalApiServiceFactory", "NetworkModule_ProvideInternalApiServiceFactory:com.libretv.android.di", "<init>:com.libretv.android.di.NetworkModule_ProvideInternalApiServiceFactory", "provideInternalApiService:com.libretv.android.di.NetworkModule_ProvideInternalApiServiceFactory", "get:com.libretv.android.di.NetworkModule_ProvideInternalApiServiceFactory"], "src\\main\\java\\com\\libretv\\android\\data\\repository\\SearchRepository.kt": ["clearAllHistory:com.libretv.android.data.repository.SearchRepository", "getRecentSearches:com.libretv.android.data.repository.SearchRepository", "cleanOldHistory:com.libretv.android.data.repository.SearchRepository", "deleteSearchHistory:com.libretv.android.data.repository.SearchRepository", "addSearchHistory:com.libretv.android.data.repository.SearchRepository", "SearchRepository:com.libretv.android.data.repository", "searchHistory:com.libretv.android.data.repository.SearchRepository"], "src\\main\\java\\com\\libretv\\android\\data\\network\\ApiService.kt": ["proxyRequest:com.libretv.android.data.network.ApiService", "InternalApiService:com.libretv.android.data.network", "proxy:com.libretv.android.data.network.InternalApiService", "title:com.libretv.android.data.network.DoubanSubject", "url:com.libretv.android.data.network.DoubanSubject", "genres:com.libretv.android.data.network.DoubanSubject", "aggregatedSearch:com.libretv.android.data.network.InternalApiService", "getDetail:com.libretv.android.data.network.InternalApiService", "DoubanResponse:com.libretv.android.data.network", "DoubanSubject:com.libretv.android.data.network", "DoubanApiService:com.libretv.android.data.network", "subjects:com.libretv.android.data.network.DoubanResponse", "year:com.libretv.android.data.network.DoubanSubject", "cover:com.libretv.android.data.network.DoubanSubject", "rate:com.libretv.android.data.network.DoubanSubject", "searchVideos:com.libretv.android.data.network.ApiService", "getVideoDetail:com.libretv.android.data.network.ApiService", "id:com.libretv.android.data.network.DoubanSubject", "casts:com.libretv.android.data.network.DoubanSubject", "getHotMovies:com.libretv.android.data.network.DoubanApiService", "ApiService:com.libretv.android.data.network", "directors:com.libretv.android.data.network.DoubanSubject", "getHotTvShows:com.libretv.android.data.network.DoubanApiService"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\hilt_aggregated_deps\\_com_libretv_android_di_NetworkModule.java": ["_com_libretv_android_di_NetworkModule:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_libretv_android_di_NetworkModule"], "src\\main\\java\\com\\libretv\\android\\di\\NetworkModule.kt": ["provideDoubanRetrofit:com.libretv.android.di.NetworkModule", "provideGson:com.libretv.android.di.NetworkModule", "NetworkModule:com.libretv.android.di", "provideInternalApiService:com.libretv.android.di.NetworkModule", "<init>:com.libretv.android.di.ApiRetrofit", "provideOkHttpClient:com.libretv.android.di.NetworkModule", "<init>:com.libretv.android.di.InternalRetrofit", "<init>:com.libretv.android.di.NetworkModule", "provideApiService:com.libretv.android.di.NetworkModule", "ApiRetrofit:com.libretv.android.di", "provideHttpLoggingInterceptor:com.libretv.android.di.NetworkModule", "provideInternalRetrofit:com.libretv.android.di.NetworkModule", "DoubanRetrofit:com.libretv.android.di", "provideJson:com.libretv.android.di.NetworkModule", "<init>:com.libretv.android.di.DoubanRetrofit", "provideDoubanApiService:com.libretv.android.di.NetworkModule", "InternalRetrofit:com.libretv.android.di", "provideApiRetrofit:com.libretv.android.di.NetworkModule"], "src\\main\\java\\com\\libretv\\android\\data\\database\\LibreTVDatabase.kt": ["<init>:com.libretv.android.data.database.Converters", "watchHistoryDao:com.libretv.android.data.database.LibreTVDatabase", "apiSourceDao:com.libretv.android.data.database.LibreTVDatabase", "Companion:com.libretv.android.data.database.LibreTVDatabase", "LibreTVDatabase:com.libretv.android.data.database", "videoDao:com.libretv.android.data.database.LibreTVDatabase", "DATABASE_NAME:com.libretv.android.data.database.LibreTVDatabase.Companion", "fromStringList:com.libretv.android.data.database.Converters", "searchHistoryDao:com.libretv.android.data.database.LibreTVDatabase", "<init>:com.libretv.android.data.database.LibreTVDatabase", "create:com.libretv.android.data.database.LibreTVDatabase.Companion", "toStringList:com.libretv.android.data.database.Converters", "Converters:com.libretv.android.data.database", "<init>:com.libretv.android.data.database.LibreTVDatabase.Companion"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\presentation\\Hilt_MainActivity.java": ["createComponentManager:com.libretv.android.presentation.Hilt_MainActivity", "onCreate:com.libretv.android.presentation.Hilt_MainActivity", "generatedComponent:com.libretv.android.presentation.Hilt_MainActivity", "getDefaultViewModelProviderFactory:com.libretv.android.presentation.Hilt_MainActivity", "<init>:com.libretv.android.presentation.Hilt_MainActivity", "inject:com.libretv.android.presentation.Hilt_MainActivity", "componentManager:com.libretv.android.presentation.Hilt_MainActivity", "onDestroy:com.libretv.android.presentation.Hilt_MainActivity", "Hilt_MainActivity:com.libretv.android.presentation"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\data\\repository\\SearchRepository_Factory.java": ["SearchRepository_Factory:com.libretv.android.data.repository", "get:com.libretv.android.data.repository.SearchRepository_Factory", "<init>:com.libretv.android.data.repository.SearchRepository_Factory", "newInstance:com.libretv.android.data.repository.SearchRepository_Factory", "create:com.libretv.android.data.repository.SearchRepository_Factory"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_libretv_android_presentation_MainActivity_GeneratedInjector.java": ["_com_libretv_android_presentation_MainActivity_GeneratedInjector:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_libretv_android_presentation_MainActivity_GeneratedInjector"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\LibreTVApplication_GeneratedInjector.java": ["LibreTVApplication_GeneratedInjector:com.libretv.android", "injectLibreTVApplication:com.libretv.android.LibreTVApplication_GeneratedInjector"], "src\\main\\java\\com\\libretv\\android\\presentation\\screens\\settings\\SettingsViewModel.kt": ["resetToDefault:com.libretv.android.presentation.screens.settings.SettingsViewModel", "uiState:com.libretv.android.presentation.screens.settings.SettingsViewModel", "themeMode:com.libretv.android.presentation.screens.settings.SettingsUiState", "setThemeMode:com.libretv.android.presentation.screens.settings.SettingsViewModel", "exportSettings:com.libretv.android.presentation.screens.settings.SettingsViewModel", "cacheSizeMB:com.libretv.android.presentation.screens.settings.SettingsUiState", "setCacheEnabled:com.libretv.android.presentation.screens.settings.SettingsViewModel", "gridColumns:com.libretv.android.presentation.screens.settings.SettingsUiState", "playerAutoFullscreen:com.libretv.android.presentation.screens.settings.SettingsUiState", "adultContentEnabled:com.libretv.android.presentation.screens.settings.SettingsUiState", "setAdultContentEnabled:com.libretv.android.presentation.screens.settings.SettingsViewModel", "SettingsUiState:com.libretv.android.presentation.screens.settings", "autoPlayNext:com.libretv.android.presentation.screens.settings.SettingsUiState", "setRememberProgress:com.libretv.android.presentation.screens.settings.SettingsViewModel", "clearCache:com.libretv.android.presentation.screens.settings.SettingsViewModel", "cacheEnabled:com.libretv.android.presentation.screens.settings.SettingsUiState", "setGridColumns:com.libretv.android.presentation.screens.settings.SettingsViewModel", "setAutoPlayNext:com.libretv.android.presentation.screens.settings.SettingsViewModel", "rememberProgress:com.libretv.android.presentation.screens.settings.SettingsUiState", "SettingsViewModel:com.libretv.android.presentation.screens.settings", "setPlayerAutoFullscreen:com.libretv.android.presentation.screens.settings.SettingsViewModel", "importSettings:com.libretv.android.presentation.screens.settings.SettingsViewModel", "setCacheSizeMB:com.libretv.android.presentation.screens.settings.SettingsViewModel"], "src\\main\\java\\com\\libretv\\android\\LibreTVApplication.kt": ["<init>:com.libretv.android.LibreTVApplication", "LibreTVApplication:com.libretv.android", "onCreate:com.libretv.android.LibreTVApplication"], "src\\main\\java\\com\\libretv\\android\\data\\model\\VideoInfo.kt": ["type:com.libretv.android.data.model.VideoDetailInfo", "lastWatchTime:com.libretv.android.data.model.WatchHistory", "cover:com.libretv.android.data.model.VideoDetailInfo", "apiUrl:com.libretv.android.data.model.VideoInfo", "vodPlayUrl:com.libretv.android.data.model.VideoInfo", "sourceCode:com.libretv.android.data.model.VideoDetailInfo", "ApiResponse:com.libretv.android.data.model", "desc:com.libretv.android.data.model.VideoDetailInfo", "sourceCode:com.libretv.android.data.model.VideoInfo", "VideoDetailInfo:com.libretv.android.data.model", "watchProgress:com.libretv.android.data.model.WatchHistory", "actor:com.libretv.android.data.model.VideoDetailInfo", "episodes:com.libretv.android.data.model.WatchHistory", "director:com.libretv.android.data.model.VideoDetailInfo", "videoId:com.libretv.android.data.model.WatchHistory", "query:com.libretv.android.data.model.SearchHistory", "WatchHistory:com.libretv.android.data.model", "sourceName:com.libretv.android.data.model.VideoInfo", "detailUrl:com.libretv.android.data.model.ApiResponse", "sourceCode:com.libretv.android.data.model.WatchHistory", "timestamp:com.libretv.android.data.model.SearchHistory", "vodPic:com.libretv.android.data.model.VideoInfo", "sourceName:com.libretv.android.data.model.VideoDetailInfo", "episodes:com.libretv.android.data.model.ApiResponse", "VideoInfo:com.libretv.android.data.model", "episodeIndex:com.libretv.android.data.model.WatchHistory", "ApiSource:com.libretv.android.data.model", "vodRemarks:com.libretv.android.data.model.VideoInfo", "videoTitle:com.libretv.android.data.model.WatchHistory", "id:com.libretv.android.data.model.WatchHistory", "lastWatchTime:com.libretv.android.data.model.VideoInfo", "isCustom:com.libretv.android.data.model.ApiSource", "vodActor:com.libretv.android.data.model.VideoInfo", "api:com.libretv.android.data.model.ApiSource", "videoInfo:com.libretv.android.data.model.ApiResponse", "list:com.libretv.android.data.model.ApiResponse", "code:com.libretv.android.data.model.ApiSource", "vodName:com.libretv.android.data.model.VideoInfo", "title:com.libretv.android.data.model.VideoDetailInfo", "isEnabled:com.libretv.android.data.model.ApiSource", "order:com.libretv.android.data.model.ApiSource", "vodDirector:com.libretv.android.data.model.VideoInfo", "totalDuration:com.libretv.android.data.model.WatchHistory", "remarks:com.libretv.android.data.model.VideoDetailInfo", "year:com.libretv.android.data.model.VideoDetailInfo", "vodYear:com.libretv.android.data.model.VideoInfo", "typeName:com.libretv.android.data.model.VideoInfo", "name:com.libretv.android.data.model.ApiSource", "vodArea:com.libretv.android.data.model.VideoInfo", "isAdult:com.libretv.android.data.model.ApiSource", "sourceName:com.libretv.android.data.model.WatchHistory", "area:com.libretv.android.data.model.VideoDetailInfo", "videoCover:com.libretv.android.data.model.WatchHistory", "isFavorite:com.libretv.android.data.model.VideoInfo", "vodId:com.libretv.android.data.model.VideoInfo", "SearchHistory:com.libretv.android.data.model", "episodeTitle:com.libretv.android.data.model.WatchHistory", "vodContent:com.libretv.android.data.model.VideoInfo", "watchProgress:com.libretv.android.data.model.VideoInfo", "code:com.libretv.android.data.model.ApiResponse", "msg:com.libretv.android.data.model.ApiResponse", "detail:com.libretv.android.data.model.ApiSource"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_libretv_android_presentation_screens_home_HomeViewModel_HiltModules_BindsModule.java": ["_com_libretv_android_presentation_screens_home_HomeViewModel_HiltModules_BindsModule:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_libretv_android_presentation_screens_home_HomeViewModel_HiltModules_BindsModule"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\presentation\\screens\\home\\HomeViewModel_Factory.java": ["get:com.libretv.android.presentation.screens.home.HomeViewModel_Factory", "newInstance:com.libretv.android.presentation.screens.home.HomeViewModel_Factory", "<init>:com.libretv.android.presentation.screens.home.HomeViewModel_Factory", "create:com.libretv.android.presentation.screens.home.HomeViewModel_Factory", "HomeViewModel_Factory:com.libretv.android.presentation.screens.home"], "src\\main\\java\\com\\libretv\\android\\presentation\\components\\SearchBar.kt": ["SearchBar:com.libretv.android.presentation.components", "SimpleSearchField:com.libretv.android.presentation.components"], "src\\main\\java\\com\\libretv\\android\\data\\database\\WatchHistoryDao.kt": ["getRecentWatchHistory:com.libretv.android.data.database.WatchHistoryDao", "deleteOldHistory:com.libretv.android.data.database.WatchHistoryDao", "getWatchHistoryById:com.libretv.android.data.database.WatchHistoryDao", "updateEpisode:com.libretv.android.data.database.WatchHistoryDao", "WatchHistoryDao:com.libretv.android.data.database", "updateWatchHistory:com.libretv.android.data.database.WatchHistoryDao", "getAllWatchHistory:com.libretv.android.data.database.WatchHistoryDao", "insertWatchHistory:com.libretv.android.data.database.WatchHistoryDao", "deleteWatchHistory:com.libretv.android.data.database.WatchHistoryDao", "getWatchHistory:com.libretv.android.data.database.WatchHistoryDao", "clearAllHistory:com.libretv.android.data.database.WatchHistoryDao", "updateProgress:com.libretv.android.data.database.WatchHistoryDao"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\di\\DatabaseModule_ProvideVideoDaoFactory.java": ["create:com.libretv.android.di.DatabaseModule_ProvideVideoDaoFactory", "<init>:com.libretv.android.di.DatabaseModule_ProvideVideoDaoFactory", "DatabaseModule_ProvideVideoDaoFactory:com.libretv.android.di", "provideVideoDao:com.libretv.android.di.DatabaseModule_ProvideVideoDaoFactory", "get:com.libretv.android.di.DatabaseModule_ProvideVideoDaoFactory"], "src\\main\\java\\com\\libretv\\android\\presentation\\screens\\settings\\SettingsScreen.kt": ["SettingsScreen:com.libretv.android.presentation.screens.settings"], "src\\main\\java\\com\\example\\shipin\\ui\\theme\\Theme.kt": ["ShipinTheme:com.example.shipin.ui.theme"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\di\\NetworkModule_ProvideOkHttpClientFactory.java": ["<init>:com.libretv.android.di.NetworkModule_ProvideOkHttpClientFactory", "provideOkHttpClient:com.libretv.android.di.NetworkModule_ProvideOkHttpClientFactory", "NetworkModule_ProvideOkHttpClientFactory:com.libretv.android.di", "get:com.libretv.android.di.NetworkModule_ProvideOkHttpClientFactory", "create:com.libretv.android.di.NetworkModule_ProvideOkHttpClientFactory"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_libretv_android_presentation_screens_search_SearchViewModel_HiltModules_KeyModule.java": ["_com_libretv_android_presentation_screens_search_SearchViewModel_HiltModules_KeyModule:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_libretv_android_presentation_screens_search_SearchViewModel_HiltModules_KeyModule"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\dagger\\hilt\\internal\\aggregatedroot\\codegen\\_com_libretv_android_LibreTVApplication.java": ["<init>:dagger.hilt.internal.aggregatedroot.codegen._com_libretv_android_LibreTVApplication", "_com_libretv_android_LibreTVApplication:dagger.hilt.internal.aggregatedroot.codegen"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\data\\repository\\VideoRepository_Factory.java": ["VideoRepository_Factory:com.libretv.android.data.repository", "get:com.libretv.android.data.repository.VideoRepository_Factory", "create:com.libretv.android.data.repository.VideoRepository_Factory", "newInstance:com.libretv.android.data.repository.VideoRepository_Factory", "<init>:com.libretv.android.data.repository.VideoRepository_Factory"], "src\\main\\java\\com\\libretv\\android\\data\\preferences\\SettingsManager.kt": ["proxyUrl:com.libretv.android.data.preferences.SettingsManager", "setProxyUrl:com.libretv.android.data.preferences.SettingsManager", "setTimeoutSeconds:com.libretv.android.data.preferences.SettingsManager", "Companion:com.libretv.android.data.preferences.SettingsManager", "isRememberProgress:com.libretv.android.data.preferences.SettingsManager", "playerSpeed:com.libretv.android.data.preferences.SettingsManager", "setThemeMode:com.libretv.android.data.preferences.SettingsManager", "timeoutSeconds:com.libretv.android.data.preferences.SettingsManager", "setLastVersionCode:com.libretv.android.data.preferences.SettingsManager", "isAdultContentEnabled:com.libretv.android.data.preferences.SettingsManager", "setCacheEnabled:com.libretv.android.data.preferences.SettingsManager", "SettingsManager:com.libretv.android.data.preferences", "isPasswordEnabled:com.libretv.android.data.preferences.SettingsManager", "isUseProxy:com.libretv.android.data.preferences.SettingsManager", "setFirstLaunch:com.libretv.android.data.preferences.SettingsManager", "setPasswordHash:com.libretv.android.data.preferences.SettingsManager", "setCacheSizeMB:com.libretv.android.data.preferences.SettingsManager", "cacheSizeMB:com.libretv.android.data.preferences.SettingsManager", "passwordHash:com.libretv.android.data.preferences.SettingsManager", "themeMode:com.libretv.android.data.preferences.SettingsManager", "isCacheEnabled:com.libretv.android.data.preferences.SettingsManager", "setPlayerVolume:com.libretv.android.data.preferences.SettingsManager", "setPlayerBrightness:com.libretv.android.data.preferences.SettingsManager", "playerBrightness:com.libretv.android.data.preferences.SettingsManager", "isFirstLaunch:com.libretv.android.data.preferences.SettingsManager", "setUseProxy:com.libretv.android.data.preferences.SettingsManager", "<init>:com.libretv.android.data.preferences.SettingsManager.Companion", "playerVolume:com.libretv.android.data.preferences.SettingsManager", "setAdultContentEnabled:com.libretv.android.data.preferences.SettingsManager", "setPlayerSpeed:com.libretv.android.data.preferences.SettingsManager", "setAutoCleanCache:com.libretv.android.data.preferences.SettingsManager", "setPasswordEnabled:com.libretv.android.data.preferences.SettingsManager", "setPlayerAutoFullscreen:com.libretv.android.data.preferences.SettingsManager", "setRememberProgress:com.libretv.android.data.preferences.SettingsManager", "clearAllSettings:com.libretv.android.data.preferences.SettingsManager", "setShowAdultWarning:com.libretv.android.data.preferences.SettingsManager", "lastVersionCode:com.libretv.android.data.preferences.SettingsManager", "setAutoPlayNext:com.libretv.android.data.preferences.SettingsManager", "isAutoPlayNext:com.libretv.android.data.preferences.SettingsManager", "isAutoCleanCache:com.libretv.android.data.preferences.SettingsManager", "setGridColumns:com.libretv.android.data.preferences.SettingsManager", "showAdultWarning:com.libretv.android.data.preferences.SettingsManager", "isPlayerAutoFullscreen:com.libretv.android.data.preferences.SettingsManager", "gridColumns:com.libretv.android.data.preferences.SettingsManager"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_libretv_android_presentation_screens_history_HistoryViewModel_HiltModules_KeyModule.java": ["<init>:hilt_aggregated_deps._com_libretv_android_presentation_screens_history_HistoryViewModel_HiltModules_KeyModule", "_com_libretv_android_presentation_screens_history_HistoryViewModel_HiltModules_KeyModule:hilt_aggregated_deps"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\libretv\\android\\presentation\\MainActivity_GeneratedInjector.java": ["injectMainActivity:com.libretv.android.presentation.MainActivity_GeneratedInjector", "MainActivity_GeneratedInjector:com.libretv.android.presentation"]}