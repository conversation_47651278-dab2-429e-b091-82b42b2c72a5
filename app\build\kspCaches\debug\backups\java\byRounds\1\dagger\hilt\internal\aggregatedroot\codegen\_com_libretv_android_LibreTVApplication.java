package dagger.hilt.internal.aggregatedroot.codegen;

import dagger.hilt.android.HiltAndroidApp;
import dagger.hilt.internal.aggregatedroot.AggregatedRoot;
import javax.annotation.processing.Generated;

/**
 * This class should only be referenced by generated code! This class aggregates information across multiple compilations.
 */
@AggregatedRoot(
    root = "com.libretv.android.LibreTVApplication",
    rootPackage = "com.libretv.android",
    originatingRoot = "com.libretv.android.LibreTVApplication",
    originatingRootPackage = "com.libretv.android",
    rootAnnotation = HiltAndroidApp.class,
    rootSimpleNames = "LibreTVApplication",
    originatingRootSimpleNames = "LibreTVApplication"
)
@Generated("dagger.hilt.processor.internal.root.AggregatedRootGenerator")
public class _com_libretv_android_LibreTVApplication {
}
