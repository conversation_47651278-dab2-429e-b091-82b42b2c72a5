package com.libretv.android.presentation.screens.search

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.libretv.android.data.model.SearchHistory
import com.libretv.android.data.model.VideoInfo
import com.libretv.android.data.repository.SearchRepository
import com.libretv.android.data.repository.VideoRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 搜索ViewModel
 */
@HiltViewModel
class SearchViewModel @Inject constructor(
    private val videoRepository: VideoRepository,
    private val searchRepository: SearchRepository
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(SearchUiState())
    val uiState: StateFlow<SearchUiState> = _uiState.asStateFlow()
    
    init {
        loadSearchHistory()
    }
    
    private fun loadSearchHistory() {
        viewModelScope.launch {
            searchRepository.getRecentSearches(10)
                .collect { history ->
                    _uiState.update { 
                        it.copy(searchHistory = history)
                    }
                }
        }
    }
    
    fun updateQuery(query: String) {
        _uiState.update { 
            it.copy(query = query, error = null)
        }
    }
    
    fun search(query: String = _uiState.value.query) {
        if (query.isBlank()) return
        
        viewModelScope.launch {
            _uiState.update { 
                it.copy(
                    isLoading = true,
                    error = null,
                    query = query
                )
            }
            
            try {
                // 添加到搜索历史
                searchRepository.addSearchHistory(query)
                
                // 执行搜索
                val result = videoRepository.searchVideos(query)
                
                result.onSuccess { videos ->
                    _uiState.update { 
                        it.copy(
                            isLoading = false,
                            searchResults = videos,
                            error = null
                        )
                    }
                }.onFailure { error ->
                    _uiState.update { 
                        it.copy(
                            isLoading = false,
                            error = error.message ?: "搜索失败"
                        )
                    }
                }
                
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(
                        isLoading = false,
                        error = e.message ?: "搜索失败"
                    )
                }
            }
        }
    }
    
    fun clearSearchResults() {
        _uiState.update { 
            it.copy(
                searchResults = emptyList(),
                error = null
            )
        }
    }
    
    fun clearError() {
        _uiState.update { it.copy(error = null) }
    }
}

/**
 * 搜索UI状态
 */
data class SearchUiState(
    val query: String = "",
    val isLoading: Boolean = false,
    val searchResults: List<VideoInfo> = emptyList(),
    val searchHistory: List<SearchHistory> = emptyList(),
    val error: String? = null
)
