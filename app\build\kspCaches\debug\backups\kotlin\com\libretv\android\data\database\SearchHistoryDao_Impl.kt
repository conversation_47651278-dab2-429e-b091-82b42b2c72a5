package com.libretv.android.`data`.database

import androidx.room.EntityDeleteOrUpdateAdapter
import androidx.room.EntityInsertAdapter
import androidx.room.RoomDatabase
import androidx.room.coroutines.createFlow
import androidx.room.util.getColumnIndexOrThrow
import androidx.room.util.performSuspending
import androidx.sqlite.SQLiteStatement
import com.libretv.android.`data`.model.SearchHistory
import javax.`annotation`.processing.Generated
import kotlin.Int
import kotlin.Long
import kotlin.String
import kotlin.Suppress
import kotlin.Unit
import kotlin.collections.List
import kotlin.collections.MutableList
import kotlin.collections.mutableListOf
import kotlin.reflect.KClass
import kotlinx.coroutines.flow.Flow

@Generated(value = ["androidx.room.RoomProcessor"])
@Suppress(names = ["UNCHECKED_CAST", "DEPRECATION", "REDUNDANT_PROJECTION", "REMOVAL"])
public class SearchHistoryDao_Impl(
  __db: RoomDatabase,
) : SearchHistoryDao {
  private val __db: RoomDatabase

  private val __insertAdapterOfSearchHistory: EntityInsertAdapter<SearchHistory>

  private val __deleteAdapterOfSearchHistory: EntityDeleteOrUpdateAdapter<SearchHistory>
  init {
    this.__db = __db
    this.__insertAdapterOfSearchHistory = object : EntityInsertAdapter<SearchHistory>() {
      protected override fun createQuery(): String =
          "INSERT OR REPLACE INTO `search_history` (`query`,`timestamp`) VALUES (?,?)"

      protected override fun bind(statement: SQLiteStatement, entity: SearchHistory) {
        statement.bindText(1, entity.query)
        statement.bindLong(2, entity.timestamp)
      }
    }
    this.__deleteAdapterOfSearchHistory = object : EntityDeleteOrUpdateAdapter<SearchHistory>() {
      protected override fun createQuery(): String =
          "DELETE FROM `search_history` WHERE `query` = ?"

      protected override fun bind(statement: SQLiteStatement, entity: SearchHistory) {
        statement.bindText(1, entity.query)
      }
    }
  }

  public override suspend fun insertSearch(search: SearchHistory): Unit = performSuspending(__db,
      false, true) { _connection ->
    __insertAdapterOfSearchHistory.insert(_connection, search)
  }

  public override suspend fun deleteSearch(search: SearchHistory): Unit = performSuspending(__db,
      false, true) { _connection ->
    __deleteAdapterOfSearchHistory.handle(_connection, search)
  }

  public override fun getRecentSearches(limit: Int): Flow<List<SearchHistory>> {
    val _sql: String = "SELECT * FROM search_history ORDER BY timestamp DESC LIMIT ?"
    return createFlow(__db, false, arrayOf("search_history")) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        var _argIndex: Int = 1
        _stmt.bindLong(_argIndex, limit.toLong())
        val _columnIndexOfQuery: Int = getColumnIndexOrThrow(_stmt, "query")
        val _columnIndexOfTimestamp: Int = getColumnIndexOrThrow(_stmt, "timestamp")
        val _result: MutableList<SearchHistory> = mutableListOf()
        while (_stmt.step()) {
          val _item: SearchHistory
          val _tmpQuery: String
          _tmpQuery = _stmt.getText(_columnIndexOfQuery)
          val _tmpTimestamp: Long
          _tmpTimestamp = _stmt.getLong(_columnIndexOfTimestamp)
          _item = SearchHistory(_tmpQuery,_tmpTimestamp)
          _result.add(_item)
        }
        _result
      } finally {
        _stmt.close()
      }
    }
  }

  public override suspend fun searchHistory(query: String): List<SearchHistory> {
    val _sql: String =
        "SELECT * FROM search_history WHERE query LIKE '%' || ? || '%' ORDER BY timestamp DESC"
    return performSuspending(__db, true, false) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        var _argIndex: Int = 1
        _stmt.bindText(_argIndex, query)
        val _columnIndexOfQuery: Int = getColumnIndexOrThrow(_stmt, "query")
        val _columnIndexOfTimestamp: Int = getColumnIndexOrThrow(_stmt, "timestamp")
        val _result: MutableList<SearchHistory> = mutableListOf()
        while (_stmt.step()) {
          val _item: SearchHistory
          val _tmpQuery: String
          _tmpQuery = _stmt.getText(_columnIndexOfQuery)
          val _tmpTimestamp: Long
          _tmpTimestamp = _stmt.getLong(_columnIndexOfTimestamp)
          _item = SearchHistory(_tmpQuery,_tmpTimestamp)
          _result.add(_item)
        }
        _result
      } finally {
        _stmt.close()
      }
    }
  }

  public override suspend fun getHistoryCount(): Int {
    val _sql: String = "SELECT COUNT(*) FROM search_history"
    return performSuspending(__db, true, false) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        val _result: Int
        if (_stmt.step()) {
          val _tmp: Int
          _tmp = _stmt.getLong(0).toInt()
          _result = _tmp
        } else {
          _result = 0
        }
        _result
      } finally {
        _stmt.close()
      }
    }
  }

  public override suspend fun clearAllHistory() {
    val _sql: String = "DELETE FROM search_history"
    return performSuspending(__db, false, true) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        _stmt.step()
      } finally {
        _stmt.close()
      }
    }
  }

  public override suspend fun deleteOldHistory(timestamp: Long) {
    val _sql: String = "DELETE FROM search_history WHERE timestamp < ?"
    return performSuspending(__db, false, true) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        var _argIndex: Int = 1
        _stmt.bindLong(_argIndex, timestamp)
        _stmt.step()
      } finally {
        _stmt.close()
      }
    }
  }

  public override suspend fun deleteOldestEntries(count: Int) {
    val _sql: String =
        "DELETE FROM search_history WHERE query IN (SELECT query FROM search_history ORDER BY timestamp ASC LIMIT ?)"
    return performSuspending(__db, false, true) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        var _argIndex: Int = 1
        _stmt.bindLong(_argIndex, count.toLong())
        _stmt.step()
      } finally {
        _stmt.close()
      }
    }
  }

  public companion object {
    public fun getRequiredConverters(): List<KClass<*>> = emptyList()
  }
}
