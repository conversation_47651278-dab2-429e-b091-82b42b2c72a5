{"logs": [{"outputFile": "com.libretv.android.app-mergeDebugResources-2:/values-ne/values-ne.xml", "map": [{"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\3bf0e9913a8fb17f961a2eb6334c8d97\\transformed\\material-release\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "87", "endOffsets": "138"}, "to": {"startLines": "159", "startColumns": "4", "startOffsets": "14113", "endColumns": "87", "endOffsets": "14196"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\0b493fc5385f598ec7f47c0a10bc88bb\\transformed\\material3-release\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,184,303,419,547,646,741,853,1005,1126,1279,1363,1471,1569,1668,1780,1904,2017,2163,2306,2440,2605,2735,2887,3044,3173,3272,3367,3483,3607,3711,3830,3940,4086,4234,4344,4452,4527,4632,4737,4848,4939,5034,5141,5221,5306,5407,5516,5611,5714,5801,5912,6011,6116,6239,6319,6425", "endColumns": "128,118,115,127,98,94,111,151,120,152,83,107,97,98,111,123,112,145,142,133,164,129,151,156,128,98,94,115,123,103,118,109,145,147,109,107,74,104,104,110,90,94,106,79,84,100,108,94,102,86,110,98,104,122,79,105,93", "endOffsets": "179,298,414,542,641,736,848,1000,1121,1274,1358,1466,1564,1663,1775,1899,2012,2158,2301,2435,2600,2730,2882,3039,3168,3267,3362,3478,3602,3706,3825,3935,4081,4229,4339,4447,4522,4627,4732,4843,4934,5029,5136,5216,5301,5402,5511,5606,5709,5796,5907,6006,6111,6234,6314,6420,6514"}, "to": {"startLines": "102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7649,7778,7897,8013,8141,8240,8335,8447,8599,8720,8873,8957,9065,9163,9262,9374,9498,9611,9757,9900,10034,10199,10329,10481,10638,10767,10866,10961,11077,11201,11305,11424,11534,11680,11828,11938,12046,12121,12226,12331,12442,12533,12628,12735,12815,12900,13001,13110,13205,13308,13395,13506,13605,13710,13833,13913,14019", "endColumns": "128,118,115,127,98,94,111,151,120,152,83,107,97,98,111,123,112,145,142,133,164,129,151,156,128,98,94,115,123,103,118,109,145,147,109,107,74,104,104,110,90,94,106,79,84,100,108,94,102,86,110,98,104,122,79,105,93", "endOffsets": "7773,7892,8008,8136,8235,8330,8442,8594,8715,8868,8952,9060,9158,9257,9369,9493,9606,9752,9895,10029,10194,10324,10476,10633,10762,10861,10956,11072,11196,11300,11419,11529,11675,11823,11933,12041,12116,12221,12326,12437,12528,12623,12730,12810,12895,12996,13105,13200,13303,13390,13501,13600,13705,13828,13908,14014,14108"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\406d1819cde1682167f3af9259cf68c0\\transformed\\ui-release\\res\\values-ne\\values-ne.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "179,278,368,462,559,645,727,823,910,996,1086,1179,1256,1340,1415,1488,1560,1641,1709", "endColumns": "98,89,93,96,85,81,95,86,85,89,92,76,83,74,72,71,80,67,119", "endOffsets": "273,363,457,554,640,722,818,905,991,1081,1174,1251,1335,1410,1483,1555,1636,1704,1824"}, "to": {"startLines": "27,28,29,31,32,100,101,166,167,168,169,170,171,172,173,174,176,177,178", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1574,1673,1763,1938,2035,7471,7553,14707,14794,14880,14970,15063,15140,15224,15299,15372,15545,15626,15694", "endColumns": "98,89,93,96,85,81,95,86,85,89,92,76,83,74,72,71,80,67,119", "endOffsets": "1668,1758,1852,2030,2116,7548,7644,14789,14875,14965,15058,15135,15219,15294,15367,15439,15621,15689,15809"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\26497276d619c831fa389bb2f1588ec8\\transformed\\media3-exoplayer-1.7.1\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,197,267,334,412,489,589,683", "endColumns": "70,70,69,66,77,76,99,93,68", "endOffsets": "121,192,262,329,407,484,584,678,747"}, "to": {"startLines": "74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "5719,5790,5861,5931,5998,6076,6153,6253,6347", "endColumns": "70,70,69,66,77,76,99,93,68", "endOffsets": "5785,5856,5926,5993,6071,6148,6248,6342,6411"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\2425a4573e4fb65053b44e46c3806d87\\transformed\\foundation-release\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,128,213", "endColumns": "72,84,90", "endOffsets": "123,208,299"}, "to": {"startLines": "19,179,180", "startColumns": "4,4,4", "startOffsets": "781,15814,15899", "endColumns": "72,84,90", "endOffsets": "849,15894,15985"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\6b27351f1a96970e2ba142187de324d4\\transformed\\media3-ui-1.7.1\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,310,529,731,824,917,1007,1108,1211,1297,1361,1458,1555,1627,1700,1760,1830,1947,2061,2181,2260,2352,2420,2506,2592,2677,2746,2809,2862,2920,2968,3029,3091,3162,3224,3286,3345,3412,3478,3532,3594,3670,3746,3799", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,92,92,89,100,102,85,63,96,96,71,72,59,69,116,113,119,78,91,67,85,85,84,68,62,52,57,47,60,61,70,61,61,58,66,65,53,61,75,75,52,64", "endOffsets": "305,524,726,819,912,1002,1103,1206,1292,1356,1453,1550,1622,1695,1755,1825,1942,2056,2176,2255,2347,2415,2501,2587,2672,2741,2804,2857,2915,2963,3024,3086,3157,3219,3281,3340,3407,3473,3527,3589,3665,3741,3794,3859"}, "to": {"startLines": "2,11,15,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,360,579,3641,3734,3827,3917,4018,4121,4207,4271,4368,4465,4537,4610,4670,4740,4857,4971,5091,5170,5262,5330,5416,5502,5587,5656,6416,6469,6527,6575,6636,6698,6769,6831,6893,6952,7019,7085,7139,7201,7277,7353,7406", "endLines": "10,14,18,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99", "endColumns": "17,12,12,92,92,89,100,102,85,63,96,96,71,72,59,69,116,113,119,78,91,67,85,85,84,68,62,52,57,47,60,61,70,61,61,58,66,65,53,61,75,75,52,64", "endOffsets": "355,574,776,3729,3822,3912,4013,4116,4202,4266,4363,4460,4532,4605,4665,4735,4852,4966,5086,5165,5257,5325,5411,5497,5582,5651,5714,6464,6522,6570,6631,6693,6764,6826,6888,6947,7014,7080,7134,7196,7272,7348,7401,7466"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\4c56c0fb8a9fdb1c153785f7a46f8077\\transformed\\media3-session-1.7.1\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,136,237,307,404,498,582,664,754,857,946,1020,1121,1208,1305,1378,1475,1556,1656,1731,1807,1888,1972,2064", "endColumns": "80,100,69,96,93,83,81,89,102,88,73,100,86,96,72,96,80,99,74,75,80,83,91,97", "endOffsets": "131,232,302,399,493,577,659,749,852,941,1015,1116,1203,1300,1373,1470,1551,1651,1726,1802,1883,1967,2059,2157"}, "to": {"startLines": "30,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,160,161,162,163,164,165", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1857,2121,2222,2292,2389,2483,2567,2649,2739,2842,2931,3005,3106,3193,3290,3363,3460,3541,14201,14276,14352,14433,14517,14609", "endColumns": "80,100,69,96,93,83,81,89,102,88,73,100,86,96,72,96,80,99,74,75,80,83,91,97", "endOffsets": "1933,2217,2287,2384,2478,2562,2644,2734,2837,2926,3000,3101,3188,3285,3358,3455,3536,3636,14271,14347,14428,14512,14604,14702"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\955c05c442e591726e4833ad1e0fc303\\transformed\\core-1.16.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,261,363,469,567,667,775", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "153,256,358,464,562,662,770,871"}, "to": {"startLines": "20,21,22,23,24,25,26,175", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "854,957,1060,1162,1268,1366,1466,15444", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "952,1055,1157,1263,1361,1461,1569,15540"}}]}]}