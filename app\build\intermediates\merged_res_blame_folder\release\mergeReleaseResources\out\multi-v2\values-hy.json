{"logs": [{"outputFile": "com.libretv.android.app-mergeReleaseResources-2:/values-hy/values-hy.xml", "map": [{"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\26497276d619c831fa389bb2f1588ec8\\transformed\\media3-exoplayer-1.7.1\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,190,254,322,403,480,554,631", "endColumns": "71,62,63,67,80,76,73,76,77", "endOffsets": "122,185,249,317,398,475,549,626,704"}, "to": {"startLines": "74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "5648,5720,5783,5847,5915,5996,6073,6147,6224", "endColumns": "71,62,63,67,80,76,73,76,77", "endOffsets": "5715,5778,5842,5910,5991,6068,6142,6219,6297"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\406d1819cde1682167f3af9259cf68c0\\transformed\\ui-release\\res\\values-hy\\values-hy.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "185,284,366,460,560,643,725,811,906,988,1073,1161,1235,1323,1400,1479,1556,1637,1706", "endColumns": "98,81,93,99,82,81,85,94,81,84,87,73,87,76,78,76,80,68,117", "endOffsets": "279,361,455,555,638,720,806,901,983,1068,1156,1230,1318,1395,1474,1551,1632,1701,1819"}, "to": {"startLines": "27,28,29,31,32,100,101,165,166,167,168,169,170,171,172,173,175,176,177", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1508,1607,1689,1864,1964,7373,7455,14315,14410,14492,14577,14665,14739,14827,14904,14983,15161,15242,15311", "endColumns": "98,81,93,99,82,81,85,94,81,84,87,73,87,76,78,76,80,68,117", "endOffsets": "1602,1684,1778,1959,2042,7450,7536,14405,14487,14572,14660,14734,14822,14899,14978,15055,15237,15306,15424"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\4c56c0fb8a9fdb1c153785f7a46f8077\\transformed\\media3-session-1.7.1\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,136,244,320,436,539,628,706,790,880,978,1046,1161,1260,1362,1439,1533,1615,1717,1790,1861,1936,2015,2105", "endColumns": "80,107,75,115,102,88,77,83,89,97,67,114,98,101,76,93,81,101,72,70,74,78,89,93", "endOffsets": "131,239,315,431,534,623,701,785,875,973,1041,1156,1255,1357,1434,1528,1610,1712,1785,1856,1931,2010,2100,2194"}, "to": {"startLines": "30,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,159,160,161,162,163,164", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1783,2047,2155,2231,2347,2450,2539,2617,2701,2791,2889,2957,3072,3171,3273,3350,3444,3526,13833,13906,13977,14052,14131,14221", "endColumns": "80,107,75,115,102,88,77,83,89,97,67,114,98,101,76,93,81,101,72,70,74,78,89,93", "endOffsets": "1859,2150,2226,2342,2445,2534,2612,2696,2786,2884,2952,3067,3166,3268,3345,3439,3521,3623,13901,13972,14047,14126,14216,14310"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\0b493fc5385f598ec7f47c0a10bc88bb\\transformed\\material3-release\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,286,401,517,616,718,837,983,1106,1262,1349,1447,1542,1641,1763,1885,1988,2128,2266,2399,2576,2705,2821,2940,3063,3159,3257,3380,3521,3627,3732,3840,3979,4123,4232,4334,4425,4520,4616,4723,4811,4896,5010,5090,5173,5272,5373,5464,5560,5649,5753,5851,5951,6068,6148,6253", "endColumns": "115,114,114,115,98,101,118,145,122,155,86,97,94,98,121,121,102,139,137,132,176,128,115,118,122,95,97,122,140,105,104,107,138,143,108,101,90,94,95,106,87,84,113,79,82,98,100,90,95,88,103,97,99,116,79,104,93", "endOffsets": "166,281,396,512,611,713,832,978,1101,1257,1344,1442,1537,1636,1758,1880,1983,2123,2261,2394,2571,2700,2816,2935,3058,3154,3252,3375,3516,3622,3727,3835,3974,4118,4227,4329,4420,4515,4611,4718,4806,4891,5005,5085,5168,5267,5368,5459,5555,5644,5748,5846,5946,6063,6143,6248,6342"}, "to": {"startLines": "102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7541,7657,7772,7887,8003,8102,8204,8323,8469,8592,8748,8835,8933,9028,9127,9249,9371,9474,9614,9752,9885,10062,10191,10307,10426,10549,10645,10743,10866,11007,11113,11218,11326,11465,11609,11718,11820,11911,12006,12102,12209,12297,12382,12496,12576,12659,12758,12859,12950,13046,13135,13239,13337,13437,13554,13634,13739", "endColumns": "115,114,114,115,98,101,118,145,122,155,86,97,94,98,121,121,102,139,137,132,176,128,115,118,122,95,97,122,140,105,104,107,138,143,108,101,90,94,95,106,87,84,113,79,82,98,100,90,95,88,103,97,99,116,79,104,93", "endOffsets": "7652,7767,7882,7998,8097,8199,8318,8464,8587,8743,8830,8928,9023,9122,9244,9366,9469,9609,9747,9880,10057,10186,10302,10421,10544,10640,10738,10861,11002,11108,11213,11321,11460,11604,11713,11815,11906,12001,12097,12204,12292,12377,12491,12571,12654,12753,12854,12945,13041,13130,13234,13332,13432,13549,13629,13734,13828"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\6b27351f1a96970e2ba142187de324d4\\transformed\\media3-ui-1.7.1\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,288,477,659,742,824,894,985,1081,1157,1220,1321,1424,1494,1562,1630,1696,1818,1934,2054,2118,2199,2276,2354,2450,2545,2614,2679,2732,2792,2840,2901,2969,3037,3110,3177,3238,3299,3366,3418,3480,3556,3632,3685", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,82,81,69,90,95,75,62,100,102,69,67,67,65,121,115,119,63,80,76,77,95,94,68,64,52,59,47,60,67,67,72,66,60,60,66,51,61,75,75,52,64", "endOffsets": "283,472,654,737,819,889,980,1076,1152,1215,1316,1419,1489,1557,1625,1691,1813,1929,2049,2113,2194,2271,2349,2445,2540,2609,2674,2727,2787,2835,2896,2964,3032,3105,3172,3233,3294,3361,3413,3475,3551,3627,3680,3745"}, "to": {"startLines": "2,11,15,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,338,527,3628,3711,3793,3863,3954,4050,4126,4189,4290,4393,4463,4531,4599,4665,4787,4903,5023,5087,5168,5245,5323,5419,5514,5583,6302,6355,6415,6463,6524,6592,6660,6733,6800,6861,6922,6989,7041,7103,7179,7255,7308", "endLines": "10,14,18,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99", "endColumns": "17,12,12,82,81,69,90,95,75,62,100,102,69,67,67,65,121,115,119,63,80,76,77,95,94,68,64,52,59,47,60,67,67,72,66,60,60,66,51,61,75,75,52,64", "endOffsets": "333,522,704,3706,3788,3858,3949,4045,4121,4184,4285,4388,4458,4526,4594,4660,4782,4898,5018,5082,5163,5240,5318,5414,5509,5578,5643,6350,6410,6458,6519,6587,6655,6728,6795,6856,6917,6984,7036,7098,7174,7250,7303,7368"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\2425a4573e4fb65053b44e46c3806d87\\transformed\\foundation-release\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,134,219", "endColumns": "78,84,88", "endOffsets": "129,214,303"}, "to": {"startLines": "19,178,179", "startColumns": "4,4,4", "startOffsets": "709,15429,15514", "endColumns": "78,84,88", "endOffsets": "783,15509,15598"}}, {"source": "D:\\SDK\\.gradle\\caches\\8.11.1\\transforms\\955c05c442e591726e4833ad1e0fc303\\transformed\\core-1.16.0\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,260,358,457,562,664,775", "endColumns": "99,104,97,98,104,101,110,100", "endOffsets": "150,255,353,452,557,659,770,871"}, "to": {"startLines": "20,21,22,23,24,25,26,174", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "788,888,993,1091,1190,1295,1397,15060", "endColumns": "99,104,97,98,104,101,110,100", "endOffsets": "883,988,1086,1185,1290,1392,1503,15156"}}]}]}