3com.libretv.android.data.database.ApiSourceDao_Impl6com.libretv.android.data.database.LibreTVDatabase_Impl7com.libretv.android.data.database.SearchHistoryDao_Impl/com.libretv.android.data.database.VideoDao_Impl6com.libretv.android.data.database.WatchHistoryDao_Impl-com.libretv.android.presentation.MainActivity&com.libretv.android.LibreTVApplication1com.libretv.android.data.database.LibreTVDatabase4com.libretv.android.data.model.VideoInfo.$serializer6com.libretv.android.data.model.ApiResponse.$serializer:com.libretv.android.data.model.VideoDetailInfo.$serializer5com.libretv.android.data.network.UserAgentInterceptor9com.libretv.android.data.network.ErrorHandlingInterceptor"com.libretv.android.di.ApiRetrofit'com.libretv.android.di.InternalRetrofit%com.libretv.android.di.DoubanRetrofit7com.libretv.android.presentation.navigation.Screen.Home9com.libretv.android.presentation.navigation.Screen.Search:com.libretv.android.presentation.navigation.Screen.History;com.libretv.android.presentation.navigation.Screen.SettingsAcom.libretv.android.presentation.screens.history.HistoryViewModel;com.libretv.android.presentation.screens.home.HomeViewModel?com.libretv.android.presentation.screens.search.SearchViewModelCcom.libretv.android.presentation.screens.settings.SettingsViewModel                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            