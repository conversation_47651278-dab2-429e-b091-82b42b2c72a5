package com.libretv.android.data.repository

import com.libretv.android.data.database.WatchHistoryDao
import com.libretv.android.data.model.WatchHistory
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 观看历史数据仓库
 */
@Singleton
class WatchHistoryRepository @Inject constructor(
    private val watchHistoryDao: WatchHistoryDao
) {
    
    /**
     * 获取所有观看历史
     */
    fun getAllWatchHistory(): Flow<List<WatchHistory>> {
        return watchHistoryDao.getAllWatchHistory()
    }
    
    /**
     * 获取最近观看历史
     */
    fun getRecentWatchHistory(limit: Int = 20): Flow<List<WatchHistory>> {
        return watchHistoryDao.getRecentWatchHistory(limit)
    }
    
    /**
     * 获取特定视频的观看历史
     */
    suspend fun getWatchHistory(videoId: String, sourceCode: String): WatchHistory? {
        return watchHistoryDao.getWatchHistory(videoId, sourceCode)
    }
    
    /**
     * 添加或更新观看历史
     */
    suspend fun addOrUpdateWatchHistory(
        videoId: String,
        videoTitle: String,
        videoCover: String?,
        sourceCode: String,
        sourceName: String,
        episodeIndex: Int = 0,
        episodeTitle: String? = null,
        watchProgress: Long = 0L,
        totalDuration: Long = 0L,
        episodes: List<String> = emptyList()
    ) {
        val id = "${videoId}_${sourceCode}"
        val existingHistory = watchHistoryDao.getWatchHistoryById(id)
        
        if (existingHistory != null) {
            // 更新现有记录
            val updatedHistory = existingHistory.copy(
                episodeIndex = episodeIndex,
                episodeTitle = episodeTitle,
                watchProgress = watchProgress,
                totalDuration = totalDuration,
                lastWatchTime = System.currentTimeMillis(),
                episodes = episodes.ifEmpty { existingHistory.episodes }
            )
            watchHistoryDao.updateWatchHistory(updatedHistory)
        } else {
            // 创建新记录
            val newHistory = WatchHistory(
                id = id,
                videoId = videoId,
                videoTitle = videoTitle,
                videoCover = videoCover,
                sourceCode = sourceCode,
                sourceName = sourceName,
                episodeIndex = episodeIndex,
                episodeTitle = episodeTitle,
                watchProgress = watchProgress,
                totalDuration = totalDuration,
                lastWatchTime = System.currentTimeMillis(),
                episodes = episodes
            )
            watchHistoryDao.insertWatchHistory(newHistory)
        }
    }
    
    /**
     * 更新观看进度
     */
    suspend fun updateWatchProgress(
        videoId: String,
        sourceCode: String,
        progress: Long
    ) {
        val id = "${videoId}_${sourceCode}"
        watchHistoryDao.updateProgress(id, progress, System.currentTimeMillis())
    }
    
    /**
     * 更新当前播放集数
     */
    suspend fun updateCurrentEpisode(
        videoId: String,
        sourceCode: String,
        episodeIndex: Int,
        episodeTitle: String?
    ) {
        val id = "${videoId}_${sourceCode}"
        watchHistoryDao.updateEpisode(id, episodeIndex, episodeTitle, System.currentTimeMillis())
    }
    
    /**
     * 删除观看历史
     */
    suspend fun deleteWatchHistory(history: WatchHistory) {
        watchHistoryDao.deleteWatchHistory(history)
    }
    
    /**
     * 清空所有观看历史
     */
    suspend fun clearAllHistory() {
        watchHistoryDao.clearAllHistory()
    }
    
    /**
     * 清理旧的观看历史
     */
    suspend fun cleanOldHistory(daysAgo: Int = 90) {
        val timestamp = System.currentTimeMillis() - (daysAgo * 24 * 60 * 60 * 1000L)
        watchHistoryDao.deleteOldHistory(timestamp)
    }
}
