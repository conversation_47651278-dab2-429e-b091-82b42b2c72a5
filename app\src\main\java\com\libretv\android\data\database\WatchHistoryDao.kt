package com.libretv.android.data.database

import androidx.room.*
import com.libretv.android.data.model.WatchHistory
import kotlinx.coroutines.flow.Flow

/**
 * 观看历史数据访问对象
 */
@Dao
interface WatchHistoryDao {
    
    @Query("SELECT * FROM watch_history ORDER BY lastWatchTime DESC")
    fun getAllWatchHistory(): Flow<List<WatchHistory>>
    
    @Query("SELECT * FROM watch_history ORDER BY lastWatchTime DESC LIMIT :limit")
    fun getRecentWatchHistory(limit: Int = 20): Flow<List<WatchHistory>>
    
    @Query("SELECT * FROM watch_history WHERE id = :id")
    suspend fun getWatchHistoryById(id: String): WatchHistory?
    
    @Query("SELECT * FROM watch_history WHERE videoId = :videoId AND sourceCode = :sourceCode")
    suspend fun getWatchHistory(videoId: String, sourceCode: String): WatchHistory?
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertWatchHistory(history: WatchHistory)
    
    @Update
    suspend fun updateWatchHistory(history: WatchHistory)
    
    @Delete
    suspend fun deleteWatchHistory(history: WatchHistory)
    
    @Query("DELETE FROM watch_history")
    suspend fun clearAllHistory()
    
    @Query("DELETE FROM watch_history WHERE lastWatchTime < :timestamp")
    suspend fun deleteOldHistory(timestamp: Long)
    
    @Query("UPDATE watch_history SET watchProgress = :progress, lastWatchTime = :timestamp WHERE id = :id")
    suspend fun updateProgress(id: String, progress: Long, timestamp: Long)
    
    @Query("UPDATE watch_history SET episodeIndex = :episodeIndex, episodeTitle = :episodeTitle, lastWatchTime = :timestamp WHERE id = :id")
    suspend fun updateEpisode(id: String, episodeIndex: Int, episodeTitle: String?, timestamp: Long)
}
