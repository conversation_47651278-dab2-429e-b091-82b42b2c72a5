package com.libretv.android.data.database

import androidx.room.*
import com.libretv.android.data.model.SearchHistory
import kotlinx.coroutines.flow.Flow

/**
 * 搜索历史数据访问对象
 */
@Dao
interface SearchHistoryDao {
    
    @Query("SELECT * FROM search_history ORDER BY timestamp DESC LIMIT :limit")
    fun getRecentSearches(limit: Int = 10): Flow<List<SearchHistory>>
    
    @Query("SELECT * FROM search_history WHERE query LIKE '%' || :query || '%' ORDER BY timestamp DESC")
    suspend fun searchHistory(query: String): List<SearchHistory>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSearch(search: SearchHistory)
    
    @Delete
    suspend fun deleteSearch(search: SearchHistory)
    
    @Query("DELETE FROM search_history")
    suspend fun clearAllHistory()
    
    @Query("DELETE FROM search_history WHERE timestamp < :timestamp")
    suspend fun deleteOldHistory(timestamp: Long)
    
    @Query("SELECT COUNT(*) FROM search_history")
    suspend fun getHistoryCount(): Int
    
    @Query("DELETE FROM search_history WHERE query IN (SELECT query FROM search_history ORDER BY timestamp ASC LIMIT :count)")
    suspend fun deleteOldestEntries(count: Int)
}
